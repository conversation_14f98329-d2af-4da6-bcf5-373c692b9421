#!/usr/bin/env python3
"""
Comprehensive dependency verification script for showback + VMDB integration.
Checks if all required modules are installed and properly configured.

Usage:
    python verify_dependencies.py
"""

import sys
import importlib
import subprocess
import os

def check_module_import(module_name, package_name=None):
    """Check if a module can be imported and get its version if available"""
    try:
        module = importlib.import_module(module_name)

        # Try to get version
        version = "unknown"
        for attr in ['__version__', 'version', 'VERSION']:
            if hasattr(module, attr):
                version = getattr(module, attr)
                break

        return True, version
    except ImportError as e:
        return False, str(e)

def check_pip_package(package_name):
    """Check if package is installed via pip"""
    try:
        result = subprocess.run(['pip', 'show', package_name],
                              capture_output=True, text=True, check=False)
        if result.returncode == 0:
            # Extract version from pip show output
            for line in result.stdout.split('\n'):
                if line.startswith('Version:'):
                    return True, line.split(':', 1)[1].strip()
            return True, "unknown"
        else:
            return False, "not installed"
    except Exception as e:
        return False, str(e)

def verify_dependencies():
    """Verify all required dependencies for showback + VMDB integration"""

    print("🔍 Verifying Dependencies for Showback + VMDB Integration")
    print("=" * 70)

    # Required dependencies for showback + VMDB
    required_deps = [
        # Core dependencies
        ("requests", "requests"),
        ("pymongo", "pymongo"),
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),

        # Authentication & Security
        ("jwt", "PyJWT"),
        ("cryptography", "cryptography"),
        ("hvac", "hvac"),
        ("jwcrypto", "jwcrypto"),

        # VMDB specific
        ("snappy", "python-snappy"),
        ("google.protobuf", "protobuf"),

        # Showback specific
        ("ksuid", "ksuid"),

        # Utilities
        ("yaml", "PyYAML"),
        ("slugify", "python-slugify"),
        ("filelock", "filelock"),
        ("dns", "dnspython"),
        ("dotenv", "python-dotenv"),

        # Logging & Monitoring
        ("pythonjsonlogger", "python-json-logger"),
        ("logstash_async", "python-logstash-async"),
        ("prometheus_fastapi_instrumentator", "prometheus-fastapi-instrumentator"),

        # HTTP & Networking
        ("gql", "gql"),
        ("requests_toolbelt", "requests-toolbelt"),
        ("urllib3", "urllib3"),
    ]

    print("\n1. Checking Module Imports:")
    print("-" * 40)

    all_imports_ok = True
    for module_name, package_name in required_deps:
        success, version = check_module_import(module_name)
        if success:
            print(f"   ✅ {module_name:<25} (v{version})")
        else:
            print(f"   ❌ {module_name:<25} - {version}")
            all_imports_ok = False

    print(f"\n2. Checking pip Package Installation:")
    print("-" * 40)

    # Key packages to verify via pip
    key_packages = [
        "python-snappy",
        "protobuf",
        "ksuid",
        "requests",
        "pymongo",
        "fastapi"
    ]

    all_packages_ok = True
    for package in key_packages:
        success, version = check_pip_package(package)
        if success:
            print(f"   ✅ {package:<20} (v{version})")
        else:
            print(f"   ❌ {package:<20} - {version}")
            all_packages_ok = False

    print(f"\n3. Checking Configuration Files:")
    print("-" * 40)

    config_files = [
        ("pyproject.toml", "Poetry configuration"),
        ("requirements.txt", "Pip requirements"),
        (".env", "Environment variables")
    ]

    config_ok = True
    for filename, description in config_files:
        if os.path.exists(filename):
            print(f"   ✅ {filename:<20} - {description}")
        else:
            print(f"   ❌ {filename:<20} - Missing {description}")
            config_ok = False

    # Check specific dependencies in files
    print(f"\n4. Checking Specific Dependencies in Files:")
    print("-" * 40)

    # Check pyproject.toml
    try:
        with open("pyproject.toml", "r") as f:
            pyproject_content = f.read()

        required_in_pyproject = ["ksuid", "python-snappy", "protobuf"]
        for dep in required_in_pyproject:
            if dep in pyproject_content:
                print(f"   ✅ {dep:<20} - Found in pyproject.toml")
            else:
                print(f"   ❌ {dep:<20} - Missing from pyproject.toml")
                config_ok = False
    except Exception as e:
        print(f"   ❌ pyproject.toml    - Error reading: {e}")
        config_ok = False

    # Check requirements.txt
    try:
        with open("requirements.txt", "r") as f:
            requirements_content = f.read()

        required_in_requirements = ["ksuid==", "python-snappy==", "protobuf=="]
        for dep in required_in_requirements:
            if dep in requirements_content:
                print(f"   ✅ {dep.rstrip('='):<20} - Found in requirements.txt")
            else:
                print(f"   ❌ {dep.rstrip('='):<20} - Missing from requirements.txt")
                config_ok = False
    except Exception as e:
        print(f"   ❌ requirements.txt  - Error reading: {e}")
        config_ok = False

    print("\n" + "=" * 70)

    # Overall status
    if all_imports_ok and all_packages_ok and config_ok:
        print("🎉 ALL DEPENDENCIES VERIFIED SUCCESSFULLY!")
        print("✅ All required modules can be imported")
        print("✅ All key packages are installed")
        print("✅ All configuration files are present")
        print("✅ Ready for showback + VMDB integration")
        return True
    else:
        print("❌ DEPENDENCY VERIFICATION FAILED!")
        if not all_imports_ok:
            print("❌ Some modules cannot be imported")
        if not all_packages_ok:
            print("❌ Some packages are not installed")
        if not config_ok:
            print("❌ Configuration files have issues")

        print("\n🔧 To fix missing dependencies:")
        print("   poetry install")
        print("   # OR")
        print("   pip install -r requirements.txt")

        return False

if __name__ == "__main__":
    success = verify_dependencies()
    sys.exit(0 if success else 1)
