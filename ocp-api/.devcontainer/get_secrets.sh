#!/bin/bash

set -e

# Script must be sourced to export variables to current shell context

if [[ $OCPENV != 'rdei' && $OCPENV != 'docker' ]]; then
  # echo "OCPENV neither 'rdei' nor 'docker'"
  set -a
  source .env
  set +a 
  secrets=`poetry run python -c 'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)'`
else
  secrets=`python -c 'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)'`
fi

# echo $secrets
# echo $secrets | tr '|' '\n' | awk '{print "export " $0}'
echo $secrets | tr '|' '\n' | xargs -I {} bash -c 'export {}; echo {}'
