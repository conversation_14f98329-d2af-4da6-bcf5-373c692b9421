# FROM python:3.9.16-alpine3.17
FROM python:3.11.8-bookworm

WORKDIR /app
COPY . .

# COPY docker-init.sh /docker-init.sh
# COPY docker-init-secrets.sh /docker-init-secrets.sh
RUN chmod u+x docker-init*.sh

RUN set -eux; \
    apt-get --allow-insecure-repositories update; \
    apt-get install -y --no-install-recommends \
    musl-dev \
    ansible \
    msmtp; \
    rm -rf /var/lib/apt/lists/*

RUN pip install --upgrade pip \
    && pip install -r /app/requirements.txt

ARG CP_VAULT_PASS

EXPOSE 443
