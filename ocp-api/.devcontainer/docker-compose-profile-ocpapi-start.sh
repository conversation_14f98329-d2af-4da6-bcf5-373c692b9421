
#!/bin/bash

# Get the absolute path of the parent directory
parent_dir="$(dirname "$(dirname "$(readlink -f "$0")")")"

# Change to the parent directory
pushd "$parent_dir"

pushd deployment/ocp-mongo-image && docker build -t hub.comcast.net/devx-run-ocp/db/ocpmongo:0.1.15 .;popd;

# docker pull python:3.11.8-alpine3.19
docker pull python:3.11.8-bookworm

docker compose --profile ocpapi -f docker-compose.yml -f .devcontainer/docker-compose.extend.yml up -d

popd
