#!/bin/bash
set -e

echo;echo "Generating new localhost self-signed cert..."
mkcert localhost 127.0.0.1 ocpmongo ocpapi

# TODO - if desired this block will install new cert into local keychain
#if ! command -v certutil 2>&1 >/dev/null
#then
#    echo "CLI 'certutil' must be installed in order to add new CA root to trust store, see mkcert output.  Once installed use 'mkcert -install' to install new CA root to trust store"
#else
#    mkcert -install
#fi

mkdir -p certs/localhost
mv localhost+3-key.pem certs/localhost/localhost.key
mv localhost+3.pem certs/localhost/localhost.cert
cat certs/localhost/localhost.key > certs/localhost/localhost-certkey.pem
cat certs/localhost/localhost.cert >> certs/localhost/localhost-certkey.pem

cp "`mkcert -CAROOT`"/rootCA.pem certs/localhost/

echo "Localhost cert generated, see mkcert docs for further install instructions."; echo
