[tool.poetry]
name = "ocpapi"
version = "1.13.24"
description = "Observability Control Plane API (OCPAPI)"
authors = ["<PERSON> <<PERSON><PERSON>@cable.comcast.com>",
           "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>@comcast.com>",
           "<PERSON><PERSON> <kost<PERSON><EMAIL>>",
           "<PERSON><PERSON><PERSON> <dixan_mart<PERSON><PERSON>@cable.comcast.com>"]

[tool.poetry.dependencies]
python = ">=3.9.16 <3.10"
uvicorn = {extras = ["standard"], version = "^0.23.2"}
requests = "^2.27.1"
fastapi = "^0.92.0"
PyJWT = {extras = ["crypto"], version = "^2.3.0"}
cryptography = "^39.0.1"
pytest = "7.2.1"
python-logstash-async = "^2.5.0"
pip = "^23.0"
flake8 = "^6.0.0"
pydantic = "^1.10.15"
certifi = "^2023.7.22"
hvac = "^1.1.0"
dnspython = "^2.3.0"
pymongo = "^4.3.3"
jwcrypto = "^1.5.0"
PyYAML = "6.0.1"
prometheus-fastapi-instrumentator = "^6.1.0"
python-json-logger = "^2.0.7"
gql = "^3.5.0"
requests-toolbelt = "^1.0.0"
python-slugify = "^8.0.4"
filelock = "^3.18.0"
python-snappy = "^0.6.1"
protobuf = "^4.21.0"
ksuid = "^1.3"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
