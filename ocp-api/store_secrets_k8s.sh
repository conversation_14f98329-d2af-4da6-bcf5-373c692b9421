#!/bin/bash
set -e

# TODO - why do we expect secrets in Vault to not be required?
#      - all vault secrets should expect to be required
# define vault vars required for API deployment
vault_vars=("oauth_client_id_metrics" "oauth_client_scope_metrics"
"oauth_client_secret_metrics" "ocp_mongo_pass" "ocp_mongo_user"
"oidc_client_audience" "oidc_client_id" "oidc_client_secret"
"oidc_client_tid" "oidc_adauto_client_id" "oidc_adauto_client_secret"
"git_app_id" "git_installation_id" "svc_acct_user" "svc_acct_art_api_key"
"mongodb_user" "mongodb_pass" "oauth_client_id_devhub"
"oauth_client_scope_devhub" "oauth_client_secret_devhub",
"oauth_client_scope_logging")

# define env vars required for API deployment
devenv_vars=("CP_VAULT_PASS" "VAULT_ROLE_ID" "VAULT_SECRET_ID"
"MONGO_INITDB_ROOT_USERNAME" "MONGO_INITDB_ROOT_PASSWORD"
"MONGO_APP_DATABASE" "MONGO_APP_USER" "MONGO_APP_PASS"
"SHOWBACK_API_URL")

# get secrets stored in vault
while read line; do
  if [[ $line == *"="* ]]; then
    #echo "export $(echo $line | cut -d'=' -f1)=$(echo $line | cut -d'=' -f2)"
    export $(echo $line | cut -d'=' -f1)=$(echo $line | cut -d'=' -f2)
  fi
done < .env
secrets=`poetry run python -c 'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)'`

# step through vault secrets for deployment and create k8s secret objects
IFS='|' read -ra s_array <<< $secrets
for i in "${s_array[@]}"
do
  key=$(echo $i | cut -d'=' -f1)
  val=$(echo $i | cut -d'=' -f2)

  if [[ "${vault_vars[*]}" =~ "${key}" ]]; then
    #kubectl create secret generic $(echo ${key} | tr '[:upper:]' '[:lower:]' | tr '_' '-') --from-literal=${key}=${val}
    echo "kubectl create secret generic $(echo ${key} | tr '[:upper:]' '[:lower:]' | tr '_' '-') --from-literal=${key}=${val}"
  fi
done

# step through .env secrets for deploment and create k8s secret objects
for i in "${devenv_vars[@]}"
do
  val=$(printenv | grep ${i}= | cut -d"=" -f2)
  name=$(echo ${i} | tr '[:upper:]' '[:lower:]' | tr '_' '-')
  echo "kubectl create secret generic ${name} --from-literal=${i}=${val}"
done

echo "kubectl create secret generic ocpenv --from-literal=PLATFORMENV=rdei"
