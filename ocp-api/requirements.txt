anyio==4.7.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
attrs==24.2.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
backoff==2.2.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
certifi==2023.11.17 ; python_full_version >= "3.9.16" and python_version < "3.10"
cffi==1.17.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
charset-normalizer==3.4.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
click==8.1.7 ; python_full_version >= "3.9.16" and python_version < "3.10"
colorama==0.4.6 ; python_full_version >= "3.9.16" and python_version < "3.10" and sys_platform == "win32" or python_full_version >= "3.9.16" and python_version < "3.10" and platform_system == "Windows"
cryptography==39.0.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
deprecated==1.2.15 ; python_full_version >= "3.9.16" and python_version < "3.10"
dnspython==2.7.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
exceptiongroup==1.2.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
fastapi==0.92.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
filelock==3.18.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
flake8==6.1.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
gql==3.5.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
graphql-core==3.2.5 ; python_full_version >= "3.9.16" and python_version < "3.10"
h11==0.14.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
httptools==0.6.4 ; python_full_version >= "3.9.16" and python_version < "3.10"
hvac==1.2.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
idna==3.10 ; python_full_version >= "3.9.16" and python_version < "3.10"
iniconfig==2.0.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
jwcrypto==1.5.6 ; python_full_version >= "3.9.16" and python_version < "3.10"
ksuid==1.3 ; python_full_version >= "3.9.16" and python_version < "3.10"
limits==3.14.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
mccabe==0.7.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
multidict==6.1.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
packaging==24.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
pip==23.3.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
pluggy==1.5.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
prometheus-client==0.21.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
prometheus-fastapi-instrumentator==6.1.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
propcache==0.2.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
protobuf==4.25.5 ; python_full_version >= "3.9.16" and python_version < "3.10"
pycodestyle==2.11.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
pycparser==2.22 ; python_full_version >= "3.9.16" and python_version < "3.10"
pydantic==1.10.19 ; python_full_version >= "3.9.16" and python_version < "3.10"
pyflakes==3.1.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
pyhcl==0.4.5 ; python_full_version >= "3.9.16" and python_version < "3.10"
pyjwt[crypto]==2.10.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
pylogbeat==2.0.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
pymongo==4.10.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
pytest==7.2.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
python-dotenv==1.0.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
python-json-logger==2.0.7 ; python_full_version >= "3.9.16" and python_version < "3.10"
python-logstash-async==2.7.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
python-slugify==8.0.4 ; python_full_version >= "3.9.16" and python_version < "3.10"
python-snappy==0.7.3 ; python_full_version >= "3.9.16" and python_version < "3.10"
pyyaml==6.0.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
requests-toolbelt==1.0.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
requests==2.32.3 ; python_full_version >= "3.9.16" and python_version < "3.10"
sniffio==1.3.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
starlette==0.25.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
text-unidecode==1.3 ; python_full_version >= "3.9.16" and python_version < "3.10"
tomli==2.2.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
typing-extensions==4.12.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
urllib3==2.2.3 ; python_full_version >= "3.9.16" and python_version < "3.10"
uvicorn[standard]==0.23.2 ; python_full_version >= "3.9.16" and python_version < "3.10"
uvloop==0.21.0 ; sys_platform != "win32" and sys_platform != "cygwin" and platform_python_implementation != "PyPy" and python_full_version >= "3.9.16" and python_version < "3.10"
watchfiles==1.0.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
websockets==14.1 ; python_full_version >= "3.9.16" and python_version < "3.10"
wrapt==1.17.0 ; python_full_version >= "3.9.16" and python_version < "3.10"
yarl==1.18.3 ; python_full_version >= "3.9.16" and python_version < "3.10"
