# Observability Control Plane API

OCPAPI, the backend for Control Plane, a resource management tool for O11Y service deployments.

## ENDPOINTS
 * Prod
    * UI:  https://ocp.comcast.net/
    * API: https://api.ocp.comcast.net:7443/
 * Test
    * UI:  https://test.ocp.comcast.net/
    * API: https://test.ocp.comcast.net:7443/
 * Dev
    * UI:  https://dev.ocp.comcast.net/
    * API: https://dev.ocp.comcast.net:7443/ 
 
 The root directory for the API, `/`, will return the name of the application and the version.  See `/docs` for Swagger site, `/redoc` for ReDoc site, or `/openapi.json` for the OpenAPI schema.

## Membership Management
Deployed with OCPAPI is the 'member-sync' cron.  This cron runs twice a day at 5am and 5pm.  The cron runs script 'ad2git-sync.py', which steps through all Tenant records, queries AD and Git, compares membership, and updates membership in Git teams according to current membership in AD 'Owners' groups.
 

## INSTALL

### Install mkcert

The mkcert tool is used to generate self-signed SSL certificates for local testing.  These self-signed certs are utilized by Mongo and OCPAPI.  See installation instructions for your OS to install the mkcert CLI tool.  Optionally you can use the mkcert tool to place mkcert's CA file into your system's trust store (prevent invalid cert warnings in browsers).

After installation run script `generate_ssl_cert.sh`, this script will generate a new certificate and place it and the CA cert used by mkcert into a folder read by the compose file to make both available at runtime to the API and Mongo containers.

* [github.com/FiloSottile/mkcert](https://github.com/FiloSottile/mkcert)
* `/generate_ssl_cert.sh`


### Install Poetry

Poetry is a packaging and dependency management tool for Python.  With poetry, you can create virtual environments to host specific binaries and libraries for a project, separate from your local python installation, as well as build and upload distributions.
* [python-poetry.org](https://python-poetry.org)

Running the application locally with Poetry is optional, as Docker is another option for running the project locally, and provides an environment which can also host local copies of external dependencies for local testing.  However, Poetry should be used to update dependencies:
```
poetry add <dependency-name>
```

Install poetry:
* [github.com/python-poetry/poetry](https://github.com/python-poetry/poetry)

Add Artifactory pypi repo (optional) and set config to create virtual environment (`.venv/`) in project directories;
```shell
poetry config repositories.artifactory-pypi https://artifactory.comcast.com/artifactory/pypi-remote/simple
poetry config virtualenvs.create true
poetry config virtualenvs.in-project true
```

Clone repo, tell poetry to use correct version of python for project (3.9), build virtual environment (`.venv/`) and install dependencies;
```shell
<NAME_EMAIL>:comcast-observability/ocp-api.git
cd ocp-api
poetry env use <path-to-python3.9-binary>
poetry install
```

If new dependencies are installed locally via Poetry, prior to building a Docker image, run script, `./poetry2pip_export_requirements.sh`, to populate the requirements file which provides Pip the list of dependencies to install when the image is built.


### Install Docker

See Docker documentation, [Get Docker](https://docs.docker.com/get-docker/), to install Docker.  Note there are new licensing restrictions for Docker Desktop which apply to MacOS and Windows.  If you require Docker Desktop to provide a Linux environment or other Docker features, you must use Colima as a replacement for Docker Desktop, see [github.com/abiosoft/colima](https://github.com/abiosoft/colima).  However, Docker binaries (engine, daemon, cli) are open source, and may be utlized on any OS free of charge.  See this blog for more information, [Looking for a Docker Alternative? Consider This.](https://www.docker.com/blog/looking-for-a-docker-alternative-consider-this/).

* [docs.docker.com/get-docker/](https://docs.docker.com/get-docker/)
* [github.com/abiosoft/colima](https://github.com/abiosoft/colima)
* [www.docker.com/blog/looking-for-a-docker-alternative-consider-this/](https://www.docker.com/blog/looking-for-a-docker-alternative-consider-this/)


## RUN

For local testing the application is hosted on port 7443:
* `https://localhost:7443/docs`

Currently authentication is only available via OIDC (SSO), in order to authenticate to test the API locally, open this URL in a browser:
* `https://localhost:7443/auth/login`

OAuth implementation TBD.

### LOCAL DEVELOPMENT WITH DOCKER

Obtain a copy of `dev.env`, shared via Tube, which should be placed in the ocp-api directory.  This file contains secrets required for the project.  

Cert files are currently encrypted with Ansible Vault, some secrets are stored in SaaS Vault, and some secrets are maintained in the dev.env file.  Eventually most secrets will be stored in SaaS Vault.

To build Docker image, run:
```
docker build -t ocpapi .
```

To spin up container using this image, along with dependencies (mongo, metricsapi), run:
```
docker compose up
```

The `docker-compose.yml` file references the latest version of the ocpapi image, `ocpapi:latest` to create container `ocpapi`, if the image does not yet exist, it will be built.  The compose file also binds the local `./src` directory to the `/app/src` directory in the image, so once the container starts up, changes to any file under src/ will take effect immediately inside the container (the FastAPI app will shutdown and startup again).  If you need to make changes to the `ocpapi.py` file, config files or other files at the root, you must shut down the container (```docker compose down```) and restart again.


### LOCAL DEVELOPMENT WITH POETRY

```shell
poetry run python ocpapi.py
```

Note, in order to run the application directly with Poetry, cert files must first be decrypted.  These files must remain encrypted when building a Docker image to run locally or push to Docker Hub (build script, `build_publish_dev_release.sh` will auto-encrypt certs if left unencrypted).


## PUSH CHANGES

### GITHUB

Development follows feature-branch workflow: 
* (TODO - Link to team documentation)

#### Lint Requirements
Once a PR for a new feature has been approved and merged, flake8 lint will run in CI/CD pipeline, failing lint test will cause deployment to fail.  If lint test fails code must be updated to meet lint requirements.  To run linting locally:
```
poetry run flake8 src/
```
Or use script:
```
poetry run python test_lint_score.py
```

#### Unit Test Requirements
(TODO - configure new action for unit testing on push)

Unit tests are also run in dev CI/CD pipeline, if tests fail code must be updated to allow tests to pass.  To run tests/lints locally, first run:
```
$ docker compose up
```
This will populate the OCP DB volume with accounts, once service complete starting up, exit and run `docker compose down`, then unit tests and lint tests can be run with:
```
$ bash run_unittests_lints.sh
```

### DOCKER HUB

To push a new image to Hub, first update version file appropriately, build image from `main` branch, tag the image, and push using script:
```
. ./build_publish_dev_release.sh
```

Alternatively, run commands directly after updating version (encrypt config/cert files first):
```
docker build -t ocpapi .
docker tag ocpapi:latest hub.comcast.net/o11y_control_plane/api/ocpapi:<version-tag>
docker push hub.comcast.net/o11y_control_plane/api/ocpapi:<version-tag>
```

Version tags follow `major.minor-release` format.  Update version file, `version`, appropriately before pushing a new image.


## ADMIN ACCESS
OCP utilizes a special AD group to provide admin access for the Observability team.  The group used to provide admin access is 'CHQ - RAPTOR', see:
 * [Azure Portal - CHQ RAPTOR](https://portal.azure.com/#view/Microsoft_AAD_IAM/GroupDetailsMenuBlade/~/Overview/groupId/ef68420f-92d7-4837-9ef5-c04d67444523/menuId/)


## OAUTH ACCESS
OCP API provides authentication on most methods via OAuth.  Any client with scope 'ocp:admin' will have access to all calls, other scopes are limites to the routes and access types listed in the scopes, this list currently includes:

* ocp:actions:read
* ocp:actions:run
* ocp:devhub
* ocp:instance:delete
* ocp:instance:provision
* ocp:instance:read
* ocp:membership
* ocp:motd
* ocp:tenant:delete
* ocp:tenant:read
* ocp:tenant:provision
* ocp:tenant:write


## LOGGING
OCP logs live on Lynceous ELK instance 'ocp', under index 'logz-ocp-api'.  Kibana link with preset field filter:

[OCP Kibana](https://federated-us.eaas.comcast.net/s/ocp/app/discover#/?_a=(columns:!(levelname,filename,funcName,lineno,message,tags.environment,tags.region,tags.namespace),filters:!(),grid:(columns:(filename:(width:157),funcName:(width:95),levelname:(width:78),lineno:(width:53),tags.environment:(width:115),tags.namespace:(width:109),tags.region:(width:110))),index:'ocp-*:logz-ocp-api',interval:auto,query:(language:kuery,query:''),sort:!(!('@timestamp',asc)))&_g=(filters:!(),refreshInterval:(pause:!t,value:60000),time:(from:now-30m,to:now)))


## METRICS
The OCP Grafana dashboard includes charts for monitors, API metrics, and the backend, found here:

[OCP Dashboard](https://comcast.grafana.net/d/1QHQvVkIkaz/ocp?orgId=1&refresh=5m&from=now-7d&to=now&var-instance=All&var-db_instance=All)
