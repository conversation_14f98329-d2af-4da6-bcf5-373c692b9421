#!/usr/bin/env python3
"""
Test script to validate VMDB metrics integration without showback client dependencies.
This simulates the metrics collection and reporting that would happen after successful showback processing.

Usage:
    python test_showback_vmdb_only.py
"""

import os
import sys
import logging
import time

# Note: Environment variables should be loaded from .env file automatically
# by your development environment or you can run: export $(cat .env | xargs)

# Add src to path
sys.path.extend("./src")

# Configure simple logging for testing (avoid remote logging errors)
logging.basicConfig(
    level=logging.WARNING,  # Reduce log level to avoid remote logging
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]  # Only console output
)

def test_showback_vmdb_metrics():
    """Test VMDB metrics integration simulating successful showback processing"""
    
    print("🔧 Testing Showback VMDB Metrics Integration")
    print("=" * 60)
    
    # Check environment variables
    print("\n1. Checking VMDB Environment Variables:")
    required_vars = ["OCP_METRICS_INSTANCE_ID", "OCP_METRICS_RW_USER", "OCP_METRICS_RW_PASS"]
    
    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # Mask sensitive values
            if "PASS" in var:
                display_value = "*" * len(value)
            elif "USER" in var:
                display_value = value[:4] + "*" * (len(value) - 4)
            else:
                display_value = value
            print(f"   ✅ {var}: {display_value}")
        else:
            print(f"   ❌ {var}: NOT SET")
            return False
    
    # Test VMDB client initialization
    print("\n2. Testing VMDB Client Initialization:")
    try:
        from src.clients.vmdb import VMClient
        vmdb_client = VMClient()
        print("   ✅ VMDB client initialized successfully")
    except Exception as e:
        print(f"   ❌ VMDB client initialization failed: {e}")
        return False
    
    # Simulate showback metrics data collection
    print("\n3. Simulating Showback Metrics Data Collection:")
    try:
        # Simulate the metrics data that would be collected during showback processing
        simulated_metrics_data = {
            "pillar_count": 1,  # Only metrics pillar is active
            "devhub_id_counts": {
                "metrics": 15,  # Simulated count of DevHub IDs processed
                "visualization": 0  # Not active in current setup
            },
            "processing_success": True
        }
        
        print(f"   ✅ Simulated pillar count: {simulated_metrics_data['pillar_count']}")
        print(f"   ✅ Simulated metrics DevHub IDs: {simulated_metrics_data['devhub_id_counts']['metrics']}")
        print(f"   ✅ Simulated processing success: {simulated_metrics_data['processing_success']}")
        
    except Exception as e:
        print(f"   ❌ Metrics data simulation failed: {e}")
        return False
    
    # Test VMDB metrics reporting (simulating successful showback completion)
    print("\n4. Testing VMDB Metrics Reporting:")
    try:
        # Single timestamp for all metrics (EPOCH seconds)
        timestamp = int(time.time())
        
        # Get environment variables for labels
        deployenv = os.environ.get("DEPLOYENV", "local")
        env = os.environ.get("ENV", "local")
        
        # 1. Report run state (success)
        vmdb_client.remote_write_internal_gauge(
            metric_name="o11y-showback_run_state",
            labels={
                "__name__": "o11y-showback_run_state",
                "status": "success", 
                "env": deployenv,
                "platform": env
            },
            value=1.0,
            timestamp=timestamp
        )
        print("   ✅ Sent run state metric")
        
        # 2. Report pillar count
        vmdb_client.remote_write_internal_gauge(
            metric_name="o11y-showback_pillar_count",
            labels={
                "__name__": "o11y-showback_pillar_count",
                "env": deployenv,
                "platform": env
            },
            value=float(simulated_metrics_data["pillar_count"]),
            timestamp=timestamp
        )
        print("   ✅ Sent pillar count metric")
        
        # 3. Report DevHub ID counts per pillar
        for pillar_name, count in simulated_metrics_data["devhub_id_counts"].items():
            if count > 0:  # Only report pillars with data
                vmdb_client.remote_write_internal_gauge(
                    metric_name="o11y-showback_pillar_devhub-id_count",
                    labels={
                        "__name__": "o11y-showback_pillar_devhub-id_count",
                        "pillar": pillar_name,
                        "env": deployenv,
                        "platform": env
                    },
                    value=float(count),
                    timestamp=timestamp
                )
                print(f"   ✅ Sent DevHub ID count metric for {pillar_name}: {count}")
        
        print(f"   ✅ All metrics sent with timestamp: {timestamp}")
        
    except Exception as e:
        print(f"   ❌ VMDB metrics reporting failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Showback VMDB Metrics Integration Test PASSED!")
    print("✅ VMDB client works correctly")
    print("✅ Metrics collection simulation successful")
    print("✅ All showback metrics sent to Victoria Database")
    print("\nMetrics sent:")
    print(f"  • o11y-showback_run_state = 1.0 (success)")
    print(f"  • o11y-showback_pillar_count = {simulated_metrics_data['pillar_count']}")
    print(f"  • o11y-showback_pillar_devhub-id_count{{pillar='metrics'}} = {simulated_metrics_data['devhub_id_counts']['metrics']}")
    print("\nNext steps:")
    print("1. Deploy showback cron with VMDB environment variables")
    print("2. Monitor Victoria Database for these metrics after showback runs")
    print("3. Set up alerts/dashboards based on these metrics")
    
    return True

if __name__ == "__main__":
    # Load environment variables
    print("Loading environment variables...")
    
    # Check if key variables are loaded
    if not os.environ.get("OCP_METRICS_INSTANCE_ID"):
        print("❌ Environment variables not loaded. Run: export $(cat .env | xargs)")
        sys.exit(1)
    
    success = test_showback_vmdb_metrics()
    sys.exit(0 if success else 1)
