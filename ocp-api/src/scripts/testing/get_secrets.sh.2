#!/bin/bash

set -e

# Script must be sourced to export variables to current shell context

if [ "$OCPENV" != 'rdei' ] && [ "$OCPENV" != 'docker' ]; then
  while IFS= read -r line; do
    if echo "$line" | grep -q "="; then
      export "$(echo "$line" | cut -d'=' -f1)"="$(echo "$line" | cut -d'=' -f2)"
    fi
  done < .env
  secrets=$(poetry run python -c 'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)')
else
  secrets=$(python -c 'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)')
fi

# Convert secrets to an array without using '<<<' and '$(...)' syntax
IFS='|'
set -f  # Disable globbing to avoid issues with special characters
s_array=$secrets  # Split the secrets string into an array based on IFS

for i in $s_array; do
  key=$(echo "$i" | cut -d'=' -f1)
  val=$(echo "$i" | cut -d'=' -f2)
  export ${key}=${val}
  #echo "export ${key}=${val}"
done
