import json
import logging
import os
import smtplib
import sys
import urllib3
from collections import defaultdict
from datetime import datetime, timedelta
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Dict, List, Any, Optional

from bson.objectid import ObjectId

# Disable urllib3 warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Add src to path for imports
sys.path.extend("./src")

# Local imports
from src.clients.mongo import Tenant
from src.clients.pillars import get_instance_details, _get_instances_by_tenant
from src.clients.showback import ShowbackClient
from src.clients.vmdb import VMClient

# Configure logging
for handler in logging.root.handlers[:]:
    logging.root.removeHandler(handler)

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

logging.getLogger("urllib3").setLevel(logging.CRITICAL)


class ShowbackProcessor:
    def __init__(self, start_validity: Optional[str] = None, end_validity: Optional[str] = None):
        self.pillar_usage_map: Dict[str, Dict[str, float]] = defaultdict(
            lambda: defaultdict(float)
        )
        self.metrics_usage_map: Dict[str, Dict[str, Any]] = {}
        self.visualization_usage_map: Dict[str, Dict[str, Any]] = {}
        self.metrics_rate_card_map: Dict[str, Dict[str, Any]] = {}
        self.visualization_rate_card_map: Dict[str, Dict[str, Any]] = {}
        self.showback_client = ShowbackClient()
        self.cache_dir = "./showback_cache"

        # Initialize VMDB client for metrics reporting
        try:
            self.vmdb_client = VMClient()
            logging.info("VMDB client initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize VMDB client (non-blocking): {e}")
            self.vmdb_client = None

        # Initialize metrics tracking
        self.metrics_data = {
            "pillar_count": 0,
            "devhub_id_counts": {},
            "processing_success": False
        }

        self.PILLAR_RESOURCE_MAP = {
            "metrics": "2rdNQs0hbn6vj2BDZQhYqG83yrF",
            "visualization": "2qwbCvT3T0YPggY9bV5rpyMRGBe",
        }

        self.PILLAR_NAME_MAP = {
            "metrics": "metrix",
            "visualization": "visualization",
        }

        self.PILLAR_COST_MAP = {
            "metrics": 0.**********,
            "visualization": 0.**********,
        }

        # New provider ID map
        self.PILLAR_PROVIDER_ID = {
            "metrics": "104409",
            "visualization": "103612",
        }

        self.start_validity = self._validate_or_generate_date(start_validity, offset_minutes=60)
        self.end_validity = self._validate_or_generate_date(
            end_validity, default_to="2025-12-31 23:59"
        )

        # Pre-fetch existing rates for each pillar
        # self.metrics_existing_rates = self._fetch_existing_rates("metrics")
        # self.visualization_existing_rates = self._fetch_existing_rates("visualization")

    def _fetch_existing_rates(self, pillar: str) -> set:
        """
        Fetch existing rates for a specific pillar using its provider ID.
        """
        provider_id = self.PILLAR_PROVIDER_ID.get(pillar)
        if not provider_id:
            logging.error(f"No provider ID found for pillar '{pillar}'.")
            return set()

        try:
            rates = self.showback_client.list_rates(
                pillar=self.PILLAR_NAME_MAP[pillar], providerID=provider_id
            ).get("rates", [])
            return {
                (rate["resourceID"], str(rate["consumerID"]))
                for rate in rates
                if rate["resourceID"] == self.PILLAR_RESOURCE_MAP[pillar]
            }
        except Exception as e:
            logging.error(f"Error fetching rates for pillar '{pillar}': {e}")
            return set()

    def log_and_email(self, stage: str, success: bool, details: str):
        """
        Log and email the status of each stage.
        """
        status = "SUCCESS" if success else "FAILURE"
        message = f"[{stage}] Status: {status}\nDetails: {details}"
        logging.info(message)

        # Send an email notification
        subject = f"Showback Processor - {stage} {status}"
        self.send_email(subject, message)

    def send_email(self, subject: str, body: str):
        """
        Send an email using a mail relay host.
        """
        try:
            sender_email = "<EMAIL>"
            receiver_email = "<EMAIL>"
            relay_host = "mailrelay.comcast.com"
            relay_port = 25

            server = smtplib.SMTP(relay_host, relay_port)
            server.ehlo()

            # Compose the email
            msg = MIMEMultipart()
            msg["From"] = sender_email
            msg["To"] = receiver_email
            msg["Subject"] = subject
            msg.attach(MIMEText(body, "plain"))

            # Send the email via the mail relay
            with smtplib.SMTP(relay_host, relay_port) as server:
                server.send_message(msg)

            logging.info(f"Email sent successfully to {receiver_email} via relay {relay_host}.")
        except Exception as e:
            logging.error(f"Failed to send email via relay {relay_host}: {e}")

    @staticmethod
    def _validate_or_generate_date(
        date_str: Optional[str],
        offset_minutes: int = 0,
        default_to: Optional[str] = None
    ) -> str:
        try:
            if date_str:
                dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M")
            else:
                if not default_to:
                    dt = datetime.now() + timedelta(minutes=offset_minutes)
                else:
                    dt = datetime.strptime(default_to, "%Y-%m-%d %H:%M")
            return dt.strftime("%Y-%m-%dT%H:%M:%SZ")
        except ValueError as e:
            logging.error(
                f"Invalid date format: {date_str}. "
                f"Expected format: 'YYYY-MM-DD HH:MM'. Error: {e}"
            )
            raise

    def build_rate_card_map(self):
        """
        Build rate card maps for metrics and visualization DevHub IDs.
        """
        for pillar in ["metrics", "visualization"]:
            resource_id = self.PILLAR_RESOURCE_MAP[pillar]
            rate_card_map = self.metrics_rate_card_map if pillar == "metrics" \
                else self.visualization_rate_card_map
            usage_map = self.metrics_usage_map if pillar == "metrics" \
                else self.visualization_usage_map
            # existing_rates = self.metrics_existing_rates if pillar == "metrics" \
            # else self.visualization_existing_rates
            existing_rates = set()

            for devhub_id in usage_map.keys():
                rate_key = (resource_id, str(devhub_id))
                if rate_key in existing_rates:
                    logging.info(f"Rate already exists for DevHub ID {devhub_id} and pillar "
                                 f"'{pillar}'. Skipping rate creation.")
                    continue  # Skip if the rate already exists in Showback

                # Create rate regardless of usage being 0.0
                if devhub_id not in rate_card_map:
                    rate_card_map[devhub_id] = {
                        "resourceID": resource_id,
                        "costPerUnit": self.PILLAR_COST_MAP[pillar],
                        "capexPercent": 1.0 if pillar == "metrics" else 0.0,
                        "partner": "comcast",
                        "startValidity": self.start_validity,
                        "endValidity": self.end_validity,
                    }
                    logging.info(f"Created rate for DevHub ID {devhub_id} in pillar '{pillar}'.")

        logging.info(f"Built rate card maps. Metrics: {len(self.metrics_rate_card_map)}, "
                     f"Visualization: {len(self.visualization_rate_card_map)}")

    def build_usage_map(self):
        """
        Build usage maps for metrics and visualization DevHub IDs.
        """
        for pillar in ["metrics", "visualization"]:
            resource_id = self.PILLAR_RESOURCE_MAP[pillar]
            usage_map = self.metrics_usage_map if pillar == "metrics" \
                else self.visualization_usage_map

            for devhub_id, usage_quantity in self.pillar_usage_map[pillar].items():
                if usage_quantity <= 0:
                    logging.warning(f"Skipping DevHub ID {devhub_id} in pillar "
                                    f"'{pillar}' due to zero quantity.")
                    continue

                # Ensure devhub_id (consumerID) is a string
                if not isinstance(devhub_id, str):
                    logging.warning(f"Converting consumerID to string: {devhub_id}")
                    devhub_id = str(devhub_id)

                # Ensure txID is generated for each entry
                try:
                    txID = self.showback_client._generate_short_ksuid()
                except AttributeError as e:
                    logging.error(f"Error generating txID for DevHub ID {devhub_id}: {e}")
                    txID = None

                # Add or update entry in the usage map
                if devhub_id in usage_map:
                    usage_map[devhub_id]["quantity"] += usage_quantity
                else:
                    usage_map[devhub_id] = {
                        "resourceID": resource_id,
                        "quantity": usage_quantity,
                        "partner": "comcast",
                        "txID": txID
                    }

        logging.debug(f"Built usage maps. Metrics: {len(self.metrics_usage_map)}, "
                      f"Visualization: {len(self.visualization_usage_map)}")

        # Collect metrics data after building usage maps
        self._collect_metrics_data()

    def prepare_rate_payload(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Prepare rate payloads for metrics and visualization.
        """
        payloads = {
            "metrics": [],
            "visualization": []
        }
        for devhub_id, rate_params in self.metrics_rate_card_map.items():
            payloads["metrics"].append({
                **rate_params,
                "consumerID": str(devhub_id),  # Convert consumerID to string
            })
        for devhub_id, rate_params in self.visualization_rate_card_map.items():
            payloads["visualization"].append({
                **rate_params,
                "consumerID": str(devhub_id),  # Convert consumerID to string
            })
        logging.debug(f"Prepared rate payloads. Metrics: {len(payloads['metrics'])}, "
                      f"Visualization: {len(payloads['visualization'])}")
        return payloads

    def prepare_usage_payload(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Prepare usage payloads for metrics and visualization with final validation.
        Only include usages with a quantity greater than zero.
        """
        payloads = {
            "metrics": [],
            "visualization": []
        }
        for devhub_id, usage_params in self.metrics_usage_map.items():
            if usage_params.get("quantity", 0) > 0:  # Only include usages with quantity > 0
                # Fix consumerID type and validate txID
                usage_params["consumerID"] = str(devhub_id)
                if "txID" not in usage_params or not usage_params["txID"]:
                    usage_params["txID"] = self.showback_client._generate_short_ksuid()
                payloads["metrics"].append(usage_params)

        for devhub_id, usage_params in self.visualization_usage_map.items():
            if usage_params.get("quantity", 0) > 0:  # Only include usages with quantity > 0
                # Fix consumerID type and validate txID
                usage_params["consumerID"] = str(devhub_id)
                if "txID" not in usage_params or not usage_params["txID"]:
                    usage_params["txID"] = self.showback_client._generate_short_ksuid()
                payloads["visualization"].append(usage_params)

        logging.debug(f"Prepared usage payloads. Metrics: {len(payloads['metrics'])}, "
                      f"Visualization: {len(payloads['visualization'])}")
        return payloads

    def _get_current_timestamp(self) -> int:
        """Get current timestamp in EPOCH seconds format"""
        import time
        return int(time.time())

    def _collect_metrics_data(self):
        """Collect metrics data during processing for later reporting"""
        try:
            # Count active pillars (both metrics and visualization are now active)
            active_pillars = list(self.PILLAR_RESOURCE_MAP.keys())
            self.metrics_data["pillar_count"] = len(active_pillars)

            # Count DevHub IDs per active pillar from actual usage maps
            self.metrics_data["devhub_id_counts"]["metrics"] = len(self.metrics_usage_map.keys())
            self.metrics_data["devhub_id_counts"]["visualization"] = \
                len(self.visualization_usage_map.keys())

            logging.info(
                f"Collected metrics data: pillar_count={self.metrics_data['pillar_count']}, "
                f"metrics_devhub_ids={self.metrics_data['devhub_id_counts']['metrics']}, "
                f"visualization_devhub_ids={self.metrics_data['devhub_id_counts']['visualization']}"
            )

        except Exception as e:
            logging.error(f"Failed to collect metrics data (non-blocking): {e}")

    def _report_success_metrics(self, pillar: Optional[str] = None):
        """
        Report all collected metrics to VMDB after successful showback processing.
        All metrics use the same timestamp for consistency.
        """
        if not self.metrics_data["processing_success"]:
            logging.warning("Not reporting metrics - processing was not successful")
            return

        if not self.vmdb_client:
            logging.warning("VMDB client not available - skipping metrics reporting")
            return

        try:
            # Single timestamp for all metrics (EPOCH seconds)
            timestamp = self._get_current_timestamp()

            # Get environment variables for labels
            deployenv = os.environ.get("DEPLOYENV", "unknown")
            env = os.environ.get("ENV", "unknown")

            # 1. Report run state (always 1.0 since only called on success)
            self.vmdb_client.remote_write_internal_gauge(
                metric_name="o11y-showback_run_state",
                labels={
                    "__name__": "o11y-showback_run_state",
                    "status": "success",
                    "env": deployenv,
                    "platform": env
                },
                value=1.0,
                timestamp=timestamp
            )

            # 2. Report pillar count
            self.vmdb_client.remote_write_internal_gauge(
                metric_name="o11y-showback_pillar_count",
                labels={
                    "__name__": "o11y-showback_pillar_count",
                    "env": deployenv,
                    "platform": env
                },
                value=float(self.metrics_data["pillar_count"]),
                timestamp=timestamp
            )

            # 3. Report DevHub ID counts per pillar
            for pillar_name, count in self.metrics_data["devhub_id_counts"].items():
                if pillar is None or pillar == pillar_name:  # Respect pillar filter
                    self.vmdb_client.remote_write_internal_gauge(
                        metric_name="o11y-showback_pillar_devhub-id_count",
                        labels={
                            "__name__": "o11y-showback_pillar_devhub-id_count",
                            "pillar": pillar_name,
                            "env": deployenv,
                            "platform": env
                        },
                        value=float(count),
                        timestamp=timestamp
                    )

            logging.info(f"Successfully reported showback metrics to VMDB at timestamp {timestamp}")
            logging.info(f"Metrics: pillar_count={self.metrics_data['pillar_count']}, "
                         f"devhub_counts={self.metrics_data['devhub_id_counts']}")

        except Exception as e:
            # Non-blocking: don't fail showback processing if metrics fail
            logging.error(f"Failed to report metrics to VMDB (non-blocking): {e}")

    def fetch_active_tenants(self) -> List[Dict[str, Any]]:
        """
        Fetch active tenants from MongoDB.
        """
        try:
            tenant_db = Tenant()
            tenants = tenant_db.find_many({}, projection={"_id": 1, "name": 1, "tenant_apps": 1})
            for tenant in tenants:
                tenant["devhub_ids"] = [app for app in tenant.get("tenant_apps", [])]
            logging.debug(f"Fetched {len(tenants)} active tenants.")
            return tenants
        except Exception as e:
            logging.error(f"Error fetching tenants: {e}")
            return []

    def process_instance_details(self, instance_id: str, pillar_name: str, devhub_ids: List[str]):
        """
        Fetch and process instance details for a given pillar and instance.
        Updates the usage and rate card maps based on extracted data.
        """
        if not devhub_ids:
            logging.warning(f"No DevHub IDs for instance {instance_id}. Skipping.")
            return

        try:
            # Fetch instance details
            details = get_instance_details(instance_id, pillar_name)
            if not details:
                logging.warning(f"No details found for instance {instance_id} "
                                f"in pillar {pillar_name}. Skipping.")
                return

            # Log the raw showback panel data for debugging
            showback_panels = details.get("showback", {}).get("panels", [])
            logging.debug(f"Showback panels for instance {instance_id}, pillar "
                          f"'{pillar_name}': {json.dumps(showback_panels, indent=2)}")

            # Initialize usage and cost variables
            usage_value = 0
            cost_per_unit = self.PILLAR_COST_MAP.get(pillar_name, 0)
            resource_id = self.PILLAR_RESOURCE_MAP[pillar_name]

            # Extract costs and usages from the 'showback' panels
            for panel in showback_panels:
                for cost_item in panel.get("costs", []):
                    if cost_item.get("resource_label") == f"{pillar_name.upper()}_COST_PER_DAY":
                        cost_per_unit = cost_item.get("cost", cost_per_unit)

                for viz in panel.get("visualizations", []):
                    resource_label = viz.get("resource_label", "")
                    if resource_label == "Metrics Time Series" and pillar_name == "metrics":
                        usage_value += viz.get("visualization", {}).get("value", 0)
                    elif resource_label == "Grafana User Count" and pillar_name == "visualization":
                        usage_value += viz.get("visualization", {}).get("value", 0)

            # Update usage map
            usage_map = self.metrics_usage_map if pillar_name == "metrics" \
                else self.visualization_usage_map
            usage_per_devhub = usage_value / len(devhub_ids) if devhub_ids else 0
            for devhub_id in devhub_ids:
                if devhub_id in usage_map:
                    usage_map[devhub_id]["quantity"] += usage_per_devhub
                else:
                    usage_map[devhub_id] = {
                        "resourceID": resource_id,
                        "quantity": usage_per_devhub,
                        "partner": "comcast",
                    }

            logging.debug(f"Processed instance {instance_id} for pillar '{pillar_name}'. "
                          f"Usage: {usage_value}, Cost: {cost_per_unit}")

        except Exception as e:
            logging.error(f"Error processing instance {instance_id} for pillar "
                          f"'{pillar_name}': {e}", exc_info=True)

    def process_tenant(self, tenant_id: str):
        """
        Process a single tenant by its ID.
        """
        tenant_db = Tenant()
        tenant = tenant_db.find_one({"_id": ObjectId(tenant_id)},
                                    projection={"_id": 1, "name": 1, "tenant_apps": 1})
        if not tenant:
            logging.error(f"Tenant with ID {tenant_id} not found.")
            return

        devhub_ids = [app for app in tenant.get("tenant_apps", [])]
        for pillar in self.PILLAR_RESOURCE_MAP.keys():
            instances = self.fetch_instances_by_tenant(tenant_id, pillar)
            for instance in instances:
                self.process_instance_details(instance.get("id"), pillar, devhub_ids)

    def fetch_instances_by_tenant(
            self, tenant_id: str, pillar_name: str) -> List[Dict]:
        """Fetch all instances for a given tenant ID and pillar."""
        try:
            # time.sleep(2)  # Add a delay between API calls to reduce 500 errors
            response = _get_instances_by_tenant(tenant_id, pillar_name)
            return response.get("msg", {}).get("instances", [])
        except Exception as e:
            logging.error(
                f"Error fetching instances for tenant {tenant_id} and pillar {pillar_name}: {e}")
            return []

    def cache_rates(self):
        """
        Write the rate card maps to files for caching rates per pillar.
        """
        os.makedirs(self.cache_dir, exist_ok=True)
        payloads = self.prepare_rate_payload()
        for pillar, rate_payload in payloads.items():
            if not rate_payload:
                logging.debug(f"No rates to cache for pillar '{pillar}'.")
                continue

            rate_file = os.path.join(self.cache_dir, f"{pillar}_rates.json")
            with open(rate_file, "w") as f:
                json.dump(rate_payload, f, indent=4)
            logging.debug(f"Cached rate payload for pillar '{pillar}' to {rate_file}.")

    def submit_rates(self, pillar: Optional[str] = None):
        """
        Submit cached rate payloads for a specific pillar or all pillars using
        ShowbackClient.create_rates.
        """
        pillars_to_process = [pillar] if pillar else ["metrics", "visualization"]

        for current_pillar in pillars_to_process:
            rate_file = os.path.join(self.cache_dir, f"{current_pillar}_rates.json")
            if not os.path.exists(rate_file):
                logging.debug(f"No cached rate file found for pillar '{current_pillar}'. "
                              f"Skipping submission.")
                continue

            with open(rate_file, "r") as f:
                rate_payload = json.load(f)

            if not rate_payload:
                logging.debug(f"No rates to submit for pillar '{current_pillar}'. "
                              f"Skipping submission.")
                continue

            try:
                response = self.showback_client.create_rates(
                    rate_payload, self.PILLAR_NAME_MAP[current_pillar]
                )
                logging.debug(f"Submitted rates for pillar '{current_pillar}' "
                              f"successfully: {response}")
            except Exception as e:
                logging.error(f"Error submitting rates for pillar '{current_pillar}': {e}")

    def cache_usages(self, pillar: Optional[str] = None) -> None:
        """
        Write the usage maps to files for caching usages per pillar with enhanced error
        handling and status tracking.
        Store status in `cache_status.json`. Email directly if caching fails completely.
        """
        result = {"success": True, "details": [], "usages_collected": 0}

        try:
            os.makedirs(self.cache_dir, exist_ok=True)
            logging.info(f"Cache directory verified: {self.cache_dir}")

            payloads = self.prepare_usage_payload()
            pillars_to_cache = [pillar] if pillar else ["metrics", "visualization"]
            logging.info(f"Pillars to cache: {pillars_to_cache}")

            for current_pillar in pillars_to_cache:
                usage_payload = payloads.get(current_pillar, [])
                if not usage_payload:
                    message = f"No usage data to cache for pillar '{current_pillar}'. Skipping."
                    logging.warning(message)
                    result["details"].append(message)
                    continue

                if not isinstance(usage_payload, list):
                    message = (f"Invalid usage payload format for pillar '{current_pillar}'. "
                               f"Expected a list.")
                    logging.error(message)
                    result["details"].append(message)
                    result["success"] = False
                    continue

                usage_file = os.path.join(self.cache_dir, f"{current_pillar}_usages.json")
                try:
                    with open(usage_file, "w") as f:
                        json.dump(usage_payload, f, indent=4)
                    message = (f"Successfully cached {len(usage_payload)} usage entries for pillar "
                               f"'{current_pillar}' to {usage_file}.")
                    logging.info(message)
                    result["details"].append(message)
                    result["usages_collected"] += len(usage_payload)
                except (IOError, json.JSONDecodeError) as e:
                    message = (f"Failed to write usage payload for pillar '{current_pillar}' "
                               f"to {usage_file}: {e}")
                    logging.error(message)
                    result["details"].append(message)
                    result["success"] = False
        except Exception as e:
            message = f"An unexpected error occurred during caching: {e}"
            logging.error(message)
            result["details"].append(message)
            result["success"] = False

        # Write status to `cache_status.json`
        status_file = os.path.join(self.cache_dir, "cache_status.json")
        try:
            with open(status_file, "w") as f:
                json.dump(result, f, indent=4)
            logging.info(f"Cache status written to {status_file}")
        except Exception as e:
            logging.error(f"Failed to write cache status to file: {e}")

        # Send email immediately if caching failed
        if not result["success"]:
            subject = "Showback Processor - Cache Usages FAILURE"
            email_body = "\n".join(result["details"])
            self.send_email(subject, email_body)

    def submit_usages(self, pillar: Optional[str] = None, simulate: bool = False) -> None:
        """
        Submit cached usage payloads for a specific pillar or all pillars
        using ShowbackClient.add_usages.
        Send a summary email with counts of cached and submitted usages.
        """
        result = {"success": True, "details": []}
        total_cached = 0
        total_submitted = 0

        # Step 1: Read caching status from `cache_status.json`
        cache_status_file = os.path.join(self.cache_dir, "cache_status.json")
        if os.path.exists(cache_status_file):
            try:
                with open(cache_status_file, "r") as f:
                    cache_status = json.load(f)
                logging.info("Loaded cache status for inclusion in submission email.")
                total_cached = cache_status.get("usages_collected", 0)
                result["details"].append(f"Total Cached Usages: {total_cached}")
                if not cache_status.get("success", True):
                    result["success"] = False
            except Exception as e:
                message = f"Failed to read cache status file: {e}"
                logging.error(message)
                result["details"].append(message)
                result["success"] = False
        else:
            message = "Cache status file not found. Skipping caching details."
            logging.warning(message)
            result["details"].append(message)

        # Step 2: Submit Usages (or simulate submission)
        try:
            pillars_to_process = [pillar] if pillar else ["metrics", "visualization"]
            for current_pillar in pillars_to_process:
                usage_file = os.path.join(self.cache_dir, f"{current_pillar}_usages.json")

                if not os.path.exists(usage_file):
                    message = (f"No cached usage file found for pillar '{current_pillar}'. "
                               f"Skipping submission.")
                    logging.warning(message)
                    result["details"].append(message)
                    continue

                try:
                    with open(usage_file, "r") as f:
                        usage_payload = json.load(f)

                    if not usage_payload:
                        message = (f"No usages to submit for pillar '{current_pillar}'. "
                                   f"Skipping submission.")
                        logging.warning(message)
                        result["details"].append(message)
                        continue

                    if simulate:
                        # Simulate submission
                        message = (f"Simulated submission for pillar '{current_pillar}'. "
                                   f"Payload size: {len(usage_payload)}")
                        logging.info(message)
                        total_submitted += len(usage_payload)
                    else:
                        # Actual submission
                        response = self.showback_client.add_usages(
                            usage_payload, self.PILLAR_NAME_MAP[current_pillar]
                        )
                        logging.info(f"Successfully submitted {len(response.get('results', []))} "
                                     f"usages for pillar '{current_pillar}'.")
                        total_submitted += len(usage_payload)

                        # Delete the file after successful submission
                        try:
                            os.remove(usage_file)
                            logging.info(f"Deleted cached usage file for pillar "
                                         f"'{current_pillar}': {usage_file}")
                        except Exception as delete_error:
                            logging.error(f"Failed to delete cached usage file for "
                                          f"pillar '{current_pillar}': {delete_error}")
                except (IOError, json.JSONDecodeError) as file_error:
                    message = (f"Error reading or decoding file '{usage_file}' "
                               f"for pillar '{current_pillar}': {file_error}")
                    logging.error(message)
                    result["details"].append(message)
                    result["success"] = False
                except Exception as e:
                    message = f"Error submitting usages for pillar '{current_pillar}': {e}"
                    logging.error(message, exc_info=True)
                    result["details"].append(message)
                    result["success"] = False
        except Exception as e:
            message = f"An unexpected error occurred during submission: {e}"
            logging.error(message, exc_info=True)
            result["details"].append(message)
            result["success"] = False

        # Step 3: Send a concise summary email
        status = "SUCCESS" if result["success"] else "FAILURE"
        subject = f"Showback Processor - Cache and Submit Usages {status}"
        email_body = (
            "Showback Usages Processing Summary:\n\n"
            f"Total Cached Usages: {total_cached}\n"
            f"Total Submitted Usages: {total_submitted}\n"
            "\nDetails:\n" + "\n".join(result["details"])
        )
        self.send_email(subject, email_body)

    def process(
        self,
        tenant_id: Optional[str] = None,
        cache_only: bool = False,
        submit: bool = False,
        pillar: Optional[str] = None,
        simulate: bool = False,
    ):
        """
        Main processing logic. Handles caching, submission, or both based on command-line arguments.
        """
        # Default to 'metrics' pillar if no pillar is specified
        pillar = pillar or "metrics"
        logging.info(f"Processing only the '{pillar}' pillar by default.")
        # Perform caching if `--cache-only` is specified
        if cache_only:
            logging.info("Caching rates and usages.")
            if tenant_id:
                logging.info(f"Caching data for tenant ID: {tenant_id}")
                self.process_tenant(tenant_id)
            else:
                logging.info("Caching data for all tenants.")
                tenants = self.fetch_active_tenants()
                for tenant in tenants:
                    self.process_tenant(str(tenant["_id"]))

            # Build and cache usage maps only for the specified pillar
            if pillar:
                logging.info(f"Building and caching usages for pillar: {pillar}")
                self.build_usage_map()
                self.cache_usages(pillar=pillar)
            else:
                logging.info("Building and caching usages for all pillars.")
                self.build_usage_map()
                self.cache_usages()

        # Perform submission if `--submit` is also specified
        if submit:
            logging.info("Submitting usages to Showback API.")
            if pillar:
                logging.info(f"Submitting only for pillar: {pillar}")
                self.submit_usages(pillar=pillar, simulate=simulate)
            else:
                self.submit_usages(simulate=simulate)

            # Mark processing as successful and report metrics
            self.metrics_data["processing_success"] = True
            self._report_success_metrics(pillar=pillar)

        # Default behavior if neither `--cache-only` nor `--submit` is specified
        if not cache_only and not submit:
            logging.info("Performing default full processing flow: caching and submission.")
            if tenant_id:
                logging.info(f"Processing single tenant with ID: {tenant_id}")
                self.process_tenant(tenant_id)
            else:
                logging.info("Processing all tenants.")
                tenants = self.fetch_active_tenants()
                for tenant in tenants:
                    self.process_tenant(str(tenant["_id"]))

            # Build and cache usage maps
            if pillar:
                logging.info(f"Building and caching usages for pillar: {pillar}")
                self.build_usage_map()
                self.cache_usages(pillar=pillar)
            else:
                logging.info("Building and caching usages for all pillars.")
                self.build_usage_map()
                self.cache_usages()

            # Submit usages after caching
            logging.info("Submitting usages to Showback API.")
            if pillar:
                logging.info(f"Submitting only for pillar: {pillar}")
                self.submit_usages(pillar=pillar, simulate=simulate)
            else:
                self.submit_usages(simulate=simulate)

            # Mark processing as successful and report metrics
            self.metrics_data["processing_success"] = True
            self._report_success_metrics(pillar=pillar)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Showback Processor")
    parser.add_argument("--tenant-id", help="Process a specific tenant by its ID.")
    parser.add_argument("--cache-only",
                        action="store_true",
                        help="Cache rates and usages without submission.")
    parser.add_argument("--cache",
                        action="store_true",
                        help="Read/Submit the payloads to Showback API from cache")
    parser.add_argument("--submit",
                        action="store_true",
                        help="Submit cached data to Showback API.")
    parser.add_argument("--simulate",
                        action="store_true",
                        help="Simulate submission to test email notifications "
                             "without actual submission.")
    parser.add_argument("--pillar",
                        choices=["metrics", "visualization"],
                        help="Specify a pillar for rate/usage submission.")
    parser.add_argument("--start-validity",
                        help="Specify start validity in 'YYYY-MM-DD HH:MM' format.")
    parser.add_argument("--end-validity",
                        help="Specify end validity in 'YYYY-MM-DD HH:MM' format.")

    args = parser.parse_args()

    processor = ShowbackProcessor(
        start_validity=args.start_validity,
        end_validity=args.end_validity,
    )
    processor.process(
        tenant_id=args.tenant_id,
        cache_only=args.cache_only,
        submit=args.submit,
        pillar=args.pillar,
        simulate=args.simulate
    )
