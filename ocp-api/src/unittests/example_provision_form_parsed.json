{"pillar": "metrics", "instructions": {"type": "numbered", "values": ["Please fill out this form.", "Then hit submit."]}, "inputs": [{"type": "text", "name": "name", "label": "Instance Name", "description": {"type": "list", "values": ["Small: upto 1M active time series", "Medium: upto 10M active time series", "Large: upto 25M active time series"]}, "required": true, "conditional": null}, {"type": "select", "name": "provisionTarget", "label": "Provision Target", "description": null, "options": [{"label": "AWS", "value": "aws"}, {"label": "RDEI", "value": "rdei"}], "conditional": null}, {"type": "select", "name": "awsSize", "label": "AWS Size", "description": null, "options": [{"label": "Small", "value": "1"}, {"label": "Medium", "value": "2"}, {"label": "Large", "value": "3"}], "conditional": {"for_field": "provisionTarget", "value": "aws"}}, {"type": "select", "name": "awsRegion", "label": "AWS Region", "description": null, "options": [{"label": "Europe (Frankfurt)", "value": "eu-central-1"}, {"label": "Europe (Ireland)", "value": "eu-west-1"}, {"label": "Europe (London)", "value": "eu-west-2"}, {"label": "Europe (Paris)", "value": "eu-west-3"}, {"label": "Europe (Stockholm)", "value": "eu-north-1"}, {"label": "Asia Pacific (Sydney)", "value": "ap-southeast-2"}], "conditional": {"for_field": "provisionTarget", "value": "aws"}}, {"type": "select", "name": "rdeiSize", "label": "RDEI Size", "description": null, "options": [{"label": "Small", "value": "1"}, {"label": "Medium", "value": "2"}, {"label": "Large", "value": "3"}], "conditional": {"for_field": "provisionTarget", "value": "rdei"}}, {"type": "checkbox", "name": "vmalert", "label": "V<PERSON>ert", "description": null, "options": null, "conditional": null}, {"type": "checkbox", "name": "alertmanager", "label": "Alert Manager", "description": null, "options": null, "conditional": null}]}