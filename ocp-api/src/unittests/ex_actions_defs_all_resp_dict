{'metrics': Actions(actions=[Action(type='View Log', description='View server log', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>, <Role.OPERATORS: 'operator'>], valid_states=['provisioning_completed', 'cluster_restarting', 'remapping_completed', 'nominal'], destructive=False), Action(type='Update Metadata', description='Update metadata for Instance', payload=Payload(fields=[KV(label='description', value='a new description'), KV(label='name', value='a new name')]), required_payload_fields=['description', 'name'], valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'cluster_restarting', 'remapping_completed', 'nominal'], destructive=False), Action(type='Restart VMAgent', description='Restart the VMAgent server', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'running'], destructive=False), Action(type='Restart VMAuth', description='Restart VMAuth server', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'running'], destructive=False), Action(type='Delete Instance', description='Delete Instance', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'running'], destructive=True)]), 'logging': Actions(actions=[Action(type='View VMAuth Log', description='View VMAuth log', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>, <Role.OPERATORS: 'operator'>], valid_states=['provisioning_completed', 'cluster_restarting', 'remapping_completed', 'nominal'], destructive=False), Action(type='Update Metadata', description='Update metadata for Instance', payload=Payload(fields=[KV(label='description', value='a new description'), KV(label='name', value='a new name')]), required_payload_fields=['description', 'name'], valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'cluster_restarting', 'remapping_completed', 'nominal'], destructive=False), Action(type='Restart VMAgent', description='Restart the VMAgent server', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'running'], destructive=False), Action(type='Restart VMAuth', description='Restart VMAuth server', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'running'], destructive=False), Action(type='Delete Instance', description='Delete Instance', payload=None, required_payload_fields=None, valid_roles=[<Role.OWNER: 'owner'>], valid_states=['provisioning_completed', 'running'], destructive=True)])} 
