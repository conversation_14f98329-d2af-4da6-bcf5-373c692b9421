{"pillar": "metrics", "instructions": {"type": "numbered", "values": ["Please fill out this form.", "Then hit submit."]}, "inputs": [{"type": "text", "label": "Instance Name", "name": "name", "required": true}, {"type": "select", "label": "Provision Target", "name": "provisionTarget", "required": true, "description": {"type": "paragraph", "values": ["RDEI is an on premises option.", "AWS is an offsite, cloud-based option for EU and AP only. Additional details will be required as the services are provisioned."]}, "options": [{"label": "AWS", "value": "aws"}, {"label": "RDEI", "value": "rdei"}]}, {"type": "select", "label": "AWS Size", "name": "awsSize", "conditional": {"for_field": "provisionTarget", "value": "aws"}, "required": true, "description": {"type": "list", "values": ["Small: upto 1M active time series", "Medium: upto 10M active time series", "Large: upto 25M active time series"]}, "options": [{"label": "Small", "value": "1"}, {"label": "Medium", "value": "2"}, {"label": "Large", "value": "3"}]}, {"type": "select", "label": "AWS Region", "name": "awsRegion", "conditional": {"for_field": "provisionTarget", "value": "aws"}, "required": true, "description": {"type": "list", "values": ["eu-central-1: Europe (Frankfurt)", "eu-west-1: Europe (Ireland)", "eu-west-2: Europe (London)", "eu-west-3: Europe (Paris)", "eu-north-1: Europe (Stockholm)", "ap-southeast-2: Asia Pacific (Sydney)"]}, "options": [{"label": "Europe (Frankfurt)", "value": "eu-central-1"}, {"label": "Europe (Ireland)", "value": "eu-west-1"}, {"label": "Europe (London)", "value": "eu-west-2"}, {"label": "Europe (Paris)", "value": "eu-west-3"}, {"label": "Europe (Stockholm)", "value": "eu-north-1"}, {"label": "Asia Pacific (Sydney)", "value": "ap-southeast-2"}]}, {"type": "select", "label": "RDEI Size", "name": "rdeiSize", "conditional": {"for_field": "provisionTarget", "value": "rdei"}, "required": true, "description": {"type": "list", "values": ["Small: upto 1M active time series", "Medium: upto 10M active time series", "Large: upto 25M active time series"]}, "options": [{"label": "Small", "value": "1"}, {"label": "Medium", "value": "2"}, {"label": "Large", "value": "3"}]}, {"type": "checkbox", "label": "V<PERSON>ert", "name": "vmalert"}, {"type": "checkbox", "label": "Alert Manager", "name": "alertmanager"}]}