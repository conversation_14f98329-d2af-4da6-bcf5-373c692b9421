import unittest
from unittest.mock import patch, MagicMock
from fastapi import Request, HTTPException
from src.clients.actived import ADClient

with patch('src.clients.actived.ADClient', autospec=True) as MockADClient:
    MockADClient.return_value.get_service_principal_token.\
        return_value = "mock_access_token"
    MockADClient.return_value.is_service_principal_token_expired.\
        return_value = False

    from src.security.oidc import validate_auth, user_has_required_role, _get_tenant_membership

# Mock request object
mock_request = MagicMock(spec=Request)
mock_request.cookies = {'access_token': 'mock_access_token'}
mock_request.headers = {'Authorization': 'Bearer mock_access_token'}
mock_request.scope = {
    'path': '/api/v1/tenants/create',
    'server': ('127.0.0.1', 8008),
    'client': ('127.0.0.1', 40174)
}
mock_request.url = "https://localhost:8008/api/v1/tenants/create"

# Mock request log data
mock_request_log = {
    'uri': '/api/v1/tenants/create',
    'server': ('127.0.0.1', 8008),
    'client': ('127.0.0.1', 40174),
    'body': b'{}',
    'url': "https://localhost:8008/api/v1/tenants/create",
    'request_id': 'mock_request_id'
}


class TestAuthFunctions(unittest.TestCase):

    @patch('src.security.oidc.is_authenticated', return_value=True)
    def test_validate_auth(self, mock_is_authenticated):
        print("\ntest_validate_auth")
        try:
            validate_auth(mock_request, mock_request_log)
        except Exception as e:
            self.fail(f"validate_auth raised {e} unexpectedly!")

    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    @patch('src.clients.mongo.Role.has_role_user',
           return_value=(True, 'owners'))
    def test_user_has_required_role(self, mock_has_role_user, mock_get_token):
        print("\ntest_user_has_required_role")
        result = user_has_required_role(mock_request,
                                        '64d3e74e7f28d3b29f7d8f36',
                                        ['owners'])
        self.assertTrue(result)
        mock_has_role_user.assert_called_once_with(mock_request,
                                                   '64d3e74e7f28d3b29f7d8f36')

    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    @patch('src.clients.mongo.Role.has_role_user', return_value=(False, None))
    def test_user_has_required_role_no_role(self, mock_has_role_user, mock_get_token):
        print("\ntest_user_has_required_role_no_role")
        result = user_has_required_role(mock_request,
                                        '64d3e74e7f28d3b29f7d8f36',
                                        ['owners'])
        self.assertFalse(result)
        mock_has_role_user.assert_called_once_with(mock_request,
                                                   '64d3e74e7f28d3b29f7d8f36')

    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    @patch('src.clients.mongo.Role.has_role_user', return_value=(False, None))
    @patch('src.clients.mongo.Tenant.get_tenant_by_slug')
    def test_get_tenant_membership_no_role(self, mock_get_tenant_by_slug,
                                           mock_has_role_user,
                                           mock_get_token):
        print("\ntest_get_tenant_membership_no_role")
        result = _get_tenant_membership(mock_request, "test",
                                        "9536d8c8-9b90-4762-9421-450f73401bb3")

        self.assertEqual(result, {"has_role": False, "role": None})

    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    @patch('src.clients.mongo.Role.has_role_user',
           return_value=(True, "owners"))
    @patch('src.clients.mongo.Tenant.get_tenant_by_slug')
    def test_get_tenant_membership_with_role(self, mock_get_tenant_by_slug,
                                             mock_has_role_user,
                                             mock_get_token):
        print("\ntest_get_tenant_membership_with_role")
        result = _get_tenant_membership(mock_request, "test",
                                        "c4b78e67-03b9-40da-ad31-"
                                        "6d8084140d1f")

        self.assertEqual(result, {"has_role": True, "role": "owners"})

    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    @patch('src.clients.mongo.Role.has_role_user', side_effect=HTTPException(
        status_code=404,
        detail="Failed to retrieve roles, user or groups not found"))
    @patch('src.clients.mongo.Tenant.get_tenant_by_slug')
    def test_get_tenant_membership_user_not_found(self,
                                                  mock_get_tenant_by_slug,
                                                  mock_has_role_user,
                                                  mock_get_token):
        print("\ntest_get_tenant_membership_user_not_found")
        with self.assertRaises(HTTPException) as context:
            _get_tenant_membership(mock_request, "test",
                                   "9536d8c8-9b90-4762-9421-450f701bb3")

        self.assertEqual(context.exception.status_code, 404)
        self.assertEqual(context.exception.detail, "Failed to retrieve "
                                                   "roles, user or "
                                                   "groups not found")
