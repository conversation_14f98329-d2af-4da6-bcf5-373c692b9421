import configparser
import requests
from requests.adapters import HTTPAdapter
from urllib3.util import Retry
import logging

config = configparser.ConfigParser()
config.read('config/backends.ini')

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)


class RetryAdapter:
    '''
       Provides interface for HTTP calls utilizing the HTTPAdapter class
       from requests and Retry class from urllib3, which manages retry logic
       for HTTP calls.  Each method will throw exceptions on any error, will
       raise exceptions on any 4xx type response, and will retry calls on any
       5xx type response.  The timeout keyword arg can be passed to any call,
       and additional keyword parameters for a request may be passed to each
       method (such as 'proxies').
    '''
    def __init__(self):
        try:
            self.max_retries = int(config.get('vault', 'MAX_RETRIES', fallback=5))
            self.backoff_factor = float(config.get('retry', 'BACKOFF_FACTOR', fallback=0.5))
            self.status_forcelist = [500, 502, 503, 504]
            self.session = requests.Session()

            # Set up retry strategy
            retries = Retry(
                total=self.max_retries,
                backoff_factor=self.backoff_factor,
                status_forcelist=self.status_forcelist,
                allowed_methods=["GET", "POST", "DELETE", "PUT", "PATCH"],
                raise_on_status=True
            )
            adapter = HTTPAdapter(max_retries=retries)
            self.session.mount('http://', adapter)
            self.session.mount('https://', adapter)
        except Exception as e:
            logger.exception("Failed to initialize RetryHandler: %s", e)
            raise Exception("RetryHandler Initialization failed")

    def get(self, url, headers=None, params=None, timeout=5, **kwargs):
        response = self.session.get(url, headers=headers, params=params,
                                    timeout=timeout, **kwargs)

        if response.status_code not in [200, 201, 204]:
            raise Exception({'method': 'GET',
                             'status': response.status_code,
                             'url': url,
                             'error': response.text})
        return response

    def post(self, url, headers=None, params=None, data=None, json=None,
             timeout=5, **kwargs):
        response = self.session.post(url, headers=headers, params=params,
                                     data=data, json=json, timeout=timeout,
                                     **kwargs)

        if response.status_code not in [200, 201, 204]:
            raise Exception({'method': 'GET',
                             'status': response.status_code,
                             'url': url,
                             'error': response.text})
        return response

    def put(self, url, headers=None, params=None, data=None, json=None,
            timeout=5, **kwargs):
        response = self.session.put(url, headers=headers, params=params,
                                    data=data, json=json, timeout=timeout,
                                    **kwargs)

        if response.status_code not in [200, 201, 204]:
            raise Exception({'method': 'GET',
                             'status': response.status_code,
                             'url': url,
                             'error': response.text})
            return response

    def delete(self, url, headers=None, params=None, data=None, json=None,
               timeout=5, **kwargs):
        response = self.session.delete(url, headers=headers, params=params,
                                       data=data, json=json, timeout=timeout,
                                       **kwargs)

        if response.status_code not in [200, 201, 204]:
            raise Exception({'method': 'GET',
                             'status': response.status_code,
                             'url': url,
                             'error': response.text})
        return response

    def patch(self, url, headers=None, params=None, data=None, json=None,
              timeout=5, **kwargs):
        response = self.session.patch(url, headers=headers, params=params,
                                      data=data, json=json, timeout=timeout,
                                      **kwargs)

        if response.status_code not in [200, 201, 204]:
            raise Exception({'method': 'GET',
                             'status': response.status_code,
                             'url': url,
                             'error': response.text})
        return response

    def request(self, method, url, headers=None, params=None, data=None,
                json=None, timeout=5, **kwargs):
        response = self.session.request(method, url, headers=headers,
                                        params=params, data=data, json=json,
                                        timeout=timeout, **kwargs)

        if response.status_code not in [200, 201, 204]:
            raise Exception({'method': 'GET',
                             'status': response.status_code,
                             'url': url,
                             'error': response.text})
        return response
