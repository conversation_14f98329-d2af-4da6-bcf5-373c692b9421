import secrets


async def get_request_log(req, include_headers=True, include_cookies=False):
    '''Generate standard log with info on requests to API calls
       An ID is generated to associate exceptions with the requests
       that caused them - running async, multi process server that may handle
       many requests simultaneously
    '''
    r_id = secrets.token_urlsafe(nbytes=32)
    log_obj = {'uri': req.scope['path'], 'server': req.scope['server'],
               'client': req.scope['client'], 'body': None if not hasattr(req,
               'body') else await req.body(), 'url': req.url,
               'request_id': r_id}
    if include_headers:
        log_obj['headers'] = {'user-agent': req.headers['user-agent'],
                              'sec-ch-ua-platform': None if
                              'sec-ch-ua-platform' not in req.headers.keys()
                              else req.headers['sec-ch-ua-platform'],
                              'content-type': None if 'content-type' not in
                              req.headers.keys() else
                              req.headers['content-type']},
    if include_cookies:
        log_obj['cookies'] = req.cookies

    return log_obj
