import configparser
import os
import logging
import json
from datetime import datetime, timed<PERSON><PERSON>
from fastapi import <PERSON><PERSON>P<PERSON>xception
from gql import Client, gql
from gql.transport.requests import RequestsHTTPTransport
from gql.transport.exceptions import TransportQueryError, TransportServerError
from src.util.oauth import get_devhub_oauth_token

# Configuration and logging setup
config = configparser.ConfigParser()
config.read('config/backends.ini')

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

# Get the logger for the gql.transport.requests module
gql_logger = logging.getLogger('gql.transport.requests')

# Set the logging level to WARNING to suppress INFO and DEBUG messages
gql_logger.setLevel(logging.WARNING)

# Path to the file where the token is stored
token_file_path = './token_cache.json'

# Cache for storing the token and its expiration time
token_cache = {
    'access_token': None,
    'expires_at': None
}

deploy_env = os.environ.get('DEPLOYENV')

if deploy_env in ['prod', 'test']:
    devhubURL = config.get('devhub', 'DEVHUB_API')
else:
    devhubURL = config.get('devhub', 'DEVHUB_API-STAGE')


def fetch_new_token():
    try:
        # Simulate fetching a new token
        token_data = get_devhub_oauth_token()
        access_token = token_data['access_token']
        expires_in = token_data['expires_in']  # seconds
        expires_at = datetime.utcnow() + timedelta(seconds=expires_in)

        # Store the new token and expiration time in the file
        with open(token_file_path, 'w') as file:
            json.dump({'access_token': access_token,
                       'expires_at': expires_at.isoformat()}, file)

        return access_token
    except Exception as e:
        logger.error(f"Error fetching new token: {e}")
        raise HTTPException(status_code=500,
                            detail="Error fetching new token")


def get_token_from_file():
    try:
        with open(token_file_path, 'r') as file:
            data = json.load(file)
            access_token = data['access_token']
            expires_at = datetime.fromisoformat(data['expires_at'])

            if datetime.utcnow() >= expires_at:
                logger.info("Token expired, fetching new token")
                return fetch_new_token()
            return access_token
    except FileNotFoundError:
        logger.warning("Token file not found, fetching new token")
        return fetch_new_token()
    except json.JSONDecodeError:
        logger.warning("Error decoding token file, fetching new token")
        return fetch_new_token()
    except Exception as e:
        logger.error(f"Unexpected error reading token file: {e}")
        raise HTTPException(status_code=500,
                            detail="Unexpected error reading token file")


def get_devhub_applications(app_name: str,
                            offset: int,
                            limit: int,
                            app_ids: list = None):
    try:
        token = get_token_from_file()
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error retrieving token: {e}")
        raise HTTPException(status_code=500,
                            detail="Error retrieving token")

    try:
        # Setup the GraphQL client
        transport = RequestsHTTPTransport(
            url=devhubURL,
            headers={"Authorization": f"Bearer {token}"},
            use_json=True,
        )

        client = Client(transport=transport,
                        fetch_schema_from_transport=False)

        # Define the GraphQL query with pagination and filter variables
        query = gql("""
            query Applications($pagination: PaginationInput,
                    $filter: ResourceCatalogApplicationFilter) {
                resources {
                    applications(pagination: $pagination, filter: $filter) {
                    name
                    applicationId
                    status
                    updatedAt
                    }
                }
            }
        """)

        if app_ids:
            variables = {
                "pagination": {"offset": offset, "limit": limit},
                "filter": {
                    "applicationIds": app_ids
                }
            }
        elif app_name:
            variables = {
                "pagination": {"offset": offset, "limit": limit},
                "filter": {
                    "applicationStatuses": ["Active", "PreProd"],
                    "applicationName": app_name
                }
            }
        else:
            raise HTTPException(status_code=400,
                                detail="Either app_name or app_ids must be provided") # noqa E501

        response = client.execute(query, variable_values=variables)
        return response
    except TransportQueryError as e:
        # Extract the message from the GraphQL error response
        error_detail = "GraphQL Query Error"
        if hasattr(e, 'errors') and e.errors:
            error_detail = e.errors[0].get('message', error_detail)
        logger.error(f"GraphQL Query Error: {error_detail}")
        raise HTTPException(status_code=400,
                            detail=f"GraphQL Query Error: {error_detail}")
    except TransportServerError as e:
        if e.response.status_code == 401:
            logger.error(f"Authentication Error: {e}")
            raise HTTPException(status_code=401,
                                detail="Authentication failed for the GraphQL request") # noqa E501
        elif e.response.status_code == 404:
            logger.error(f"Resource Not Found: {e}")
            raise HTTPException(status_code=404,
                                detail="Resource not found for the GraphQL request") # noqa E501
        error_message = str(e)
        logger.error(f"Server Error: {error_message}")
        raise HTTPException(status_code=503,
                            detail=f"Server Error: {error_message}")
    except Exception as e:
        error_message = str(e)
        logger.error(f"An unexpected error occurred: {error_message}")
        raise HTTPException(status_code=500,
                            detail=f"Internal Server Error: {error_message}")
