import configparser
import json
import logging
import logging.config
import os
import time
from slugify import slugify
from filelock import FileLock
import tempfile

from fastapi import Request, HTTPException
from src.security.auth import HeaderAuth
from src.util.retry_adapter import RetryAdapter

config = configparser.ConfigParser()
config.read('config/auth.ini')

logging.config.fileConfig('config/logging.conf', disable_existing_loggers=False)
logger = logging.getLogger(__name__)

# Get environment
env = os.environ.get('PLATFORMENV', 'local')

# Create a file lock for token operations
token_file_lock = FileLock(os.path.join(tempfile.gettempdir(), "ocp-token.lock"), timeout=10)

ha = HeaderAuth()
if env == 'local' or env == 'docker':
    ocp_proxy = None
else:
    ocp_proxy = os.environ.get('API_PROXY')

# Only set proxies if ocp_proxy is defined
proxies = {'https': ocp_proxy} if ocp_proxy else {}

# Set Graph API endpoint and service account ID
graphapi_endpoint = config.get('endpoints', 'graph')
svc_acc_id = (os.environ.get("OCP_SVC_ID_PROD") if env == 'rdei'
              else os.environ.get("OCP_SVC_ID_DEV"))

# Set OIDC parameters based on environment
if env in ['local', 'docker', 'concourse']:
    svc_principal_oid = os.environ.get('OCP_OIDC_SERVICE_PRINCIPAL_OID')
    oidc_client_id_prod = os.environ.get('OCP_OIDC_ID_PROD')
    oidc_client_secret_prod = os.environ.get('OCP_OIDC_SECRET_PROD')
    oidc_client_tid = os.environ.get('OCP_OIDC_TID')
else:
    svc_principal_oid = os.environ.get('oidc_service_principal')
    oidc_client_id_prod = os.environ.get('oidc_client_id_prod')
    oidc_client_secret_prod = os.environ.get('oidc_client_secret_prod')
    oidc_client_tid = os.environ.get('oidc_client_tid')


class ADClient:
    """Client for interacting with Azure Active Directory.

    This implementation uses a file-based lock for synchronization across processes,
    and prioritizes memory-first token access to minimize disk I/O.
    """

    def __init__(self):
        self.retry_adapter = RetryAdapter()
        self.token_file_name = 'ocp-svp-tokens'

        # Instance-level token cache for memory-first access
        self._token_cache = {
            'access_token': None,
            'expiry': 0,
            'last_disk_read': 0  # Timestamp of last disk read
        }

        # Initialize token from disk if needed
        # self._initialize_token()

    def _initialize_token(self):
        """Initialize token from disk if needed."""
        # Use file lock to ensure only one process loads/refreshes at a time
        with token_file_lock:
            # First try to load from disk
            token_data = self._load_token_from_disk()

            # Update instance cache
            self._token_cache['access_token'] = token_data.get('access_token')
            self._token_cache['expiry'] = token_data.get('expiry', 0)
            self._token_cache['last_disk_read'] = int(time.time())

            # If no token or expired, fetch a new one
            token_expired = self._is_token_expired(self._token_cache['expiry'])
            if not self._token_cache['access_token'] or token_expired:
                self._fetch_new_token()

    def _load_token_from_disk(self):
        """Load the access token and its expiry time from a file on disk.

        Returns:
            dict: A dictionary containing 'access_token' and 'expiry' keys.
        """
        token_data = {'access_token': None, 'expiry': 0}
        token_file_path = self.token_file_name

        if not os.path.exists(token_file_path):
            logger.warning(f"Token file {token_file_path} does not exist.")
            return token_data

        try:
            with open(token_file_path, 'r') as t_file:
                tokens = t_file.readlines()

            token_data['access_token'] = tokens[0].split("=")[1].strip()
            token_data['expiry'] = int(tokens[1].split("=")[1].strip())

            # Update instance cache
            self._token_cache['access_token'] = token_data['access_token']
            self._token_cache['expiry'] = token_data['expiry']
            self._token_cache['last_disk_read'] = int(time.time())
        except Exception as e:
            logger.error(f"Error reading tokens from disk: {e}")

        return token_data

    def _write_token_to_disk(self, token_data):
        """Write token to disk.

        Args:
            token_data (dict): A dictionary containing 'access_token' and 'expiry' keys.
        """
        tokens = [
            f"access={token_data['access_token']}\n",
            f"expiry={token_data['expiry']}"
        ]

        try:
            with open(self.token_file_name, 'w') as t_file:
                t_file.writelines(tokens)

            # Update instance cache
            self._token_cache['access_token'] = token_data['access_token']
            self._token_cache['expiry'] = token_data['expiry']
            self._token_cache['last_disk_read'] = int(time.time())
        except Exception as e:
            logger.error(f"Error writing tokens to disk: {e}")

    def write_service_principal_token_disk(self, access_token):
        """Legacy method maintained for backward compatibility."""
        token_data = {
            'access_token': access_token,
            'expiry': int(time.time()) + 3600
        }

        # Use file lock to ensure only one process writes at a time
        with token_file_lock:
            self._write_token_to_disk(token_data)

    def _is_token_expired(self, expiry):
        """Check if a token is expired based on its expiry time.

        Args:
            expiry (int): The token expiry timestamp.

        Returns:
            bool: True if the token is expired, False otherwise.
        """
        current_time = int(time.time())
        # No buffer time as per reviewer's suggestion
        return current_time >= expiry

    def _fetch_new_token(self):
        """Fetch a new token from Azure AD."""
        if env == 'concourse':
            return

        url = (f"https://login.microsoftonline.com/{oidc_client_tid}/"
               f"oauth2/v2.0/token")

        payload = {'grant_type': 'client_credentials',
                   'client_id': oidc_client_id_prod,
                   'client_secret': oidc_client_secret_prod,
                   'scope': 'https://graph.microsoft.com/.default'}

        headers = {'Content-Type': 'application/x-www-form-urlencoded'}

        try:
            response = self.retry_adapter.post(url,
                                               headers=headers,
                                               data=payload,
                                               proxies=proxies)
            token = response.json().get('access_token')
            if not token:
                logger.error("Failed to retrieve service principal token")
                raise Exception("No token present in response")

            # Create token data
            token_data = {
                'access_token': token,
                'expiry': int(time.time()) + 3600
            }

            # Write to disk with file lock
            self._write_token_to_disk(token_data)

            logger.debug("Service principal token retrieved and saved successfully")
        except Exception as e:
            logger.exception(f"Failed to retrieve service principal token: {e}")
            raise Exception({"message": "Failed to retrieve service principal token"})

    def refresh_service_principal_token(self):
        """Refresh the service principal token if it's expired."""
        # Check memory cache first (prioritize memory access)
        if (self._token_cache['access_token'] is not None
                and not self._is_token_expired(self._token_cache['expiry'])):
            # Token is valid in memory cache, no need to refresh
            return

        # Check if we need to reload from disk (if another process updated it)
        # Only reload from disk if it's been more than 5 seconds since last read
        current_time = int(time.time())
        if current_time - self._token_cache['last_disk_read'] > 5:
            # Load current token data from disk
            token_data = self._load_token_from_disk()

            # If token from disk is valid, we're done
            if not self._is_token_expired(token_data.get('expiry', 0)):
                return
        # If token is expired (either in memory or from disk)
        elif not self._is_token_expired(self._token_cache['expiry']):
            # Token is valid in memory cache, no need to refresh
            return

        # Token is expired, refresh it
        # Use file lock to ensure only one process refreshes at a time
        with token_file_lock:
            # Double-check if another process already refreshed the token
            fresh_token_data = self._load_token_from_disk()
            if self._is_token_expired(fresh_token_data.get('expiry', 0)):
                self._fetch_new_token()

    def is_service_principal_token_expired(self):
        """Check if the service principal token is expired (for external use)."""
        # Check memory cache first (prioritize memory access)
        if self._token_cache['access_token']:
            return self._is_token_expired(self._token_cache['expiry'])

        # If not in memory, load from disk
        token_data = self._load_token_from_disk()
        return self._is_token_expired(token_data.get('expiry', 0))

    def cleanup_tenant_groups(self, tenant_name):
        tenant_name = tenant_name.strip()
        self.delete_tenant_groups(tenant_name)

    def get_service_principal_token(self, force_refresh=False):
        """Get a valid service principal token, refreshing if needed."""
        if env == 'concourse':
            return None

        # Check memory cache first (prioritize memory access)
        if (not force_refresh
                and self._token_cache['access_token'] is not None
                and not self._is_token_expired(self._token_cache['expiry'])):
            # Use cached token if it's valid
            return self._token_cache['access_token']

        # If force refresh is requested, fetch a new token
        if force_refresh:
            with token_file_lock:
                self._fetch_new_token()
                # Token cache is updated in _write_token_to_disk
                return self._token_cache['access_token']

        # If token is expired or not in memory, check disk
        # Load current token data from disk
        token_data = self._load_token_from_disk()

        # If token is expired, refresh it
        if self._is_token_expired(token_data.get('expiry', 0)):
            # Use file lock to ensure only one process refreshes at a time
            with token_file_lock:
                # Double-check if another process already refreshed the token
                fresh_token_data = self._load_token_from_disk()
                if self._is_token_expired(fresh_token_data.get('expiry', 0)):
                    self._fetch_new_token()
                    # Token cache is updated in _write_token_to_disk
                    return self._token_cache['access_token']
                else:
                    # Use the fresh token data (already cached in _load_token_from_disk)
                    return self._token_cache['access_token']

        # Return the current token (which is now valid)
        return self._token_cache['access_token']

    def get_ad_group_by_name(self, group_name):
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        headers = {"Authorization": f"Bearer {token}"}
        query = f"?$filter=displayName eq '{group_name}'"

        try:
            response = self.retry_adapter.get(
                graphapi_endpoint + "groups" + query,
                headers=headers,
                proxies=proxies
            )
            response.raise_for_status()
            groups = response.json().get('value', [])
            if groups:
                return groups[0]
            return None
        except Exception as e:
            msg = f"Failed to fetch AD Group by name: {e=}"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})

    def provision_tenant_groups(self, user_access_token, tenant_name,
                                owner_id):
        tenant_name = tenant_name.strip()

        group_slug_pre = slugify(tenant_name, separator="_", lowercase=True)
        group_d = {
            "owners": "Owners",
            "ops": "Operators",
            "users": "Users",
            "collabs": "Collaborators"
        }
        tenant_groups = []
        groups_objs = {}
        owner_group_id = None

        for g, group_desc in group_d.items():
            group_name = f"O11Y - {tenant_name} - {group_desc}"
            logger.info(
                f"Checking if AD group, {group_slug_pre}_{g}, exists..."
            )

            existing_group = self.get_ad_group_by_name(group_name)
            if existing_group:
                logger.info(f"AD group {group_name} already exists.")
                groups_objs[g] = existing_group
                if g == "owners":
                    owner_group_id = existing_group['id']
            else:
                logger.info(f"Creating AD group, {group_slug_pre}_{g}...")

                try:
                    create_resp = self.create_ad_group(
                        user_access_token, group_name,
                        f"{group_slug_pre}_{g}", group_desc,
                        tenant_name, owner_id
                    )
                    groups_objs[g] = create_resp
                    if g == "owners":
                        owner_group_id = create_resp['id']
                except Exception as e:
                    msg = (
                        f"Failed to create {g} group for Tenant, "
                        f"'{tenant_name}': {e=}"
                    )
                    logger.exception(msg)
                    raise Exception({"message": f"Error: {msg}"})

            tenant_groups.append(group_name)

        self.validate_user_membership(
            owner_id, tenant_name, group_d['owners'], owner_group_id
        )
        logger.info(
            f"AD Group provisioning complete for Tenant, {tenant_name}: "
            f"{tenant_groups}"
        )

        return {
            "tenant": tenant_name,
            "group_names": tenant_groups,
            "group_objects": groups_objs
        }

    def create_ad_group(self, user_access_token, group_name, group_slug,
                        group_type, tenant_name, owner_id):
        params = {
            "displayName": group_name,
            "description": f"OCP Tenant Group for {tenant_name} {group_type}",
            "mailEnabled": False,
            "mailNickname": f"o11y_{group_slug}",
            "securityEnabled": True,
            "groupTypes": [],
            "<EMAIL>": [
                f"{graphapi_endpoint}servicePrincipals/{svc_principal_oid}"
            ],
            "<EMAIL>": [
                f"{graphapi_endpoint}users/{owner_id}"
            ]
        }

        headers = {
            "Authorization": f"Bearer {user_access_token}",
            "Content-type": "application/json"
        }

        try:
            create_group_resp = self.retry_adapter.post(
                graphapi_endpoint + "groups",
                headers=headers,
                json=params,
                proxies=proxies
            )
            create_group_resp.raise_for_status()
        except Exception as e:
            msg = f"Create AD Group request failed: {e=}"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})

        return create_group_resp.json()

    def validate_user_membership(self, owner_id, tenant_name, owners_desc,
                                 owner_group_id):
        self.refresh_service_principal_token()
        found = False
        count = 60
        while not found and count > 0:
            token = self.get_service_principal_token()
            headers = {
                "Authorization": f"Bearer {token}",
                "ConsistencyLevel": "eventual"
            }
            query = (
                f"users/{owner_id}/memberOf/microsoft.graph.group"
                f"?$count=true&$filter=startswith(displayName, "
                f"'O11Y - {tenant_name} - {owners_desc}')"
            )

            try:
                g_resp = self.retry_adapter.get(
                    graphapi_endpoint + query, headers=headers, proxies=proxies
                )
                logger.debug(
                    f"Testing membership for new Tenant AD Group, url: "
                    f"'{graphapi_endpoint + query}'"
                )
                if (g_resp.json()['value']
                        and 'id' in g_resp.json()['value'][0]
                        and g_resp.json()['value'][0]['id'] == owner_group_id):
                    found = True
            except Exception as e:
                msg = f"Graph request failed: {e}"
                logger.error(msg)

            time.sleep(1)
            count -= 1

        if not found:
            logger.error(
                f"Failed to validate user is member of '{tenant_name}' "
                f"Owner group, continuing..."
            )

    def get_ad_group_members(self, group_oid, level=0):
        ''' Get members of an AD group recursively '''
        self.refresh_service_principal_token()
        self.max_group_search_depth = 2

        get_members_resp = self._call_get_ad_group_members(group_oid)
        logger.debug(
            f"/groups/{group_oid}/members: {get_members_resp.json()}"
        )
        member_p_names = []
        members = get_members_resp.json()['value']
        for m in members:
            ''' Skip the following groups from the list of members
            ef68420f-92d7-4837-9ef5-c04d67444523 - CHQ - RAPTOR
            99fcef6a-6cbb-4bff-a804-d572830f5cf7 - CHQ - MELT
            95db9fcc-e4fa-43dc-a062-2dbbda175c12 - [TPX -- All Employees]
            364417cf-1ebc-4142-a6d7-26a950891d0f - Comcast - Corporate Users
            0cc66ffa-cf60-4e81-901c-10a272dd6bbc - Guests - Sky
            '''
            # TODO: Add separate collection in Mongo and manage from there
            if m['id'] in [
                'ef68420f-92d7-4837-9ef5-c04d67444523',
                '99fcef6a-6cbb-4bff-a804-d572830f5cf7',
                '95db9fcc-e4fa-43dc-a062-2dbbda175c12',
                '364417cf-1ebc-4142-a6d7-26a950891d0f',
                '0cc66ffa-cf60-4e81-901c-10a272dd6bbc'

            ]:
                continue
            if 'graph.user' in m['@odata.type']:
                member_p_names.append(m['userPrincipalName'])
            if 'graph.group' in m['@odata.type']:
                oid = m['id']
                if level < self.max_group_search_depth:
                    member_p_names.extend(
                        self.get_ad_group_members(oid, level=level + 1)
                    )
                else:
                    return member_p_names

        return [x.split("@")[0] for x in member_p_names]

    def _call_get_ad_group_members(self, oid):
        token = self.get_service_principal_token()
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-type": "application/json"
        }
        params = {"ConsistencyLevel": "eventual"}

        try:
            get_members_resp = self.retry_adapter.get(
                f"{graphapi_endpoint}groups/{oid}/members"
                f"?$select=displayName,userPrincipalName,id",
                headers=headers,
                json=params, proxies=proxies
            )
        except Exception as e:
            msg = f"AD Group membership lookup failed: {e}"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})

        return get_members_resp

    def rename_tenant_groups(self, old_tenant_name, new_tenant_name):
        old_tenant_name = old_tenant_name.strip()
        new_tenant_name = new_tenant_name.strip()

        group_slug_pre_old = slugify(
            old_tenant_name, separator="_", lowercase=True
        )
        group_slug_pre_new = slugify(
            new_tenant_name, separator="_", lowercase=True
        )
        group_d = {
            "owners": "Owners",
            "ops": "Operators",
            "users": "Users",
            "collabs": "Collaborators"
        }

        for g, group_desc in group_d.items():
            old_group_name = f"O11Y - {old_tenant_name} - {group_desc}"
            new_group_name = f"O11Y - {new_tenant_name} - {group_desc}"
            logger.info(
                f"Renaming AD group, {group_slug_pre_old}_{g} to "
                f"{group_slug_pre_new}_{g}..."
            )

            try:
                result = self.rename_ad_group(
                    old_group_name, new_group_name,
                    f"{group_slug_pre_new}_{g}"
                )
                if result == 0:
                    return 0
            except Exception as e:
                msg = \
                    (f"Failed to rename {g} group for Tenant, "
                     f"'{old_tenant_name}' "
                     f"to '{new_tenant_name}': "
                     f"{e}"
                     )
                logger.exception(msg)
                return 0

        logger.info(
            f"AD Group renaming complete for Tenant, {old_tenant_name} "
            f"to {new_tenant_name}"
        )
        return 1

    def rename_ad_group(self, old_group_name, new_group_name, new_group_slug):
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-type": "application/json"
        }

        try:
            group_id = self.find_group_id_by_name(old_group_name)
        except Exception as e:
            msg = f"Failed to fetch AD Group '{old_group_name}': {e}"
            logger.exception(msg)
            return 0

        params = {
            "displayName": new_group_name,
            "mailNickname": f"o11y_{new_group_slug}"
        }

        try:
            update_resp = self.retry_adapter.patch(
                graphapi_endpoint + f"groups/{group_id}",
                headers=headers,
                json=params, proxies=proxies
            )
            update_resp.raise_for_status()
        except Exception as e:
            msg = (
                f"Failed to update AD Group '{old_group_name}' to "
                f"'{new_group_name}': {e}"
            )
            logger.exception(msg)
            return 0

        return 1

    def delete_tenant_groups(self, tenant_name: str):
        """
        Delete all AD groups associated with a tenant.

        :param tenant_name: The name of the tenant.
        :raises: HTTPException if the deletion fails.
        """
        try:
            group_d = {
                "owners": "Owners",
                "ops": "Operators",
                "users": "Users",
                "collabs": "Collaborators"
            }
            for _, group_desc in group_d.items():
                group_name = f"O11Y - {tenant_name} - {group_desc}"
                try:
                    g_id = self.find_group_id_by_name(group_name)
                    self.delete_ad_group(g_id)
                except Exception:
                    pass

        except Exception as e:
            msg = f"Failed to remove AD Groups: {e}"
            logger.exception(msg)
            return 0

    def delete_ad_group(self, group_id, max_retries=4):
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        headers = {"Authorization": f'Bearer {token}'}

        # Loop to check if the group exists
        # TODO - configure retry adapter to accept max_retries as parameter
        found = False
        for attempt in range(max_retries):
            try:
                check_resp = self.retry_adapter.get(
                    graphapi_endpoint + f"groups/{group_id}",
                    headers=headers,
                    proxies=proxies
                )

                if check_resp.status_code == 200:
                    logger.info(f"AD Group {group_id} exists, proceeding with"
                                " deletion")
                    found = True
                    break
                elif check_resp.status_code == 404:
                    logger.warning(f"AD Group {group_id} not found")
                    return
                else:
                    check_resp.raise_for_status()
            except Exception as e:
                msg = "Error checking existence of AD Group "\
                    + f"{group_id} on attempt {attempt + 1}: {e=}"

                logger.exception(msg)

        if not found:
            raise Exception({"message": f"Failed to verify existence of "
                             f"AD Group {group_id} after {attempt} attempts."}
                            )

        # Attempt to delete the group after confirming its existence
        try:
            delete_resp = self.retry_adapter.delete(
                graphapi_endpoint + f"groups/{group_id}",
                headers=headers,
                proxies=proxies
            )
            delete_resp.raise_for_status()
        except Exception as e:
            msg = f"Delete AD Group request failed: {e=}"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})

        logger.info(f"AD Group {group_id} deleted successfully.")

    def find_group_id_by_name(self, group_name):
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        headers = {"Authorization": f'Bearer {token}',
                   "Content-type": "application/json"}

        try:
            query = f"groups?$filter=displayName eq '{group_name}'"
            response = self.retry_adapter.get(
                graphapi_endpoint + query, headers=headers, proxies=proxies
            )
            response.raise_for_status()
            group = response.json()['value']
            if group:
                return group[0]['id']
            else:
                raise Exception(f"Group '{group_name}' not found")
        except Exception as e:
            msg = f"Failed to fetch AD Group '{group_name}': {e}"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})

    def add_user_as_owner(self, group_oid, user_oid):
        """
        Add a user as an owner to a group.

        :param group_oid: The Object ID of the group.
        :param user_oid: The Object ID of the user to add as an owner.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        url = f"{graphapi_endpoint}groups/{group_oid}/owners/$ref"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        data = {
            "@odata.id": f"{graphapi_endpoint}users/{user_oid}"
        }

        try:
            response = self.retry_adapter.post(url,
                                               headers=headers,
                                               json=data,
                                               proxies=proxies)

            response.raise_for_status()
            logger.info(f"Successfully added user {user_oid} as owner to group {group_oid}")
        except Exception as e:
            logger.error(f"Failed to add user as owner: {e}")
            raise Exception({"message": f"Error: {e}"})

    def add_user_as_member(self, group_oid, user_oid):
        """
        Add a user as a member to a group.

        :param group_oid: The Object ID of the group.
        :param user_oid: The Object ID of the user to add as a member.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        url = f"{graphapi_endpoint}groups/{group_oid}/members/$ref"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        data = {
            "@odata.id": f"{graphapi_endpoint}users/{user_oid}"
        }

        try:
            response = self.retry_adapter.post(url,
                                               headers=headers,
                                               json=data,
                                               proxies=proxies)

            response.raise_for_status()
            logger.info(f"Successfully added user {user_oid} as member to group {group_oid}")
        except Exception as e:
            logger.error(f"Failed to add user as member: {e}")
            raise Exception({"message": f"Error: {e}"})

    def remove_user_as_owner(self, group_oid, user_oid):
        """
        Remove a user as an owner from a group.

        :param group_oid: The Object ID of the group.
        :param user_oid: The Object ID of the user to remove as an owner.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        url = f"{graphapi_endpoint}groups/{group_oid}/owners/{user_oid}/$ref"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = self.retry_adapter.delete(url,
                                                 headers=headers,
                                                 proxies=proxies)

            response.raise_for_status()
            logger.info(f"Successfully removed user {user_oid} as owner from group {group_oid}")
        except Exception as e:
            logger.error(f"Failed to remove user as owner: {e}")
            raise Exception({"message": f"Error: {e}"})

    def remove_user_as_member(self, group_oid, user_oid):
        """
        Remove a user as a member from a group.

        :param group_oid: The Object ID of the group.
        :param user_oid: The Object ID of the user to remove as a member.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        url = f"{graphapi_endpoint}groups/{group_oid}/members/{user_oid}/$ref"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = self.retry_adapter.delete(url,
                                                 headers=headers,
                                                 proxies=proxies)

            response.raise_for_status()
            logger.info(f"Successfully removed user {user_oid} as member from group {group_oid}")
        except Exception as e:
            logger.error(f"Failed to remove user as member: {e}")
            raise Exception({"message": f"Error: {e}"})

    def find_user_oid_by_ntid_or_email(self, ntid_or_email):
        """
        Find a user Object ID (OID) by NTid or email.

        :param ntid_or_email: The NTid or email of the user.
        :return: The user's OID if found, else None.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        # Build the query to search by userPrincipalName or mail only
        query = (f"users?$filter=userPrincipalName eq '{ntid_or_email}' "
                 f"or mail eq '{ntid_or_email}'")

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = self.retry_adapter.get(
                f"{graphapi_endpoint}{query}",
                headers=headers,
                proxies=proxies
            )
            response.raise_for_status()
            users = response.json().get('value', [])

            if users:
                user_oid = users[0]['id']
                logger.info(f"User OID for {ntid_or_email}: {user_oid}")
                return user_oid
            else:
                logger.warning(f"No user found with NTid or email: {ntid_or_email}")
                return None
        except Exception as e:
            logger.error(f"Failed to find user OID by NTid or email: {e}")
            raise Exception({"message": f"Error: {e}"})

    def get_user_name_by_user_oid(self, user_id):
        """
        Get the user's display name by User ID (OID).

        :param user_id: The Object ID of the user.
        :return: The user's display name if found, else None.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        url = f"{graphapi_endpoint}users/{user_id}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = self.retry_adapter.get(url,
                                              headers=headers,
                                              proxies=proxies)
            response.raise_for_status()
            user_data = response.json()
            user_name = user_data.get('displayName', None)

            if user_name:
                logger.info(f"User name for User ID {user_id}: {user_name}")
                return user_name
            else:
                logger.warning(f"No user found with User ID: {user_id}")
                return None
        except Exception as e:
            logger.error(f"Failed to get user name by User ID: {e}")
            raise Exception({"message": f"Error: {e}"})

    def get_user_name_by_ntid(self, ntid):
        """
        Get the user's display name by NTid (Network ID),
        using userPrincipalName if necessary.

        :param ntid: The NTid or email of the user. Example:
        '<EMAIL>'
        :return: The user's display name if found, else None.
        """
        self.refresh_service_principal_token()
        token = self.get_service_principal_token()

        # Build the query to search by userPrincipalName
        # or mail (instead of onPremisesSamAccountName)
        query = (f"users?$filter=userPrincipalName eq '{ntid}' "
                 f"or mail eq '{ntid}'")
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        try:
            response = self.retry_adapter.get(
                f"{graphapi_endpoint}{query}",
                headers=headers,
                proxies=proxies
            )
            response.raise_for_status()
            users = response.json().get('value', [])

            if users:
                user_name = users[0].get('displayName', None)
                logger.info(f"User name for NTid {ntid}: {user_name}")
                return user_name
            else:
                logger.warning(f"No user found with NTid: {ntid}")
                return None
        except Exception as e:
            logger.error(f"Failed to get user name by NTid: {e}")
            raise Exception({"message": f"Error: {e}"})

    def get_user_ad_groups(self,
                           request: Request,
                           userOnly: bool,
                           user_oid: str = None):
        # logger.debug(f"{request_log}")
        # TODO - rework this, we want to use existing request_log
        # if available, need refactor due to number of existing calls
        # to this method

        if 'id_token' in request.cookies.keys() and not user_oid:
            # only checking id token when call comes from oauth method,
            # 'get_tenant_membership', worth refactor?
            _, claims = ha.validate_token(request.cookies['id_token'],
                                          return_payload=True)
        users_groups = None

        if 'access_token' not in request.cookies.keys() and not user_oid:
            logger.exception("Access token not available to request users"
                             + " groups")
            raise HTTPException(status_code=500,
                                detail="Failed to retrieve users groups")
        else:
            access_token = None
            if not user_oid:
                access_token = request.cookies['access_token']
            else:
                # get service account access token for GraphAPI calls
                access_token = self.get_service_principal_token()

            headers = {"Authorization":
                       f"Bearer {access_token}",
                       "ConsistencyLevel": "eventual"}
            if userOnly:
                oid = None
                if not user_oid:
                    oid = claims['oid']
                else:
                    oid = user_oid
                query = \
                    f"users/{oid}/transitiveMemberOf/"\
                    + "microsoft.graph.group"
            else:
                query = "groups"

            query += "?$count=true&$orderby=displayName"\
                + "&$filter=startswith(description,'OCP Tenant')"
            try:
                adg_resp = self.retry_adapter.get(graphapi_endpoint + query,
                                                  headers=headers, proxies=proxies)
            except Exception as e:
                logger.exception("Failed to retrieve groups user is a member of: "
                                 f"{e=}")  # - {request_log}")
                if '404' in str(e):
                    raise HTTPException(detail="Failed to retrieve user groups"
                                        ", user or groups not found: 404",
                                        status_code=404)
                raise HTTPException(detail="Failed to retrieve user groups",
                                    status_code=500)

            adg_data = json.loads(adg_resp.text)
            users_groups = []
            for g in adg_data['value']:
                users_groups.append({'oid': g['id'], 'name': g['displayName'],
                                     'description': g['description'] if
                                     g['description'] else ""})

            # Test for membership in admin group
            if not user_oid:
                query = \
                    "me/memberOf?$filter=id eq "\
                    + "'ef68420f-92d7-4837-9ef5-c04d67444523'"
            else:
                query = \
                    f"users/{user_oid}/memberOf?$filter=id eq "\
                    + "'ef68420f-92d7-4837-9ef5-c04d67444523'"
            found = False
            try:
                resp = self.retry_adapter.get(graphapi_endpoint + query,
                                              headers=headers, proxies=proxies)
            except Exception as e:
                if "404" in str(e):
                    pass
                else:
                    logger.error("Failed to test admin group membership for "
                                 + f"user: {e=}")
            else:
                found = True

            if found:
                r = resp.json()['value'][0]
                users_groups.append({'oid': r['id'], 'name': r['displayName'],
                                     'description': r['description'] if
                                     r['description'] else ""})

            # users_groups_value = '' if users_groups is None else
            #    str(users_groups)[1:-1].replace(', ','|').replace("'",'')
            # response.set_cookie(key="users_groups", value=users_groups_value,
            #    max_age=600, path="/", domain=domain, samesite="strict",
            #    secure=True, httponly=True)

            # TODO - additional transformation required to prevent illegal chars
            # from ending up in cookie value
        # if not users_groups:
        #    users_groups = request.cookies["users_groups"]
        # users_groups_obj = []
        # for g in users_groups.split("||||"):
        #    group = {}
        #    for d in g.split("|"):
        #        key, val = d.split(":")
        #        val = val[1:]
        #        group[key] = val
        #    users_groups_obj.append(group)

        return users_groups
