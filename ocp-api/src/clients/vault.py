import configparser
import hvac
# import json
import os
import logging
import logging.config

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

config = configparser.ConfigParser()
config.read('config/backends.ini')

mount = 'kv2'
hvac_timeout = 5
vault_address = config.get('vault', 'VAULT_ADDR')
path = os.environ.get("VAULT_PATH")
role_id = os.environ.get('VAULT_ROLE_ID')
secret_id = os.environ.get('VAULT_SECRET_ID')


class VaultClient:

    @classmethod
    def create_vault_client(self):
        return hvac.Client(
            url=vault_address,
            token=self.fetch_token(),
            timeout=hvac_timeout)

    @classmethod
    def fetch_token(self):
        token = None
        try:
            with open("vault_token", "r") as t_file:
                token = t_file.read()
            if not self.validate_token(token):
                token = self.generate_token()
                self.store_token(token)
            return token
        except Exception:
            token = self.generate_token()
            self.store_token(token)
            return token

        logger.debug("Failed to fetch SaaS Vault token")
        return None

    @classmethod
    def store_token(self, token):
        try:
            with open("vault_token", "w") as t_file:
                t_file.write(token)
        except Exception:
            logger.exception("Failed to write token to disk")

    @classmethod
    def generate_token(self):
        client = self.authenticate_vault_client()
        logger.debug("Generated new Vault token")
        return str(client.token)

    @classmethod
    def authenticate_vault_client(self):
        client = hvac.Client(url=vault_address)
        client.auth.approle.login(role_id, secret_id)

        if client.is_authenticated():
            return client

        msg = "Failed to authenticate with SaaS Vault"
        logger.error(msg)
        raise Exception({"message": f"Error: {msg}"})

    @classmethod
    def validate_token(self, token):
        client = hvac.Client(url=vault_address,
                             token=token,
                             timeout=hvac_timeout)
        try:
            token_ttl = client.lookup_token().get('data', {}).get('ttl', 0)
        except Exception as e:
            logger.exception(f"Token validation failed: {e=}")
            return False

        if token_ttl > 0:
            return True

        return False

    @classmethod
    def read_secrets(self, log=True, p=False):
        retries = 0
        v_client = self.create_vault_client()
        while retries < 3:
            try:
                res = v_client.secrets.kv.v2.\
                    read_secret_version(path=path,
                                        mount_point=mount)
                secrets = ''
                for s in res['data']['data']:
                    secrets = secrets + f"{s}={res['data']['data'][s]}|"
                secrets = secrets[:-1]
                if p:
                    print(secrets)
                return secrets

                # return json.dumps(res['data']['data'])

            except Exception as e:
                if log:
                    logger.error("Failed to read secrets from SaaS Vault: "
                                 + f"{e=}")
                self.store_token(self.generate_token())
                retries += 1

        msg = "Failed to read secrets from SaaS Vault"
        logger.exception(msg)
        raise Exception({"message": f"Error: {msg}"})

    @classmethod
    def get_adauto_refresh_token(self):
        retry = 0
        v_client = self.create_vault_client()
        while retry < 3:
            try:
                res = v_client.secrets.kv.v2.\
                    read_secret_version(path=path,
                                        mount_point=mount)
                token = res['data']['data']['oidc_adauto_refresh_token']
                return token
            except Exception as e:
                logger.error("Failed to read refresh token from SaaS Vault: "
                             + f"{e=}")
                self.store_token(self.generate_token())
                retry += 1
        msg = "Failed to read secrets from SaaS Vault"
        logger.exception(msg)
        raise Exception({"message": f"Error: {msg}"})

    @classmethod
    def store_adauto_refresh_token(self, token):
        success = False
        retry = 0
        v_client = self.create_vault_client()
        while retry < 3:
            try:
                v_client.secrets.kv.v2.\
                    patch(
                        path=path,
                        mount_point=mount,
                        secret=dict(oidc_adauto_refresh_token=token))
                logger.info("Stored ADAUTO Refresh Token")
                success = True
                break
            except Exception as e:
                logger.error("Failed to store Refresh Token to SaaS Vault: "
                             + f"{e=}")
                self.store_token(self.generate_token())
                retry += 1
        if not success:
            msg = "Failed to store Refresh Token to SaaS Vault"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})
