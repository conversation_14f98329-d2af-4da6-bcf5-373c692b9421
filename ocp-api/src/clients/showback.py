import configparser
import json
import logging
import logging.config
import os
import sys
import time
import warnings
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, List
from urllib3.exceptions import InsecureRequestWarning

import ksuid
import requests

# Suppress only the InsecureRequestWarning
warnings.simplefilter("ignore", InsecureRequestWarning)
logging.getLogger("urllib3").setLevel(logging.CRITICAL)

sys.path.extend("./src")

from src.models.showback import (
    ResourcesV1PostBulkRequest,
    ResourcesV1PatchBulkRequest
)
from src.util.oauth import (
    get_metrix_showback_oauth_token,
    get_visualization_showback_oauth_token
)
from src.util.retry_adapter import RetryAdapter

# Configure logging
logging.config.fileConfig("config/logging.conf", disable_existing_loggers=False)
logging.getLogger().setLevel(logging.ERROR)
logger = logging.getLogger(__name__)

# Load configuration
config = configparser.ConfigParser()
config.read("config/backends.ini")

# Determine environment
env = os.environ.get("PLATFORMENV", "local")
showback_api = (
    "https://gw-ce-data.api.dh.comcast.com/showback/stage"
    if env in ["local", "docker"]
    else "https://gw-ce-data.api.dh.comcast.com/showback/prod"
)

showback_api = "https://gw-ce-data.api.dh.comcast.com/showback/stage"


class ShowbackClient:
    def __init__(self):
        self.base_url = showback_api
        self.token_metrix_file_name = "metrix_showback_oauth_token.json"
        self.token_visualization_file_name = "visualization_showback_oauth_token.json"
        self.retry_adapter = RetryAdapter()
        self.metrix_showback_oauth_token = self.refresh_metrix_showback_token()
        self.visualization_showback_oauth_token = self.refresh_visualization_showback_token()

    def read_metrix_showback_token_disk(self):
        if not os.path.exists(self.token_metrix_file_name):
            logger.warning(f"Metrix token file {self.token_metrix_file_name} does not exist.")
            return {"access": "", "expiry": 0}

        try:
            with open(self.token_metrix_file_name, "r") as token_file:
                lines = token_file.readlines()
                access = lines[0].split("=")[1].strip()
                expiry = int(lines[1].split("=")[1].strip())
                return {"access": access, "expiry": expiry}
        except Exception as e:
            logger.error(f"Error reading metrix token file: {e}")
            return {"access": "", "expiry": 0}

    def write_metrix_showback_token_disk(self, access_token: str, expiry: int):
        try:
            with open(self.token_metrix_file_name, "w") as token_file:
                token_file.write(f"access={access_token}\n")
                token_file.write(f"expiry={expiry}\n")
        except Exception as e:
            logger.error(f"Error writing metrix token to disk: {e}")

    def refresh_metrix_showback_token(self):
        tokens = self.read_metrix_showback_token_disk()
        if int(time.time()) >= tokens.get("expiry", 0):
            logger.debug("Metrix token expired or missing, fetching new token")
            token_data = get_metrix_showback_oauth_token()
            new_token = token_data.get("access_token")
            expiry_time = token_data.get("expires_in", 3600) + int(time.time())
            if not new_token:
                raise ValueError("OAuth metrix token retrieval failed")
            self.write_metrix_showback_token_disk(new_token, expiry_time)
            return new_token
        logger.debug("Using cached metrix token")
        return tokens.get("access", "")

    def read_visualization_showback_token_disk(self):
        if not os.path.exists(self.token_visualization_file_name):
            logger.warning(f"Visualization token file {self.token_visualization_file_name} does not exist.")
            return {"access": "", "expiry": 0}

        try:
            with open(self.token_visualization_file_name, "r") as token_file:
                lines = token_file.readlines()
                access = lines[0].split("=")[1].strip()
                expiry = int(lines[1].split("=")[1].strip())
                return {"access": access, "expiry": expiry}
        except Exception as e:
            logger.error(f"Error reading visualization token file: {e}")
            return {"access": "", "expiry": 0}

    def write_visualization_showback_token_disk(self, access_token: str, expiry: int):
        try:
            with open(self.token_visualization_file_name, "w") as token_file:
                token_file.write(f"access={access_token}\n")
                token_file.write(f"expiry={expiry}\n")
        except Exception as e:
            logger.error(f"Error writing visualization token to disk: {e}")

    def refresh_visualization_showback_token(self):
        tokens = self.read_visualization_showback_token_disk()
        if int(time.time()) >= tokens.get("expiry", 0):
            logger.debug("Visualization token expired or missing, fetching new token")
            token_data = get_visualization_showback_oauth_token()
            new_token = token_data.get("access_token")
            expiry_time = token_data.get("expires_in", 3600) + int(time.time())
            if not new_token:
                raise ValueError("OAuth visualization token retrieval failed")
            self.write_visualization_showback_token_disk(new_token, expiry_time)
            return new_token
        logger.debug("Using cached visualization token")
        return tokens.get("access", "")

    # def _generate_short_ksuid(self) -> str:
    #     ksuid_obj = ksuid.ksuid()
    #     return ksuid_obj.toBase62()

    def _generate_short_ksuid(self) -> str:
        """
        Generate a Base62-encoded KSUID.
        """
        try:
            # Generate a raw KSUID object
            raw_ksuid = ksuid.ksuid()
            # Access the `bytes` attribute correctly
            ksuid_bytes = raw_ksuid.bytes()
            # Convert the raw KSUID bytes to an integer
            ksuid_int = int.from_bytes(ksuid_bytes, byteorder="big")
            # Base62 encode the integer
            base62_chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
            base62 = []
            while ksuid_int > 0:
                ksuid_int, remainder = divmod(ksuid_int, len(base62_chars))
                base62.append(base62_chars[remainder])
            # Return the Base62 encoded string (reversed)
            return ''.join(reversed(base62))
        except Exception as e:
            logger.error(f"Error generating Base62 KSUID: {e}")
            raise

    def _make_request(self, method: str, endpoint: str, pillar: str, **kwargs) -> Any:
        """
        Centralized HTTP request handler utilizing RetryAdapter and consistent error handling.
        """
        try:
            token = self.metrix_showback_oauth_token if pillar == "metrix" else self.visualization_showback_oauth_token
            url = f"{self.base_url}{endpoint}"
            headers = kwargs.pop("headers", {})
            headers.update({
                "Authorization": f"Bearer {token}",
                "X-Request-ID": self._generate_short_ksuid(),
                "Content-Type": "application/json",
                "Accept": "application/json",
            })
            kwargs["headers"] = headers

            # logger.info(f"Executing {method.upper()} request to {url} with headers: {headers} and kwargs: {kwargs}")
            response = self.retry_adapter.request(method.upper(), url, **kwargs)

            # Handle valid 204 responses
            if response.status_code == 204:
                logger.info(f"Received 204 No Content for {method.upper()} {url}")
                return {}

            if response.status_code == 400:
                logger.info(f"Received 400 for {method.upper()} {url}")
                return {}

            # Log and parse response
            logger.debug(f"Received response: {response.status_code} - {response.text}")
            response.raise_for_status()  # Raise HTTP errors for bad status codes (4xx/5xx)

            return response.json()

        except requests.exceptions.RequestException as e:
            # Pass HTTP-related exceptions up the stack
            logger.error(f"HTTP request exception for {method.upper()} {url}: {str(e)}")
            raise e

        except Exception as e:
            # Handle unexpected exceptions
            error_detail = "Unknown error occurred"
            resolution = "Refer to the API documentation"
            error_code = None

            if isinstance(e.args, tuple) and isinstance(e.args[0], dict):
                error_data = e.args[0]
                error_json = json.loads(error_data.get("error", "{}"))
                error_detail = error_json.get("detail", error_detail)
                error_code = error_json.get("code")

            # Known error handling
            known_errors = {
                "resources-401-f54d08b49816": "Unauthorized: Add ITRC/ApplicationId to SAT token.",
                "resources-401-b3b770554bcb": "Unauthorized: Reauthorize the token.",
                "usages-400-ce0f5d6c0855": "No active rate within bounds for the provided resource ID.",
                "usages-400-3a388c01c6a9": "Resource ID not associated with the account. Verify the resource ID.",
                "resources-400-791241fe7afe": "Resource ID is immutable and associated with an existing usage. Verify immutability.",
                "resources-400-3ed7c12d40642": "App ID associated with the token is retired or inactive. Check devhub portal.",
                "usages-400-d8c4950c3235": "Self-consumer usage submission is not allowed.",
                "usages-400-3d649d28afb6": "Timestamp is out of the allowed backfill period. Verify timestamp.",
                "usages-400-88736a4d652d": "No rate defined for the specified partner or consumer ID.",
                "rates-400-0d102b5be19f": "Rate does not match schema. Check `capexPercent` and other values.",
                "usages-429-f54d08b49816": "Rate limit exceeded. Batch requests to avoid exceeding limits.",
                "rates-400-f6865bb197c2": "Partner not authorized for the SAT client. Verify SAT configuration.",
                "rates-400-bef3a5de292d": "Resource  ID not found. Verify the rate ID.",
                "resources-400-bef3a5de292d": "Resource ID not found. Verify the resource ID."
            }
            resolution = known_errors.get(error_code, resolution)

            # Return a controlled error response without propagating stack traces
            # logger.error(f"Error during {method.upper()} request to {url}: {error_detail} | Resolution: {resolution}")
            return {"error": error_detail, "resolution": resolution}


    def list_rates(self, providerID: Optional[str] = None, rate_ids: Optional[List[str]] = None, pillar: str = "") -> Dict[str, Any]:
        """
        Lists rates based on provider ID or rate IDs.
        """
        params = {}
        if providerID:
            params["providerID"] = providerID
        if rate_ids:
            params["id"] = rate_ids

        response = self._make_request("GET", "/rates", pillar, params=params)

        # Check if an error was returned
        if "error" in response:
            return response  # Return the structured error message as is

        # logger.info(f"{response}")
        return response

    def create_resources(self, resources: List[Dict[str, str]], pillar: str) -> Dict[str, Any]:
        sanitized_resources = [{k: v for k, v in resource.items() if k != "resourceID"} for resource in resources]
        validated_resources = ResourcesV1PostBulkRequest(resources=sanitized_resources)
        response = self._make_request("POST", "/resources", pillar, json=validated_resources.dict()["resources"])
        return self._parse_response(response, "resources")

    def list_resources(self, providerID: Optional[str] = None, resource_ids: Optional[List[str]] = None, pillar: str = "") -> Dict[str, Any]:
        params = {}
        if providerID:
            params["providerID"] = providerID
        if resource_ids:
            params["id"] = resource_ids
        return self._make_request("GET", "/resources", pillar, params=params)

    def update_resources(self, resources: ResourcesV1PatchBulkRequest, pillar: str) -> Dict[str, Any]:
        return self._make_request("PATCH", "/resources", pillar, json=resources.dict())

    def delete_resources(self, resource_ids: List[str], pillar: str) -> Dict[str, Any]:
        return self._make_request("DELETE", "/resources", pillar, json=resource_ids)

    def create_rates(self, rates: List[Dict[str, Any]], pillar: str, batch_size: int = 6) -> Dict[str, Any]:
        """
        Creates rates in batches with proper validations and startValidity adjustments.
        """
        # Define the consumerIDs to skip rate creation
        skip_consumer_ids = {"104409", "103612"}
        validated_rates = []

        for rate in rates:
            try:
                # Skip rates for specific consumerIDs
                if str(rate.get("consumerID")) in skip_consumer_ids:
                    logger.debug(f"Skipping rate creation for consumerID: {rate['consumerID']}")
                    continue

                # Ensure required fields are present and correctly formatted
                if "startValidity" in rate:
                    start_validity_dt = datetime.strptime(rate["startValidity"], "%Y-%m-%dT%H:%M:%SZ")
                    current_time_utc = datetime.utcnow()
                    if start_validity_dt <= current_time_utc:
                        adjusted_start_validity = (current_time_utc + timedelta(minutes=10)).strftime("%Y-%m-%dT%H:%M:%SZ")
                        logger.warning(f"Adjusting startValidity for rate: {rate['startValidity']} -> {adjusted_start_validity}")
                        rate["startValidity"] = adjusted_start_validity

                if "endValidity" in rate and not rate["endValidity"].endswith("Z"):
                    rate["endValidity"] = rate["endValidity"] + "Z"

                # Handle optional consumerID
                if "consumerID" in rate and rate["consumerID"]:
                    rate["consumerID"] = str(rate["consumerID"])  # Ensure consumerID is a string
                else:
                    logger.debug("Creating a Special Partner Rate (no consumerID).")

                rate["costPerUnit"] = float(rate.get("costPerUnit", 0.0))

                # Ensure costCurrencyCode is added if missing
                if "costCurrencyCode" not in rate:
                    rate["costCurrencyCode"] = "USD"

                validated_rates.append(rate)

            except KeyError as ke:
                logger.error(f"Missing required field in rate: {ke}")
            except Exception as e:
                logger.error(f"Unexpected error during rate validation: {e}")

        results = []

        # Process rates in batches, adhering to the API limit
        for i in range(0, len(validated_rates), batch_size):
            batch = validated_rates[i:i + batch_size]
            logger.info(f"Submitting batch of {len(batch)} rates.")

            try:
                response = self._make_request("POST", "/rates", pillar, json=batch)
                if "rates" not in response:
                    logger.error(f"Unexpected response format: {response}")
                    results.append({"error": "Unexpected response format", "response": response})
                    continue

                # Parse rate details from the response
                rate_details = [
                    {
                        "resourceID": rate.get("resourceID", "N/A"),
                        "consumerID": rate.get("consumerID", "N/A"),
                        "costPerUnit": rate.get("costPerUnit", 0.0),
                        "capexPercent": rate.get("capexPercent", 0.0),
                        "startValidity": rate.get("startValidity", "N/A"),
                        "endValidity": rate.get("endValidity", "N/A"),
                    }
                    for rate in response.get("rates", [])
                ]
                logger.debug(f"Batch created rates: {rate_details}")
                results.append({"message": response.get("message", ""), "rates": rate_details})

            except requests.exceptions.RequestException as e:
                logger.error(f"HTTP error during rate creation: {e}")
                results.append({"error": f"HTTP error: {str(e)}"})

            except Exception as e:
                logger.error(f"Unexpected error during rate creation: {e}")
                results.append({"error": f"Unexpected error: {str(e)}"})

        return {"message": "Rate creation completed.", "results": results}

    def update_rates(self, rates: List[Dict[str, Any]], pillar: str) -> Dict[str, Any]:
        """
        Updates rates based on the given list of rates.
        """
        for rate in rates:
            # Ensure startValidity and endValidity are valid ISO 8601
            if "startValidity" in rate and not rate["startValidity"].endswith("Z"):
                rate["startValidity"] = rate["startValidity"] + "Z"
            if "endValidity" in rate and not rate["endValidity"].endswith("Z"):
                rate["endValidity"] = rate["endValidity"] + "Z"
            rate["consumerID"] = str(rate["consumerID"])

        # Send the PATCH request
        self._make_request("PATCH", "/rates", pillar, json=rates)

    def delete_rates(self, rate_ids: List[str], pillar: str) -> Dict[str, Any]:
        """
        Deletes rates based on the given rate IDs.
        """
        if not rate_ids:
            raise ValueError("rate_ids cannot be empty for deleting rates.")

        # Construct the payload
        payload = rate_ids

        # Log the request details
        logger.debug(f"Deleting rates with payload: {payload}")

        try:
            # Make the DELETE request
            response = self._make_request("DELETE", "/rates", pillar, json=payload)
            logger.debug(f"Deleted rates: {response}")
            return {"message": response.get("message", "Rates deleted successfully.")}
        except requests.exceptions.HTTPError as e:
            # Parse the error response
            if e.response is not None:
                try:
                    e.response.json()
                    if e.response.status_code == 400:
                        rate_id = rate_ids[0]  # Assuming only one rate ID is passed
                        logger.warning(f"Rate ID doesn't exist: {rate_id}")
                        return {"message": f"Rate ID doesn't exist {rate_id}"}
                except ValueError:
                    pass
            logger.error(f"Failed to delete rates: {e}")
            raise

    def validate_payload(self, payload: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate and fix payload entries before submission.
        Updates startValidity to ensure it is in the future.
        """
        validated_payload = []
        current_time_utc = datetime.utcnow()

        for entry in payload:
            # Validate startValidity and adjust if necessary
            if "startValidity" in entry:
                start_validity_dt = datetime.strptime(entry["startValidity"], "%Y-%m-%dT%H:%M:%SZ")
                if start_validity_dt <= current_time_utc:
                    adjusted_start_validity = (current_time_utc + timedelta(minutes=10)).strftime("%Y-%m-%dT%H:%M:%SZ")
                    logging.warning(
                        f"Adjusting startValidity for entry: {entry['startValidity']} -> {adjusted_start_validity}"
                    )
                    entry["startValidity"] = adjusted_start_validity

            # Other validations (if needed)
            if "consumerID" in entry and not isinstance(entry["consumerID"], str):
                entry["consumerID"] = str(entry["consumerID"])

            validated_payload.append(entry)

        logging.debug(f"Validated payload with {len(validated_payload)} entries.")
        return validated_payload

    def add_usages(self, usages: List[Dict[str, Any]], pillar: str, batch_size: int = 20) -> Dict[str, Any]:
        """
        Adds usages in batches with proper validations.
        """
        # Define the consumerIDs to skip usage submission
        skip_consumer_ids = {"104409", "103612"}
        formatted_usages = []

        for usage in usages:
            try:
                # Skip usages for specific consumerIDs
                if str(usage.get("consumerID")) in skip_consumer_ids:
                    logger.debug(f"Skipping usage submission for consumerID: {usage['consumerID']}")
                    continue

                # Skip usages with zero or negative quantity
                if usage.get("quantity", 0) <= 0:
                    logger.debug(f"Skipping usage with non-positive quantity: {usage}")
                    continue

                # Ensure consumerID is a string
                usage["consumerID"] = str(usage["consumerID"])

                formatted_usage = {
                    "txID": usage.get("txID", self._generate_short_ksuid()),
                    "resourceID": usage["resourceID"],
                    "consumerID": usage["consumerID"],
                    "partner": usage.get("partner", "comcast"),
                    "quantity": usage["quantity"],
                }
                formatted_usages.append(formatted_usage)

            except KeyError as ke:
                logger.error(f"Missing required field in usage: {ke}")
            except Exception as e:
                logger.error(f"Unexpected error during usage validation: {e}")

        results = []

        for i in range(0, len(formatted_usages), batch_size):
            batch = formatted_usages[i:i + batch_size]
            if not self.validate_payload(batch):
                logger.error(f"Invalid payload: {json.dumps(batch, indent=2)}")
                continue

            logger.info(f"Submitting batch with {len(batch)} items.")
            try:
                # Serialize the payload as JSON
                response = self._make_request("PUT", "/usages?debug=true", pillar, json=batch)
                logger.debug(f"Batch response: {response}")
                results.append(response)
            except requests.exceptions.RequestException as e:
                logger.error(f"HTTP error during batch submission: {e}")
                results.append({"error": f"HTTP error: {str(e)}"})
            except Exception as e:
                logger.error(f"Unexpected error during batch submission: {e}")
                results.append({"error": f"Unexpected error: {str(e)}"})

        return {"message": "Usages submission completed.", "results": results}

    def _parse_response(self, response: Dict[str, Any], key: str) -> Dict[str, Any]:
        try:
            details = [
                {k: v for k, v in res.items() if k in ["providerID", "unit", "resourceID"]}
                for res in response.get(key, [])
            ]
            return {"message": response.get("message", ""), key: details}
        except KeyError as e:
            logger.error(f"Failed to parse {key} details from response: {e}")
            raise ValueError(f"Invalid response format for {key} operation.")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Showback API Client")
    # parser.add_argument("function", choices=["resource", "rate", "usage"], help="API function to test")
    # parser.add_argument("action", choices=["add", "create", "update", "list", "delete"], help="Action to perform")
    # # Make `function` and `action` optional unless `--generate-txid` is not provided
    parser.add_argument("function", choices=["resource", "rate", "usage"], nargs="?", help="API function to test")
    parser.add_argument("action", choices=["add", "create", "update", "list", "delete"], nargs="?", help="Action to perform")

    parser.add_argument("--pillar", choices=["metrix", "visualization"], help="Specify the pillar")
    parser.add_argument("--providerID", help="Provider ID for the resource or rate")
    parser.add_argument("--unit", help="Unit for the resource")
    parser.add_argument("--resourceID", help="Resource ID for update, list, or delete")
    parser.add_argument("--rateID", help="Rate ID for update, list, or delete")
    parser.add_argument("--consumerID", help="Consumer ID for the rate")
    parser.add_argument("--costPerUnit", type=float, help="Cost per unit for the rate")
    parser.add_argument("--capexPercent", type=float, help="Capex percent for the rate (0-1)")
    parser.add_argument("--startValidity", help="Start validity for the rate (ISO 8601 format)")
    parser.add_argument("--endValidity", help="End validity for the rate (ISO 8601 format)")
    parser.add_argument("--quantity", type=float, help="Quantity for the usage")
    parser.add_argument("--timestamp", help="Timestamp for the usage (ISO 8601 format)")
    parser.add_argument("--generate-txid", action="store_true", help="Generate and print a txID")

    args = parser.parse_args()

    client = ShowbackClient()

    # Check if --generate-txid is provided
    if args.generate_txid:
        client = ShowbackClient()
        tx_id = client._generate_short_ksuid()
        print(f"Generated txID: {tx_id}")
        sys.exit(0)

    # Validate required arguments if not generating txID
    if not args.pillar:
        parser.error("--pillar is required unless --generate-txid is used.")

    try:
        if args.function == "resource":
            if args.action == "create":
                if not args.providerID or not args.unit:
                    raise ValueError("providerID and unit are required for creating a resource.")
                resources = [{"providerID": args.providerID, "unit": args.unit}]
                result = client.create_resources(resources, pillar=args.pillar)
                logging.debug("Resource created:", result)

            elif args.action == "list":
                result = client.list_resources(
                    providerID=args.providerID,
                    resource_ids=[args.resourceID] if args.resourceID else None,
                    pillar=args.pillar
                )
                # Check if the result contains the 'message' key
                if result.get("message"):
                    raw_content = json.dumps(result)
                    cleaned_content = raw_content.replace("'", '"')
                    parsed_data = json.loads(cleaned_content)
                    formatted_json = json.dumps(parsed_data['resources'], indent=4)
                    print(f"\bResource:\n {formatted_json}")
                else:
                    print(f"\bResource:\n {result}")
                    # logging.info("Resources Rates:", result)

            elif args.action == "update":
                if not args.resourceID or not args.providerID or not args.unit:
                    raise ValueError("resourceID, providerID, and unit are required for updating a resource.")
                resources = ResourcesV1PatchBulkRequest(resources=[
                    {
                        "resourceID": args.resourceID,
                        "providerID": args.providerID,
                        "unit": args.unit
                    }
                ])
                result = client.update_resources(resources, pillar=args.pillar)
                print("\nResource updated:\n", result)
                # logging.info("Resource updated:", result)

            elif args.action == "delete":
                if not args.resourceID:
                    raise ValueError("resourceID is required for deleting a resource.")
                result = client.delete_resources([args.resourceID], pillar=args.pillar)
                print("\nResource deleted:\n", result)
                # logging.info("Resource deleted:", result)

        elif args.function == "rate":
            if args.action == "create":
                logging.debug(f"\nOptions: {args}\n")
                if not (args.resourceID and args.costPerUnit and args.startValidity and args.endValidity):
                    raise ValueError("resourceID, costPerUnit, startValidity, and endValidity are required for creating a rate.")
                rate = {
                    "resourceID": args.resourceID,
                    "partner": "comcast",
                    "costPerUnit": args.costPerUnit,
                    "costCurrencyCode": "USD",
                    "capexPercent": args.capexPercent,
                    "startValidity": args.startValidity,
                    "endValidity": args.endValidity
                }
                    # Include consumerID if provided
                if args.consumerID:
                    rate["consumerID"] = str(args.consumerID)

                rates = [rate]
                result = client.create_rates(rates, pillar=args.pillar)
                print("\nRate created:\n", result)
                # logging.info("Rate created:", result)

            elif args.action == "list":
                result = client.list_rates(
                    providerID=args.providerID,
                    rate_ids=[args.rateID] if args.rateID else None,
                    pillar=args.pillar
                )
                # Apply additional filter for consumerID if provided
                if args.consumerID:
                    filtered_rates = [
                        rate for rate in result.get("rates", [])
                        if str(rate.get("consumerID")) == str(args.consumerID)
                    ]
                    result["rates"] = filtered_rates

                if result.get("message"):
                    raw_content = json.dumps(result)
                    cleaned_content = raw_content.replace("'", '"')
                    parsed_data = json.loads(cleaned_content)
                    formatted_json = json.dumps(parsed_data['rates'], indent=4)
                    print(f"\bRates:\n {formatted_json}")
                else:
                    print(f"\bNo rates found\n {result}")

            elif args.action == "update":
                if not (args.rateID and args.resourceID and args.consumerID and args.costPerUnit and args.capexPercent and args.startValidity and args.endValidity):
                    raise ValueError("All parameters are required for updating a rate.")
                rates = [{
                    "rateID": args.rateID,
                    "resourceID": args.resourceID,
                    "partner": "comcast",
                    "consumerID": args.consumerID,
                    "costPerUnit": args.costPerUnit,
                    "costCurrencyCode": "USD",
                    "capexPercent": args.capexPercent,
                    "startValidity": args.startValidity,
                    "endValidity": args.endValidity
                }]
                result = client.update_rates(rates, pillar=args.pillar)
                print("\nRate updated:\n", result)

            elif args.action == "delete":
                if not args.rateID:
                    raise ValueError("rateID is required for deleting a rate.")
                result = client.delete_rates([args.rateID], pillar=args.pillar)
                if "message" in result:
                    print(f"\n{result['message']}")
                else:
                    print(f"\n{result}")

        elif args.function == "usage":
            if args.action == "add":
                if not (args.resourceID and args.consumerID and args.quantity):
                    raise ValueError("All parameters (resourceID, consumerID, quantity) are required for adding a usage.")
                usages = [{
                    "resourceID": args.resourceID,
                    "consumerID": args.consumerID,
                    "quantity": args.quantity
                }]

                try:
                    result = client.add_usages(usages, pillar=args.pillar)
                    if "message" in result:
                        # logging.debug(result["message"])
                        print(f"\n{result['message']}")
                    else:
                        # logging.info("Usage added successfully:", result)
                        print(f"\n{result['message']}")
                except Exception as e:
                    logging.error(f"Error while adding usage: {e}")

    except Exception as e:
        logging.error(f"Error: {e}")