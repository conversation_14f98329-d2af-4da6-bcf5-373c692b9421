import configparser
import jwcrypto.jwt as jwcrypto_jwt
import logging
import logging.config
import os
import time

from jwcrypto import jwk

from src.util.retry_adapter import RetryAdapter
from src.util.exceptions import parse_exception_args


config = configparser.ConfigParser()
config.read('config/backends.ini')
logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

env = os.environ.get('PLATFORMENV', 'local')
ocp_proxy = os.environ.get('API_PROXY')

proxies = {'https': ocp_proxy}
retry_adapter = RetryAdapter()


class GHClient:
    def __init__(self):
        try:
            self.app_id = os.environ.get('git_app_id')
            self.private_key_file = (
                os.environ.get('OCP_GIT_KEY')
                if env == 'rdei'
                else os.environ.get('ocp_git_key')
            )
            self.installation_id = os.environ.get('git_installation_id')
            tokens = self.read_tokens_disk()
            self.inst_access_token = tokens['access']
            self.token_expiry = tokens['expiry']
            self.base_url = "https://api.github.com"
            self.org_name = config.get('git', 'TENANT_ORG')
        except Exception as e:
            logger.exception("Initialization failed: %s", e)
            raise Exception("GitHub Client Initialization failed")

    def generate_jwt(self):
        try:
            private_key = None
            self.private_key_file = self.private_key_file.replace('\\n', '\n')
            with open('gitkeyfile', 'w') as f:
                f.write(self.private_key_file)
            with open('gitkeyfile', 'rb') as f:
                private_key = jwk.JWK.from_pem(f.read())

            now = int(time.time())
            payload = {
                "iat": now,
                "exp": now + (10 * 60),
                "iss": self.app_id
            }

            token = jwcrypto_jwt.JWT(header={"alg": "RS256"}, claims=payload)
            token.make_signed_token(private_key)
            return token.serialize()
        except Exception as e:
            logger.exception("JWT generation failed: %s", e)
            raise Exception("JWT generation failed")

    def get_installation_access_token(self):
        try:
            inst_id = self.installation_id
            url = f"{self.base_url}/app/installations/{inst_id}/access_tokens"
            headers = {
                "Authorization": f"Bearer {self.generate_jwt()}",
                "Accept": "application/vnd.github.v3+json"
            }
            response = retry_adapter.post(url,
                                          headers=headers,
                                          proxies=proxies)

            expiry = int(time.time()) + 3600
            self.write_tokens_disk(response.json()['token'], expiry)
            self.inst_access_token = response.json()['token']
            self.token_expiry = expiry
        except Exception as e:
            logger.exception("Failed to retrieve Git installation access "
                             f"token: {e=}")
            raise Exception("Failed to retrieve Git installation access token")

    def read_tokens_disk(self):
        try:
            with open('gittokens', 'r') as t_file:
                tokens = t_file.readlines()
            access = tokens[0].split("=")[1][:-1]
            expiry = int(tokens[1].split("=")[1])
            return {"access": access, "expiry": expiry}
        except Exception as e:
            logger.debug(f"Reading tokens from disk failed: {e=}")
            return {"access": "", "expiry": 0}

    def write_tokens_disk(self, access, expiry):
        try:
            with open('gittokens', 'w') as t_file:
                t_file.writelines([
                    f"access={access}\n",
                    f"access_expiry={expiry}\n"
                ])
        except Exception as e:
            logger.warning("Writing tokens to disk failed: %s", e)

    def ensure_token(self):
        """Ensure that the git access \
            token is valid before making API requests."""
        # Only generate a new token if the current token is expired
        if self.token_expiry <= int(time.time()):
            logger.info("GitHub access token expired or missing."
                        "Generating a new token.")
            self.get_installation_access_token()
        else:
            logger.info("Using existing GitHub access token."
                        "Expiry is still valid.")

    def cleanup_tenant_repo(self, tenant_slug):
        repo_name = tenant_slug
        self.delete_repo(repo_name)

    def get_repo(self, repo_name):
        if self.token_expiry <= int(time.time()):
            self.get_installation_access_token()

        headers = {"Authorization": f"Bearer {self.inst_access_token}",
                   "Accept": "application/vnd.github.v3+json"}

        url = f"{self.base_url}/repos/{self.org_name}/{repo_name}"

        try:
            resp = retry_adapter.get(url, headers=headers,
                                     proxies=proxies, timeout=20)
        except Exception as e:
            obj = parse_exception_args(e)
            logger.error(f"Failed to get repo '{repo_name}': {e=}")
            raise Exception({"message": f"Failed to get repo '{repo_name}': "
                             f"{obj['status']}: '{obj['error']}'",
                             "status": obj['status']})

        return resp.json()

    def repo_exists(self, repo_name):
        try:
            repo_obj = self.get_repo(repo_name)
            logger.info(f"Found repo {repo_obj['name']}")
        except Exception as e:
            obj = parse_exception_args(e)
            if obj['status'] == 404:
                return False
            else:
                raise
        return True

    def provision_tenant_repo(self, repo_name, tenant_apps):

        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()

            url = f'{self.base_url}/orgs/{self.org_name}/repos'
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }

            devhub_id = None
            if len(tenant_apps) > 0:
                devhub_id = tenant_apps[0]

            data = {
                "name": repo_name,
                "description": "Observability configuration repository",
                "private": True,
                "auto_init": True,
                "has_issues": False,
                "has_projects": False,
                "has_wiki": False,
                "allow_squash_merge": True,
                "allow_merge_commit": False,
                "allow_rebase_merge": False,
                "delete_branch_on_merge": True,
                "allow_auto_merge": False,
                "custom_properties": {
                    "DevHub-App-ID": str(devhub_id)
                }
            }

            if self.repo_exists(repo_name):
                logger.info(f"Repo '{repo_name}' already exists, updating "
                            "repo settings...")
                del data['name']
                del data['custom_properties']
                del data['auto_init']

                try:
                    url = f"{self.base_url}/repos/{self.org_name}/{repo_name}"
                    retry_adapter.post(url, headers=headers, json=data,
                                       proxies=proxies, timeout=20)
                    if devhub_id:
                        self.add_custom_property_to_repo(self.org_name,
                                                         repo_name,
                                                         devhub_id)
                    logger.info(f"Settings updated for repo '{repo_name}'")
                except Exception as e:
                    logger.error("Failed to update settings for repo "
                                 f"'{repo_name}': {e=}")
            else:
                logger.info(f"Creating repository with payload: {data}")
                retry_adapter.post(url,
                                   headers=headers,
                                   json=data,
                                   proxies=proxies,
                                   timeout=20)

                logger.info(f"Successfully created repository '{repo_name}'")

            # Add admin team to the repository
            self.add_team_to_repo(repo_name, "admin", "admin")

            # Configure branch protection
            self.configure_branch_protection(self.org_name, repo_name)

            return {"repo_name": repo_name,
                    "repo_url": f"https://github.com/{self.org_name}/"
                    "{repo_name}",
                    "repo_team": f"{repo_name}_owners"}
        except Exception as e:
            logger.exception(f"Failed to create repository '{repo_name}': "
                             f"{e=}")
            raise Exception(f"Failed to create repository {repo_name}")

    def add_custom_property_to_repo(self, org, repo_name, devhub_id):
        try:
            url = f'{self.base_url}/repos/{org}/{repo_name}/properties/values'
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            data = {
                "properties": [
                    {
                        "DevHub-App-ID": devhub_id
                    }
                ]
            }
            retry_adapter.patch(url, headers=headers,
                                json=data, proxies=proxies,
                                timeout=20)
            logger.info("Added custom property 'DevHub-App-ID' to "
                        f"repository '{repo_name}'")
        except Exception as e:
            logger.exception("Failed to add custom property to repository "
                             f"'{repo_name}': {e=}")
            raise Exception("Failed to add custom property to repository "
                            f"{repo_name}")

    def configure_branch_protection(self, org, repo_name):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()

            url = (
                f"{self.base_url}/repos/{org}/{repo_name}"
                f"/branches/main/protection"
            )
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.luke-cage-preview+json"
            }
            data = {
                "required_pull_request_reviews": {
                    "dismissal_restrictions": {
                        "teams": ["admin"]
                    },
                    "dismiss_stale_reviews": True,
                    "require_code_owner_reviews": False,
                    "required_approving_review_count": 1
                },
                "enforce_admins": False,
                "restrictions": {
                    "users": [],
                    "teams": [f"{repo_name}_owners"]
                },
                "required_conversation_resolution": False,
                "block_creations": False,
                "required_signatures": False,
                "required_linear_history": False,
                "allow_force_pushes": False,
                "allow_deletions": False,
                "required_status_checks": {
                    "strict": True,
                    "contexts": []
                }
            }
            retry_adapter.put(url, headers=headers,
                              json=data, proxies=proxies,
                              timeout=20)

            logger.info("Branch protection configured for repository "
                        f"'{repo_name}'")
        except Exception as e:
            logger.exception("Failed to configure branch protection for "
                             f"'{repo_name}': {e=}")
            raise Exception("Failed to configure branch protection for "
                            f"'{repo_name}'")

    def add_team_to_repo(self, repo_name, team_name, permission):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()

            url = (
                f"{self.base_url}/orgs/{self.org_name}/teams/{team_name}"
                f"/repos/{self.org_name}/{repo_name}"
            )
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            data = {
                "permission": permission
            }
            retry_adapter.put(url, headers=headers,
                              json=data, proxies=proxies,
                              timeout=20)

            logger.info(f"Team '{team_name}' successfully added to repo "
                        f"'{repo_name}' with '{permission}' permissions")
        except Exception as e:
            logger.exception(f"Failed to add team '{team_name}' to repo "
                             f"'{repo_name}': {e=}")
            raise Exception(f"Failed to add team '{team_name}' to repo "
                            f"'{repo_name}'")

    def cleanup_repo_team(self, repo_name):
        # self.delete_team(team_name)
        self.delete_team(repo_name)

    def provision_repo_team(self, repo_name):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()

            team_name = f"{repo_name}_owners"
            permission = 'maintain'

            url = f"{self.base_url}/orgs/{self.org_name}/teams"
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            data = {
                "name": team_name,
                "repo_names": [repo_name],
                "privacy": "closed"
            }
            response = retry_adapter.post(url,
                                          headers=headers,
                                          json=data,
                                          proxies=proxies,
                                          timeout=20)

            team = response.json()
            team_slug = team['slug']
            self.add_team_to_repo(repo_name, team_slug, permission)

            return team
        except Exception as e:
            logger.exception("Failed to create and assign team "
                             f"'{repo_name}': {e=}")
            raise Exception("Failed to create and assign team "
                            f"'{repo_name}_owners'")

    def add_members_to_repo_team(self, repo_name, users):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }

            non_org_members = []
            for user in users:
                guser = f"{user}_comcast"
                url = (
                    f"{self.base_url}/orgs/{self.org_name}"
                    f"/memberships/{guser}"
                )
                try:
                    resp = retry_adapter.get(url,
                                             headers=headers,
                                             proxies=proxies,
                                             timeout=20)
                    if resp.status_code == 404:
                        non_org_members.append(guser)
                except Exception as e:
                    logger.error("Failed to pull org membership for user, "
                                 f"'{user}': {e=}")
                    non_org_members.append(guser)

            if non_org_members:
                for user in non_org_members:
                    url = (
                        f"{self.base_url}/orgs/{self.org_name}"
                        f"/memberships/{user}"
                    )
                    data = {"role": "member"}
                    try:
                        retry_adapter.put(url,
                                          headers=headers,
                                          json=data,
                                          proxies=proxies,
                                          timeout=20)
                    except Exception as e:
                        msg = "Failed to add user to org"
                        if e.args[0]['status'] == 404:
                            msg += (
                                f", GHEC account not "
                                f"found for user '{user}'"
                            )
                        logger.error(f"{msg}: {e=}")

                time.sleep(2)

            successful_adds = []
            for user in users:
                guser = f"{user}_comcast"
                url = (
                    f"{self.base_url}/orgs/{self.org_name}/teams/"
                    f"{repo_name}_owners/memberships/{guser}"
                )
                data = {"role": "member"}
                try:
                    retry_adapter.put(url,
                                      headers=headers,
                                      json=data,
                                      proxies=proxies,
                                      timeout=20)
                    logger.info(f"Added membership, '{user}_comcast' to team "
                                f"'{repo_name}_owners'")
                    successful_adds.append(guser)
                except Exception as e:
                    logger.exception(f"Failed to add user, '{guser}', to team"
                                     f", '{repo_name}_owners': {e=}")

            logger.info(f"Users, {[x for x in successful_adds]}, added to "
                        f"team, '{repo_name}_owners'")
        except Exception as e:
            logger.exception("Failed to add members to team "
                             f"'{repo_name}_owners': {e=}")
            raise Exception(f"Failed to add members to team {repo_name}")

    def remove_members_from_repo_team(self, repo_name, users):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }

            successful_removes = []
            url = (
                f"{self.base_url}/orgs/{self.org_name}"
                f"/teams/{repo_name}_owners/memberships"
            )
            for user in users:
                guser = f"{user}_comcast"
                try:
                    retry_adapter.delete(
                        f"{url}/{guser}",
                        headers=headers,
                        proxies=proxies,
                        timeout=20
                    )
                    logger.info(f"Removed membership, '{user}_comcast', from"
                                f" team '{repo_name}_owners'")
                    successful_removes.append(guser)
                except Exception as e:
                    if '404' in str(e):
                        try:
                            retry_adapter.delete(f"{url}/{guser}",
                                                 headers=headers,
                                                 proxies=proxies,
                                                 timeout=20)
                            logger.info(f"Removed membership, "
                                        f"'{user}_comcast', from team "
                                        f"'{repo_name}-owners'")
                            successful_removes.append(guser)
                        except Exception as e:
                            logger.error("Remove team membership call "
                                         f"failed: {e=}")
                    else:
                        logger.error("Remove team membership call failed: "
                                     f"{e=}")

            logger.info(f"Users, {[x for x in successful_removes]}, removed "
                        f"from team, '{repo_name}_owners'")
        except Exception as e:
            logger.exception("Failed to remove members from team "
                             f"'{repo_name}_owners': {e}")
            raise Exception(f"Failed to remove members from team {repo_name}")

    def get_repo_team_members(self, repo_name):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }

            url = (
                f"{self.base_url}/orgs/{self.org_name}"
                f"/teams/{repo_name}_owners/members"
            )
            try:
                resp = retry_adapter.get(url,
                                         headers=headers,
                                         proxies=proxies,
                                         timeout=20)
            except Exception as e:
                if '404' in str(e):
                    try:
                        resp = retry_adapter.get(
                            f"{url}/{repo_name}-owners/members",
                            headers=headers,
                            proxies=proxies,
                            timeout=20
                        )
                    except Exception as e:
                        logger.error(f"Get team members call failed: {e=}")
                        raise Exception({"message": "Get team members call "
                                         "failed"})
                else:
                    logger.error(f"Get team members call failed: {e=}")
                    raise Exception({"message": "Get team members call "
                                     "failed"})

            members = [x['login'] for x in resp.json()]
            return members
        except Exception as e:
            logger.exception("Failed to get team members for repo "
                             f"'{repo_name}': {e=}")
            raise Exception("Failed to get team members for repo "
                            f"'{repo_name}'")

    def rename_tenant_repo(self, old_repo_name, new_repo_name):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()

            url = f'{self.base_url}/repos/{self.org_name}/{old_repo_name}'
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            data = {"name": new_repo_name}
            retry_adapter.patch(url, headers=headers,
                                json=data, proxies=proxies,
                                timeout=20)

            logger.info(f"Successfully renamed repository '{old_repo_name}'"
                        f" to '{new_repo_name}'")
            return 1
        except Exception as e:
            logger.exception("Failed to rename repository '{old_repo_name}' "
                             f"to '{new_repo_name}': {e=}")
            return 0
            # TODO - rework error handling using exeptions instead of bools

    def rename_repo_team(self, old_team_name, new_team_name):
        try:
            if self.token_expiry <= int(time.time()):
                self.get_installation_access_token()

            url = (
                f"{self.base_url}/orgs/{self.org_name}"
                f"/teams/{old_team_name}_owners"
            )
            headers = {
                "Authorization": f"Bearer {self.inst_access_token}",
                "Accept": "application/vnd.github.v3+json"
            }
            data = {"name": new_team_name + "_owners"}
            retry_adapter.patch(url, headers=headers,
                                json=data, proxies=proxies,
                                timeout=15)

            logger.info(f"Successfully renamed team '{old_team_name}_owners'"
                        f"to '{new_team_name}_owners'")
            return 1
        except Exception as e:
            logger.exception("Failed to rename team '{old_team_name}_owners' "
                             f"to '{new_team_name}_owners': {e=}")
            return 0
            # TODO - rework error handling using exeptions instead of bools

    def delete_repo(self, repo_name: str):
        """
        Delete a GitHub repository.

        :param repo_name: The name of the repository to delete.
        :raises: Exception if the deletion fails.
        """
        self.ensure_token()  # Ensure valid token before proceeding

        headers = {
            "Authorization": f"Bearer {self.inst_access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        url = f"{self.base_url}/repos/{self.org_name}/{repo_name}"
        # TODO - wrap call in try block
        response = retry_adapter.delete(url, headers=headers,
                                        proxies=proxies, timeout=15)

        if response.status_code != 204:
            raise Exception(f"Failed to delete repository {repo_name}: "
                            f"{response.text}")

        return {"status": "success", "message": f"Repository {repo_name} "
                "deleted successfully"}

    def delete_team(self, repo_name: str):
        """
        Delete a GitHub team.

        :param team_name: The name of the team to delete.
        :raises: Exception if the deletion fails.
        """

        self.ensure_token()  # Ensure valid token before proceeding

        team_name = f"{repo_name}_owners"
        headers = {
            "Authorization": f"Bearer {self.inst_access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        url = f"{self.base_url}/orgs/{self.org_name}"\
              + f"/teams/{team_name}"
        try:
            retry_adapter.delete(url,
                                 headers=headers,
                                 proxies=proxies,
                                 timeout=15)
        except Exception as e:
            if '404' in str(e):
                logger.error("Failed to find team with '_' convention, "
                             "trying '-' convention...")
                try:
                    team_name = f"{repo_name}-owners"
                    url = f"{self.base_url}/orgs/{self.org_name}"\
                        + f"/teams/{team_name}"
                    retry_adapter.delete(url,
                                         headers=headers,
                                         proxies=proxies,
                                         timeout=15)
                except Exception as e:
                    msg = f"Delete team call failed: {e=}"
                    logger.error(msg)
            else:
                logger.error("Failed to delete team for repo, "
                             f"'{repo_name}': {e=}")

            raise Exception(f"Failed to delete team for repo '{repo_name}', "
                            f"'{team_name}'")

        return {"status": "success",
                "message": f"Team {team_name} deleted successfully"}
