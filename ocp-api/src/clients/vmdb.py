import logging
import logging.config
import os
import snappy
import sys

sys.path.extend("./src")
from src.library.protobuf.remote_pb2 import WriteRequest
from src.util.retry_adapter import RetryAdapter

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

retry_adp = RetryAdapter()
env = os.environ.get("ENV")
deployenv = os.environ.get("DEPLOYENV")
default_write_urls = ["https://ingress-rdei-ashburn-cct-01.monitoring.comcast.net/",
                      "https://ingress-rdei-northlake-cct-01.monitoring.comcast.net/"]
ocp_metrics_id = os.environ.get("OCP_METRICS_INSTANCE_ID")
ocp_metrics_user = os.environ.get("OCP_METRICS_RW_USER")
ocp_metrics_pass = os.environ.get("OCP_METRICS_RW_PASS")


class VMClient():
    def __init__(self):
        """
        Simplified VMDB client for internal gauge metrics only.
        Uses predefined endpoints and credentials for OCP metrics.
        """
        self.write_api = "/api/v1/write"

        # Validate required environment variables
        self._validate_environment_variables()

    def _validate_environment_variables(self):
        """
        Validate that all required VMDB environment variables are set.
        Provides helpful error messages for local testing.
        """
        required_vars = {
            "OCP_METRICS_INSTANCE_ID": ocp_metrics_id,
            "OCP_METRICS_RW_USER": ocp_metrics_user,
            "OCP_METRICS_RW_PASS": ocp_metrics_pass
        }

        missing_vars = []
        for var_name, var_value in required_vars.items():
            if not var_value:
                missing_vars.append(var_name)

        if missing_vars:
            error_msg = (
                f"Missing required VMDB environment variables: {', '.join(missing_vars)}\n"
                f"For local testing, ensure these are set in your .env file:\n"
                f"  OCP_METRICS_INSTANCE_ID=your-instance-id\n"
                f"  OCP_METRICS_RW_USER=your-username\n"
                f"  OCP_METRICS_RW_PASS=your-password\n"
                f"For production, ensure these are configured as Kubernetes secrets."
            )
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info(f"VMDB environment variables validated successfully. Instance ID: {ocp_metrics_id}")

    def remote_write_internal_gauge(self, metric_name, labels, value, timestamp):
        """
            Inputs:
                :metric_name: Name of gauge metric to publish to
                :labels: A dictionary representing labels for the gauge,
                :value: Value to publish
                :timestamp: Timestamp for sample being published

            NOTES:
                This method accepts inputs used to publish to internal gauges.  These
                gauges are meant to capture statistics on performance of deployments.

                By default these metrics are only published to a single VMDB instance,
                the ADE prod metrics instance, 'prod', 10de8728-cc08-410d-9386-b1459eafac8f

                When publishing to VMDB, all epoch timestamps must be in milliseconds
        """
        labels['env'] = deployenv
        labels['platform'] = env
        labels['__name__'] = metric_name

        try:
            write_request = WriteRequest()
            ts = write_request.timeseries.add()
            for l_name, l_value in labels.items():
                label = ts.labels.add()
                label.name = l_name
                label.value = str(l_value)
            sample = ts.samples.add()
            sample.value = value
            if len(str(timestamp)) < 11:
                # assume seconds and convert to milliseconds
                timestamp = timestamp * 1000
            sample.timestamp = timestamp

            data = write_request.SerializeToString()
            compressed_data = snappy.compress(data)
            headers = {'Content-Type': 'application/x-protobuf',
                       'Content-Encoding': 'snappy'}

            failed = 0
            resp = None
            for endpoint in default_write_urls:
                try:
                    url = endpoint + f"vmagent-{ocp_metrics_id}" + self.write_api
                    resp = retry_adp.post(url,
                                          data=compressed_data,
                                          headers=headers,
                                          auth=(ocp_metrics_user, ocp_metrics_pass))
                except Exception as e:
                    logger.error(f"Error writing to VMAgent, '{url}': {e=}")
                    failed += 1
                    if failed == len(default_write_urls):
                        raise e

            logger.info(f"Published sample to gauge '{labels['__name__']}'")
        except Exception as e:
            raise Exception("Failed to publish samples for gauge "
                            + f"'{labels['__name__']}': {resp.text if resp else ''}: {e=}")

    def test_connection(self):
        """
        Test VMDB connection by sending a simple test metric.
        Useful for local testing and validation.
        """
        import time

        test_labels = {
            "__name__": "o11y-showback_test_connection",
            "test": "true",
            "env": deployenv or "local",
            "platform": env or "local"
        }

        timestamp = int(time.time())

        try:
            self.remote_write_internal_gauge(
                metric_name="o11y-showback_test_connection",
                labels=test_labels,
                value=1.0,
                timestamp=timestamp
            )
            logger.info("VMDB connection test successful!")
            return True
        except Exception as e:
            logger.error(f"VMDB connection test failed: {e}")
            return False
