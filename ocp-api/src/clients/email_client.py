import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging
import logging.config
import os

# flake8: noqa

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)


class EmailClient:
    def __init__(self):
        self.relay_host = 'mailrelay.comcast.com'
        self.relay_port = 25
        self.ui_endpoint = os.environ.get('UI_ENDPOINT', 'https://ocp.comcast.net')

    def send_email(self, sender_email, receiver_email, tenant_owner,
                   tenant_name, tenant_desc, subject, tenant_slug):
        server = None
        try:
            # set the a href link endpoint
            ui_endpoint = self.ui_endpoint

            # Set up the SMTP client
            # TODO - this request can fail, configure retries for server init
            server = smtplib.SMTP(self.relay_host, self.relay_port)
            server.ehlo()

            # Create the email to send
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = sender_email
            msg['To'] = receiver_email

            # Create the HTML version of your message
            html = """\
           <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http: //www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
           <html xmlns="http: //www.w3.org/1999/xhtml">
           <head>
                   <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
               <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
               <title>Tenant Created - Observability Control Plane</title>
               <style type="text/css">
                   body {{
                 margin: 0;
                 padding: 0;
               }}
               </style>
           </head>
           <body>
             <table align="center" valign="center" role="presentation" border="0" cellpadding="0" cellspacing="0" style="margin: 0 auto; padding: 0; width: 100%; background-color: #c0c0c0; font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;">
               <tbody>
                 <tr>
                   <td>
                     <div style="background-color: #e8e8e8; max-width: 640px; margin: 0 auto 64px; padding: 0 0 12px;">
                       <h1 style="width: 100%; margin: 16px 0 0;">
                         <img src="data:image/png;base64,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" height="164" width="640" alt="DevX Accelerate: Observability Control Plane" />
                       </h1>

                       <div style="padding: 16px">
                         <div style="padding: 0 0 16px; border: 0; border-left: 0.25rem solid; border-image-slice: 1; border-image-source: linear-gradient(180deg, rgba(253,185,19,1) 16.666%, rgba(243,112,33,1) 33.333%, rgba(180,40,70,1) 49.999%, rgba(100,95,170,1) 66.666%, rgba(0,137,207,1) 83.333%, rgba(13,177,75,1) 100%); border-radius: 3px; box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2); margin: 0 0 16px; padding: 16px; background-color: #fff;">
                           <h2 style="margin: 0 0 16px; font-size: 20px; line-height: 106.2%; letter-spacing: -0.5px; color: #000000; font-weight: 500;">Hello <b>{0}</b></h2>
                           <p style="font-size: 16px; line-height: 1.6; color: #000000;">You've created a new Tenant to manage your observability services on the <a href="{4}/" style="color: #666;">DevX Accelerate: Observability Control Plane</a>.</p>

                           <div style="border-radius: 3px; border: 1px solid #c0c0c0; margin: 12px 0; padding: 16px; background-color: #fff;">
                             <h3 style="padding: 0; margin: 0 0 12px; font-size: 21px; line-height: 106.2%; letter-spacing: -0.5px; color: #000000; font-weight: 500;">New Tenant Created</h3>
                             <p><a href="{4}/tenant/{3}" 
                             style="color: #666;">{1}</a></p>
                             <p>{2}</p>
                             <table style="margin: 16px 0; width: 100%; border: 1px solid #ddd; padding: 8px">
                               <thead>
                                 <tr style="text-align: left;">
                                   <th>Roles</th><th>Count</th>
                                 </tr>
                               </thead>
                               <tbody>
                                 <tr>
                                   <td>Owners</td><td>1</td>
                                 </tr>
                                 <tr>
                                   <td>Operators</td><td>0</td>
                                 </tr>
                                 <tr>
                                   <td>Users</td><td>0</td>
                                 </tr>
                                 <tr>
                                   <td>Collaborators</td><td>0</td>
                                 </tr>
                               </tbody>
                             </table>
                           </div>

                           <div style="margin: 16px 0 16px 16px; text-align: right;">
                             <a href="{4}/" style="border: 1px solid #000000; border-radius: 0px; font-size: 12px; font-weight: 700; color: #fff; background-color: #000; padding: 8px 16px; text-decoration: none;" target="_blank">Observability Control Plane Home &rarr; </a>
                           </div>
                         </div>

                         <div style="border-radius: 3px; box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2); margin: 12px 0; padding: 16px; background-color: #fff;">
                           <h3 style="padding: 0; margin: 0 0 12px; font-size: 21px; line-height: 106.2%; letter-spacing: -0.5px; color: #000000; font-weight: 500;">Resources created for your new Tenant: </h3>
                           <ul>
                             <li>A <a href="https://comcast.grafana.net">Grafana</a> Folder named: {3}  has been created for you to visualize your observability data.</li>
                             <li><a 
                             href="https://github.com/comcast-observability-tenants/{3}" style="color: #666;">{1} GitHub Repo</a></li>
                             <li>Security Group Management Roles on <a 
                             href="https://portal.azure.com/#blade/Microsoft_AAD_IAM/GroupsList.ReactView/queryMode/filter/query/{3}" style="color: #666;">Azure Active Directory Groups</a>.</li>
                           </ul>
                         </div>

                         <div style="border-radius: 3px; box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2); margin: 12px 0; padding: 16px; background-color: #fff;">
                           <h3 style="padding: 0; margin: 0 0 12px; font-size: 21px; line-height: 106.2%; letter-spacing: -0.5px; color: #000000; font-weight: 500;">Next Steps...</h3>
                           <ol>
                             <li>Give at least one additional trusted person the role of Tenant Owner in <a href="https://portal.azure.com/" style="color: #666;">Azure Active Directory Groups</a>.</li>
                             <li>Add people to the other roles for your Tenant on <a href="https://portal.azure.com/" style="color: #666;">Azure Active Directory Groups</a>, as needed.</li>
                             <!-- <li>Add people to your Tenant's team roles on 
                             your <a 
                             href="https://github.com/comcast-observability-tenants/{3}/settings/access" style="color: #666;">GitHub Repo Manage Access</a> page.</li> -->
                             <li>Provision your first observability service on the <a href="{4}/provision" style="color: #666;">Provision</a> page.</li>
                           </ol>
                         </div>

                         <div style="border-radius: 3px; box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2); margin: 12px 0; padding: 16px; background-color: #fff;">
                           <h3 style="padding: 0; margin: 0 0 12px; font-size: 21px; line-height: 106.2%; letter-spacing: -0.5px; color: #000000; font-weight: 500;">Frequently Asked Questions</h3>
                           <dl>
                             <dt style="font-weight: 500">How does it work?</dt>
                             <dd style="margin: 0 16px 12px"><a href="https://github.comcast.com/pages/observability/control-plane-strategy/" style="color: #666;">Magic and rainbows!</a></dd>
                             <dt style="font-weight: 500">How do I provision a new instance?</dt>
                             <dd style="margin: 0 16px 12px">Tenant Owners and Operators can go to the <a href="{4}/provision" style="color: #666;">Provision New Instance</a> page and fill out the required information for the type of observability service that you'd like to provision.</dd>
                             <dt style="font-weight: 500">What are the Tenant Roles?</dt>
                             <dd style="margin: 0 16px 12px">The four types of roles are Owners, Operators, Users, and Collaborators. More information can be found on the RBAC page under <a href="https://github.comcast.com/pages/observability/control-plane-strategy/rbac/#role-definitions" style="color: #666;">Role definitions</a>.</dd>
                             <dt style="font-weight: 500">How can I reach the Control Plane Team?</dt>
                             <dd style="margin: 0 16px 12px">All of our current support channels are listed on the <a href="{4}/support" style="color: #666;">OCP Support</a> page. You can also submit feedback and feature requests through the <a href="https://forms.office.com/r/4ysHXnTayh" style="color: #666;">OCP Feedback</a> form.</dd>
                           </dl>
                           <div style="margin: 16px 0 16px 16px; text-align: right;">
                             <a href="https://github.comcast.com/pages/observability/control-plane-strategy/" style="border: 1px solid #000000; border-radius: 0px; font-size: 12px; font-weight: 700; color: #fff; background-color: #000; padding: 8px 16px; text-decoration: none;" target="_blank">More FAQs...</a>
                           </div>
                         </div>
                       </div>
                       <ul style="font-size: 12px; font-weight: 500; list-style: none; display: flex; padding: 0; justify-content: center;">
                         <li style="display: inline-block; padding: 12px;"><a href="https://github.comcast.com/pages/observability/control-plane-strategy/" style="color: #666;">OCP Tenant Documentation</a></li>
                         <li style="display: inline-block; padding: 12px;"><a 
                         href="{4}/support" style="color: 
                         #666;">OCP Support Channels</a></li>
                         <li style="display: inline-block; padding: 12px;"><a href="https://forms.office.com/r/4ysHXnTayh" style="color: #666;">OCP Feedback</a></li>
                       </ul>
                     </div>
                   </td>
                 </tr>
               </tbody>
             </table>
           </body>
           </html>
           """.format(tenant_owner, tenant_name, tenant_desc, tenant_slug, ui_endpoint)
            html_body = MIMEText(html, 'html')

            # Add the HTML part to the msg
            msg.attach(html_body)

            # Send the email
            server.sendmail(sender_email, receiver_email, msg.as_string())
            logger.info(f"New Tenant email sent to user")
        except Exception as e:
            logger.error(f"Error: {e=}")
        finally:
            if server:
                server.quit()
