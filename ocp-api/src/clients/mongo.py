import configparser
import fastapi
import logging
import logging.config
import os
import time

from bson.objectid import ObjectId
from fastapi import HTTPException  # noqa: F401
from pymongo import MongoClient, errors

from src.clients import actived

env = os.environ.get('PLATFORMENV', 'local')

config = configparser.ConfigParser()
config.read('config/db.ini')

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)


class OCPMongoClient:
    def __init__(self):
        self.db_name = config.get('ocp-mongo', 'DBNAME')
        self.user = os.environ.get('ocp_mongo_user')
        self.passw = os.environ.get('ocp_mongo_pass')
        self.replica_set = os.environ.get('MONGO_RS', None)
        self.mtls = True
        self.minvcrt = True
        self.uri = self._get_uri()
        self.adc = actived.ADClient()

        if not self.uri:
            msg = "Failed to set DB URI, URI must be set at runtime"
            logger.exception(msg)
            raise Exception({"message": f"Error: {msg}"})

        # As of Mongo 7.0, by default clients are required to present the CA
        # and certkey files to validate the server's cert
        # *** this requirement is disabled with server option:
        #  --tlsAllowConnectionsWithoutCertificates
        #
        # if env == 'local':
        #     self.tls_ca_file = 'certs/localhost/rootCA.pem'
        #     self.tls_certkey_file = 'certs/localhost/localhost-certkey.pem'
        # elif env == 'docker':
        #     self.tls_ca_file = '/run/secrets/ssl_ca_file'
        #     self.tls_certkey_file = '/run/secrets/ssl_certkey_file'
        # else:
        #     self.tls_ca_file = 'certs/rootCA.pem'
        #     self.tls_certkey_file = 'certs/certkey.pem'

        # Create the initial connection to MongoDB
        self._conn = self._create_connection()

    def _get_uri(self):
        # Determine the URI based on the environment
        if env == 'local':
            return config.get('ocp-mongo', 'URI-LOCAL')
        elif env == 'docker':
            return config.get('ocp-mongo', 'URI-DOCKER')
        elif env == 'concourse':
            return config.get('ocp-mongo', 'URI-CONCOURSE')
        else:
            self.db_name = os.environ.get('MONGO_DBNAME')
            return os.environ.get('MONGO_URI')

    def _create_connection(self):
        # Create a MongoDB connection
        try:
            return MongoClient(
                f"{self.uri}/{self.db_name}",
                username=self.user,
                password=self.passw,
                authSource=self.db_name,
                tls=self.mtls,
                # tlsCertificateKeyFile=self.tls_certkey_file,
                # tlsCAFile=self.tls_ca_file,
                tlsAllowInvalidCertificates=self.minvcrt,
                replicaset=self.replica_set
            )
        except errors.PyMongoError as e:
            logger.error(f"Error creating MongoDB connection: {e=}")
            raise

    def _open_conn(self):
        self._conn = self._create_connection()

    def _close_conn(self):
        try:
            self._conn.close()
        except errors.PyMongoError as e:
            logger.error(f"Error closing MongoDB connection: {e=}")
            raise


class BaseCollection(OCPMongoClient):
    def __init__(self, collection_name):
        super().__init__()
        self.collection = self._conn[self.db_name][collection_name]

    def insert_one(self, doc):
        # Insert a single document, raise error if it exists
        try:
            if self.collection.find_one(doc):
                raise ValueError("Document already exists")
            return self.collection.insert_one(doc)
        except errors.PyMongoError as e:
            logger.error(f"Error inserting one document: {e}")
            raise

    def insert_many(self, docs):
        # Insert multiple documents, raise error if any exist
        try:
            if self.collection.count_documents({"$or": docs}) > 0:
                raise ValueError("Some documents already exist")
            return self.collection.insert_many(docs)
        except errors.PyMongoError as e:
            logger.error(f"Error inserting many documents: {e}")
            raise

    def find_one(self, query, projection=None):
        # Find a single document matching the query
        try:
            return self.collection.find_one(query, projection)
        except errors.PyMongoError as e:
            logger.error(f"Error finding one document: {e}")
            raise

    def find_many(self, query, projection=None):
        # Find multiple documents matching the query
        try:
            return list(self.collection.find(query, projection))
        except errors.PyMongoError as e:
            logger.error(f"Error finding many documents: {e}")
            raise

    def update_one(self, query, new_vals):
        # Update a single document, raise error if it doesn't exist
        try:
            if not self.collection.find_one(query):
                raise ValueError("Document does not exist")
            return self.collection.update_one(query, {'$set': new_vals})
        except errors.PyMongoError as e:
            logger.error(f"Error updating one document: {e}")
            raise

    def update_many(self, query, new_vals):
        # Update multiple documents, raise error if they don't exist
        try:
            if not self.collection.find_one(query):
                raise ValueError("Documents do not exist")
            return self.collection.update_many(query, {'$set': new_vals})
        except errors.PyMongoError as e:
            logger.error(f"Error updating many documents: {e}")
            raise

    def delete_one(self, query):
        # Delete a single document, raise error if it doesn't exist
        try:
            if not self.collection.find_one(query):
                raise ValueError("Document does not exist")
            return self.collection.delete_one(query)
        except errors.PyMongoError as e:
            logger.error(f"Error deleting one document: {e}")
            raise

    def delete_many(self, query):
        # Delete multiple documents, raise error if they don't exist
        try:
            if not self.collection.find_one(query):
                raise ValueError("Documents do not exist")
            return self.collection.delete_many(query)
        except errors.PyMongoError as e:
            logger.error(f"Error deleting many documents: {e}")
            raise


class Tenant(BaseCollection):
    def __init__(self):
        super().__init__("tenant")

    def get_tenant_by_id(self, t_id):
        try:
            if type(t_id) is str:
                t_id = ObjectId(t_id)

            tenant = self.find_one({"_id": t_id})
            return tenant
        except Exception as e:
            logger.error(f"Error querying tenant by id: {e=}")
            raise

    def get_tenant_by_name(self, name):
        return self.find_one({"name": name})

    def get_tenant_by_slug(self, slug):
        return self.find_one({"slug": slug})

    def get_tenants_by_ad_group_oids(self, adg_oids):
        return list(self.collection.find({
            "owner": {"$elemMatch": {"oid": {"$in": adg_oids}}}
        }))

    def get_tenants_by_name_regex(self, name_rx):
        # Retrieve tenants by name regex
        try:
            return list(self.collection.find(
                {'name': {'$regex': f'(?i){name_rx}(?-i)'}}))
        except errors.PyMongoError as e:
            logger.error(f"Error getting tenants by name regex: {e}")
            raise

    def get_tenants_by_devhubid(self, devhubid):
        try:
            return list(self.collection.find(
                {"tenant_apps": {"$in": [devhubid]}}))
        except errors.PyMongoError as e:
            logger.error(f"Error getting tenants by devhubid: {e}")
            raise

    def mark_for_delete(self, query, delete):
        # Mark a tenant for deletion
        try:
            if not self.find_one(query):
                raise ValueError("Document does not exist")
            del_val = int(time.time()) + 604800 if delete else 0
            result = self.update_one(query, {'delete_ts': del_val})
            return result
        except Exception as e:
            logger.error(f"Error marking tenant for delete: {e=}")
            raise Exception("Failed to mark tenant for delete, "
                            f"query: {query}")

    # TODO - refactor to handle deletion by collection
    def delete_by_tenant_id(self, tenant_id):
        try:
            # Determine if tenant_id is an ObjectId or
            # other identifier (like UUID)
            query = {"_id": tenant_id}
            tenant = self.find_one(query)
            if not tenant:
                raise ValueError("Tenant does not exist")

            if not isinstance(tenant_id, ObjectId):
                try:
                    tenant_id = ObjectId(tenant_id)
                    query = {"_id": tenant_id}
                except Exception:
                    logger.info(f"Tenant ID {tenant_id} is not a valid \
                                ObjectId, using as a string instead.")
                    raise

            # Check if the tenant exists
            if not self.find_one(query):
                raise ValueError("Tenant does not exist")

            # Delete associated AD groups, roles
            # and tenant documents by tenant ID
            role = Role()
            role.delete_ad_groups_by_tenant_id(tenant_id)
            role.delete_role_by_tenant_id(tenant_id)
            self.delete_one(query)

        except Exception as e:
            logger.error(f"Error deleting tenant by ID: {e=}")
            raise Exception("Failed to delete Tenant")

    def get_tenants_by_ids(self, tenant_ids):
        try:
            object_ids = []
            non_object_ids = []

            for x in tenant_ids:
                if isinstance(x, ObjectId):
                    object_ids.append(x)
                else:
                    try:
                        # Try to convert to ObjectId
                        object_ids.append(ObjectId(x))
                    except Exception:
                        # If not a valid ObjectId, treat it as a string or UUID
                        non_object_ids.append(x)

            # Query for both ObjectIds and other types (like UUIDs)
            query = {"$or": [
                {"_id": {"$in": object_ids}},
                {"_id": {"$in": non_object_ids}}
            ]}
            return self.collection.find(query)
        except errors.PyMongoError as e:
            logger.error(f"Error getting tenants by IDs: {e}")
            raise

    def rename_tenant_by_id(self, tenant_id, new_name, new_slug):
        try:
            # Determine if tenant_id is an ObjectId or
            # other identifier (like UUID)
            query = {"_id": tenant_id}

            if not isinstance(tenant_id, ObjectId):
                try:
                    tenant_id = ObjectId(tenant_id)
                    query = {"_id": tenant_id}
                except Exception:
                    logger.info(f"Tenant ID {tenant_id} is not a valid \
                                ObjectId, using as a string instead.")

            # Check if the tenant exists
            if not self.find_one(query):
                raise ValueError("Tenant does not exist")

            # Update the tenant document with new name and slug
            result = self.update_one(
                query,
                {"$set": {"name": new_name,
                          "slug": new_slug,
                          "updated": int(time.time())}}
            )

            # Return 1 if the document was modified, otherwise 0
            return 1 if result.modified_count > 0 else 0
        except errors.PyMongoError as e:
            logger.error(f"Error renaming tenant by ID: {e}")
            raise
        except ValueError as ve:
            logger.error(f"ValueError: {ve}")
            raise


class Role(BaseCollection):
    def __init__(self):
        super().__init__("role")

    def get_user_roles(self, request):
        # Retrieve user roles based on request
        try:
            ad_groups = self.adc.get_user_ad_groups(request, True)
            adg_oids = [adg['oid'] for adg in ad_groups]
            return self.get_roles_by_adg_oids(adg_oids)
        except errors.PyMongoError as e:
            logger.error(f"Error getting user roles: {e}")
            raise

    def get_roles_by_adg_oids(self, adg_oids):
        # Ensure adg_oids is a list
        if not isinstance(adg_oids, list):
            adg_oids = [adg_oids]

        try:
            roles_by_oids = {}
            admin_group_oid = 'ef68420f-92d7-4837-9ef5-c04d67444523'
            query = {}

            if admin_group_oid in adg_oids:
                roles = self.collection.find({})
                is_admin = True
            else:
                query = {
                    "$or": [
                        {"owners": {"$in": adg_oids}},
                        {"ops": {"$in": adg_oids}},
                        {"users": {"$in": adg_oids}},
                        {"collabs": {"$in": adg_oids}}
                    ]
                }
                roles = self.collection.find(query)
                is_admin = False

            matched_roles = list(roles)

            for r in matched_roles:
                tenant_id = str(r['tenant_id'])
                if is_admin:
                    roles_by_oids[tenant_id] = 'owners'
                elif str(r['owners']) in adg_oids:
                    roles_by_oids[tenant_id] = 'owners'
                elif str(r['ops']) in adg_oids:
                    roles_by_oids[tenant_id] = 'ops'
                elif str(r['users']) in adg_oids:
                    roles_by_oids[tenant_id] = 'users'
                elif str(r['collabs']) in adg_oids:
                    roles_by_oids[tenant_id] = 'collabs'

            return roles_by_oids

        except errors.PyMongoError as e:
            logger.error(f"Error getting roles by AD group OIDs: {e}")
            raise

    def has_role_user(self, request, tenant_id, user_oid=None):
        '''
            Lookup groups and test against roles based on tenant_id
            and return bool on whether user has role on Tenant, and
            what role if so
        '''
        try:
            if type(tenant_id) is ObjectId:
                tenant_id = str(tenant_id)

            ad_groups = self.adc.get_user_ad_groups(request, True, user_oid)
            adg_oids = [adg['oid'] for adg in ad_groups]

            # Test for membership in admin group
            if 'ef68420f-92d7-4837-9ef5-c04d67444523' in adg_oids:
                return True, 'owners'

            tenant_roles = self.find_one({'tenant_id': tenant_id})
            # Step through keys in role record, test values of role keys
            # against ad group oids - each value is oid of group representing
            # membership for that role
            for k in tenant_roles.keys():
                if k != '_id' and k != 'tenant_id':
                    if tenant_roles[k] in adg_oids:
                        return True, k
            return False, None
        except Exception as e:
            logger.error(f"Failed to get user role for Tenant '{tenant_id}':"
                         f" {e=}")
            if isinstance(e, fastapi.exceptions.HTTPException) and \
              e.status_code == 404:  # noqa: E127
                raise e
            else:
                raise Exception({"message": "Failed to get user role for "
                                 f"Tenant '{tenant_id}'"})

    def has_role_client(self, tenant_id, client_token, role=None):
        # Check if a client has a role in the tenant (placeholder)
        pass

    def get_ad_groups_by_tenant(self, tenant_id):
        # Retrieve AD groups by tenant ID
        try:
            tenant_roles = self.find_one({'tenant_id': tenant_id})
            if not tenant_roles:
                return None

            ad_groups = {}
            for role in ['owners', 'ops', 'users', 'collabs']:
                if role in tenant_roles:
                    ad_group_id = tenant_roles[role]
                    ad_group = ADGroup().get_ad_group_by_id(ad_group_id)
                    ad_groups[role] = ad_group

            return ad_groups
        except errors.PyMongoError as e:
            logger.error(f"Error getting AD groups by tenant: {e}")
            raise

    def get_ad_groups_oids_by_tenant_id(self, tenant_id):
        # Retreive the role and group ids from the role for a given tenant
        try:
            roles = self.find_one({'tenant_id': tenant_id},
                                  {'_id': 0,
                                   'tenant_id': 0})
            return roles
        except errors.PyMongoError as e:
            logger.error(f"Error getting AD Group IDS by Tenant ID: {e}")
            raise

    def get_users_by_ad_group_and_role(self, tenant_id, role):
        # Retrieve users by AD group and role
        try:
            tenant_roles = self.get_ad_groups_by_tenant(tenant_id)

            if role in tenant_roles:
                ad_group_id = tenant_roles[role]
                return self.adc.get_ad_group_members(ad_group_id)

            return None
        except errors.PyMongoError as e:
            logger.error(f"Error getting users by AD group and role: {e}")
            raise

    def get_heirarchy_by_tenant_and_role(self, tenant_id, role, start_email):
        # Retrieve hierarchy by tenant and role
        try:
            members = self.get_users_by_ad_group_and_role(tenant_id, role)
            if members:
                return self.adc.get_member_hierarchy(members, start_email)
            return {}
        except errors.PyMongoError as e:
            logger.error(f"Error getting hierarchy by tenant and role: {e}")
            raise

    def delete_role_by_tenant_id(self, tenant_id):
        # Delete role by tenant ID
        try:
            role = self.find_one({'tenant_id': str(tenant_id)})
            if not role:
                raise ValueError("Role does not exist")
            self.delete_one({'tenant_id': str(tenant_id)})
        except errors.PyMongoError as e:
            logger.error(f"Error deleting role by tenant ID: {e}")
            raise

    def delete_ad_groups_by_tenant_id(self, tenant_id):
        # Delete AD groups by tenant ID
        try:
            role = self.find_one({'tenant_id': str(tenant_id)})
            if not role:
                raise ValueError("Role does not exist")
            group = ADGroup()
            if 'owners' in role:
                group.delete_group_by_oid(role['owners'])
            if 'ops' in role:
                group.delete_group_by_oid(role['ops'])
            if 'collabs' in role:
                group.delete_group_by_oid(role['collabs'])
            if 'users' in role:
                group.delete_group_by_oid(role['users'])
        except errors.PyMongoError as e:
            logger.error(f"Error deleting AD groups by tenant ID: {e}")
            raise


class ADGroup(BaseCollection):
    def __init__(self):
        super().__init__("ad_group")

    def get_ad_group_by_id(self, adg_id):
        # Retrieve an AD group by its ID
        try:
            return self.find_one({"oid": adg_id})
        except errors.PyMongoError as e:
            logger.error(f"Error getting AD group by ID: {e}")
            raise

    def get_ad_groups_by_name(self, name_rx):
        # Retrieve AD groups by name regex
        try:
            return list(self.collection.find(
                {"name": {'$regex': name_rx, '$options': 'i'}}))
        except errors.PyMongoError as e:
            logger.error(f"Error getting AD groups by name: {e}")
            raise

    def delete_group_by_oid(self, oid):
        try:
            # Check if the group exists
            if not self.find_one({'oid': oid}):
                raise ValueError("AD group does not exist")

            # Delete the group
            self.delete_one({'oid': oid})
        except errors.PyMongoError as e:
            logger.error(f"Error deleting AD group by OID: {e}")
            raise


class MOTD(BaseCollection):
    def __init__(self):
        super().__init__("motd")
