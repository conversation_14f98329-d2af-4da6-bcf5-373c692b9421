import logging
import logging.config

from typing import Any
from bson.objectid import ObjectId

from fastapi import APIRouter, Request, Body, HTTPException, Response, Depends

from src.util.logging import get_request_log
from src.clients import mongo
from src.clients import pillars
from src.models.entities import Instance, InstanceField, InstanceType
from src.models.provision import Conditional, TextInput, SelectorOption, \
    Selector, Instructions, ProvisionForm, DevHubList, form_input, \
    provision_request, provision_request_pillar  # noqa: F401
from src.models.instance_details import KV, StatusIndicatorThreshold, \
    StatusIndicator, StackResource, Info, SeriesLabel, DataPoint, \
    TSThreshold, TimeSeries, TSChart, Panel, MetricsPanel, Metrics, \
    CostLI, CostsPanel, UtilLI, UtilPanel, Showback, \
    InstanceDetails  # noqa: F401
from src.security.auth import HeaderAuth
# from src.security.oidc import user_has_required_role, _get_auth_status_claims
from src.security import oidc
from src.util.exceptions import parse_exception_args
from src.validation.input_validator import validate_tenant_id, validate_string

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

instance_router = APIRouter(prefix="/api/v1/instances",
                            tags=["instances"],
                            responses={404: {"description":
                                             "404 Not Found"}})


@instance_router.get("/filter",
                     dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def get_instances_filter(request: Request,
                               tenant_id: str = None,
                               tenant_slug: str = None):
    # user: bool = False):
    # token: str = Depends(api_key_header)):
    '''
        Return list
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    filter_count = 0
    for x in [tenant_id, tenant_slug]:  # , user]:
        if x:
            filter_count += 1
    if filter_count > 1:
        msg = "Only one filter may be used: user, tenant_id, or tenant_slug"
        logger.error(f"400 - {msg} - {request_log}")
        raise HTTPException(status_code=400, detail=msg)

    # This Pillar API method is defunct
    # if user:
    #     user_id = auth._get_auth_status_claims(request, request_log)['ntid']
        # TODO - rework testing auth here, need to add method to tell if auth
        # comes from SSO or a token passed in header or swagger, if
        # auth-by-token, then we allow a ntid to be passed in, and token
        # should include scope for this access? (oauth client may only exist
        # for auth b/t cpapi and pillar api initially)

    #    return _get_instances_by_user(request, request_log, user_id, pillar)

    if tenant_id:
        return _get_instances_by_tenant(request, request_log, tenant_id)

    if tenant_slug:
        return _get_instances_by_tenant_slug(request, request_log,
                                             tenant_slug, None)

    msg = "No parameters passed for Instance filter"
    logger.error(f"400 - {msg} - {request_log}")
    raise HTTPException(status_code=400, detail=msg)


# @instance_router.get("/user",
#                      dependencies=[Depends(HeaderAuth())])
# async def get_instances_by_user(request: Request,
#                                 pillar: str = None):
#     # token: str = Depends(api_key_header)):
#     '''
#        Return list of Instances and metadata logged in user has
#        access to \nIf pillar param is provided, only Instances from that
#        Pillar will be returned
#     '''
#     request_log = await get_request_log(request)
#     logger.info(f"{request_log}")
#
#     auth.validate_auth(request, request_log)
#
#     # TODO - Caching TDB
#     # _instances = mongo.Stacks()
#     # instances = _instances.get_instances_by_user(user_id)
#
#     user_props = auth._get_auth_status_claims(request, request_log)
#     user_id = user_props['ntid']
#     # TODO - rework testing auth here, need to add method to tell if auth
#     # comes from SSO or a token passed in header or swagger, if
#     # auth-by-token, then we allow a ntid to be passed in, and token
#     # should include scope for this access? (oauth client may only exist
#     # for auth b/t cpapi and pillar api initially)
#
#     return _get_instances_by_user(request, request_log, user_id, pillar)


def _get_instances_by_user(request: Request,
                           request_log: Any,
                           user_id: str,
                           pillar: str = 'logging'):
    if pillar:
        validate_string(pillar, 'pillar')
    try:
        instances_resp = pillars.get_instances_by_user(user_id, pillar)
    except Exception as e:
        msg = f"Request failed: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    # if len(instances_resp['instances']) < 1:
    #     msg = f"No instances found in MELT portal for user, {user_id} "\
    #         + f"(request_id: {request_log['request_id']})"
    #     logger.exception(msg)
    #     raise HTTPException(status_code=404, detail=f"Error: {msg}")
    instances = []
    for inst in instances_resp['instances']:
        # inst_t = "MaaS" if inst['type'] == 'maas' else 'LaaS'
        # inst_t = "None" if inst['type'] != 'elk' and inst['type'] != 'maas'\
        #     else inst_t

        t_id = None
        t_name = None
        if 'tenant_id' in inst.keys():
            _tenant = mongo.Tenant()
            tenant = _tenant.find_one({'_id': ObjectId(inst['tenant_id'])})
            t_id = tenant['_id']
            t_name = tenant['name']

        fields = None
        if 'fields' in inst.keys():
            fields = []
            for f in inst['fields']:
                fields.append(InstanceField(key=f['key'],
                                            value=f['value']))

        instances.append(Instance(id=inst['id'],
                                  name=inst['name'],
                                  type=InstanceType(inst['type']),
                                  tenant_id=t_id,
                                  tenant_name=t_name,
                                  fields=fields,
                                  status=inst['status'] if 'status' in
                                  inst.keys() else ''))

    return instances


# @instance_router.get("/tenant/{tenant_id}",
#                      dependencies=[Depends(HeaderAuth())])
# async def get_instances_by_tenant(request: Request,
#                                   tenant_id: str):
#     # token: str = Depends(api_key_header)):
#     '''
#        Return list of Instances and metadata owned by Tenant
#     '''
#     request_log = await get_request_log(request)
#     logger.info(f"{request_log}")
#
#     auth.validate_auth(request, request_log)
#
#     return _get_instances_by_tenant(request, request_log, tenant_id)


def _get_instances_by_tenant(request: Request,
                             request_log: Any,
                             tenant_id: str):
    # TODO - caching Instances TBD
    # _instance = mongo.Instance()
    # instances = _instances.get_instances_by_tenant_id(tenant_id)

    validate_tenant_id(tenant_id)
    _tenant = mongo.Tenant()
    tenant = _tenant.find_one({'_id': ObjectId(tenant_id)})
    if not tenant:
        msg = f"No tenants found for Id, {tenant_id} "\
            + f"(request_id: {request_log['request_id']})"
        logger.error(msg)
        # raise HTTPException(status_code=404, detail=f"Error: {msg}")
        return []

    authd = False
    if 'authorization' in request.headers.keys():
        authd = True
    else:
        authd = oidc.user_has_required_role(request, tenant['_id'],
                                            ['owners', 'ops', 'users', 'collabs'])
    if authd:
        try:
            instances_resp = pillars.get_instances_by_tenant(tenant_id)
        except Exception as e:
            msg = f"Request failed: {str(e)}"
            logger.exception(msg + f" - request: {request_log}")
            raise HTTPException(status_code=500, detail=f"{msg}")
        instances = []
        for inst in instances_resp['instances']:
            # inst_t = "MaaS" if inst['type'] == 'maas' else 'LaaS'
            # inst_t = "None" if inst['type'] != 'elk' and
            # inst['type'] != 'maas' \
            #     else inst_t
            # TODO - convey to Metrics - attributes like this now belong
            #        in 'fields'

            devhub_ids = \
                inst['devhub_ids'] if 'devhub_ids' in inst.keys() else []
            fields = None
            if 'fields' in inst.keys():
                fields = []
                for f in inst['fields']:
                    if f['key'] == 'devhub_ids':
                        devhub_ids = [int(d) for d in f['value']]
                        continue
                    fields.append(InstanceField(key=f['key'],
                                                value=f['value']))

            instances.append(Instance(id=inst['id'],
                                      name=inst['name'],
                                      tenant_id=tenant_id,
                                      tenant_name=tenant['name'],
                                      devhub_ids=devhub_ids,
                                      type=InstanceType(inst['type']),
                                      fields=fields,
                                      status=inst['status'] if 'status' in
                                      inst.keys() else ''))
        return instances

    claims = oidc._get_auth_status_claims(request, request_log)
    user_id = claims['ntid'] if 'ntid' in claims.keys() else 'OAuth'
    msg = f"Forbidden: User {user_id} does not have privileges "\
        + f"on Tenant '{tenant['name']}'"
    logger.error(f"403 - msg - {request_log}")
    raise HTTPException(status_code=403, detail=msg)


def _get_instances_by_tenant_slug(request: Request,
                                  request_log: Any,
                                  tenant_slug: str,
                                  pillar: str):

    validate_string(tenant_slug, "slug")

    _tenant = mongo.Tenant()
    tenant = _tenant.find_one({'slug': tenant_slug})
    if not tenant:
        msg = f"No tenants found for slug, {tenant_slug} "\
            + f"(request_id: {request_log['request_id']})"
        logger.error(msg)
        # raise HTTPException(status_code=404, detail=f"Error: {msg}")
        return []

    tenant_id = str(tenant['_id'])
    claims = oidc._get_auth_status_claims(request, request_log)
    user_id = claims['ntid'] if 'ntid' in claims.keys() else 'OAuth'

    authd = False
    if 'authorization' in request.headers.keys():
        authd = True
    else:
        authd = oidc.user_has_required_role(request, tenant['_id'],
                                            ['owners', 'ops', 'users', 'collabs'])
    if authd:
        try:
            instances_resp = pillars.get_instances_by_tenant(tenant_id)
        except Exception as e:
            msg = f"Request failed: {str(e)}"
            logger.exception(msg + f" - request: {request_log}")
            raise HTTPException(status_code=500, detail=f"{msg}")
        instances = []
        for inst in instances_resp['instances']:
            devhub_ids = \
                inst['devhub_ids'] if 'devhub_ids' in inst.keys() else []
            fields = None
            if 'fields' in inst.keys():
                fields = []
                for f in inst['fields']:
                    if f['key'] == 'devhub_ids':
                        devhub_ids = [int(a) for a in f['value']]
                        continue
                    fields.append(InstanceField(key=f['key'],
                                                value=f['value']))

            instances.append(Instance(id=inst['id'],
                                      name=inst['name'],
                                      tenant_id=tenant_id,
                                      tenant_name=tenant['name'],
                                      devhub_ids=devhub_ids,
                                      type=InstanceType(inst['type']),
                                      fields=fields,
                                      status=inst['status'] if 'status' in
                                      inst.keys() else ''))
        return instances

    msg = f"Forbidden: User {user_id} does not have privileges "\
        + f"on Tenant '{tenant['name']}'"
    logger.error(f"403 - msg - {request_log}")
    raise HTTPException(status_code=403, detail=msg)


# TODO - should we provide pillar as option,
#        or always call each?
# pillar: str = "metrics"):  # TODO - provide enums


@instance_router.get("/id/{instance_id}",
                     dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def get_instance_by_id(request: Request,
                             instance_id: str):
    '''
        Return Instance
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    return _get_instance_by_id(request,
                               request_log,
                               # pillar,
                               instance_id)  # noqa: F821


def _get_instance_by_id(request: Request,
                        request_log: Any,
                        # pillar: str,
                        instance_id: str):

    instance = None
    try:
        instance = pillars.get_instance_by_id(instance_id)  # , pillar)
    except Exception as e:
        msg = f"Request failed: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        if "'status': 404" in str(e):
            raise HTTPException(status_code=404, detail=f"{msg}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    tenant = None
    if instance['tenant_id']:
        _tenant = mongo.Tenant()
        tenant = _tenant.get_tenant_by_id(instance['tenant_id'])
        if not tenant:
            msg = f"No tenants found for ID, {instance['tenant_id']} "\
                + f"(request_id: {request_log['request_id']})"
            logger.error(msg)
            raise HTTPException(status_code=404, detail=f"Error: {msg}")

        authd = False
        if 'authorization' in request.headers.keys():
            authd = True
        else:
            authd = oidc.user_has_required_role(request, tenant['_id'],
                                                ['owners', 'ops', 'users', 'collabs'])
        if not authd:
            claims = oidc._get_auth_status_claims(request, request_log)
            user_id = claims['ntid'] if 'ntid' in claims.keys() else 'OAuth'
            msg = f"Forbidden: User {user_id} does not have privileges "\
                + f"on Tenant '{tenant['name']}'"
            logger.error(msg)
            raise HTTPException(status_code=403, detail=msg)

    fields = []
    devhub_ids = \
        instance['devhub_ids'] if 'devhub_ids' in instance.keys() else []
    for f in instance['fields']:
        if f['key'] == 'devhub_ids':
            devhub_ids = [int(d) for d in f['value']]
            continue
        fields.append(InstanceField(key=f['key'],
                                    value=f['value']))

    if 'status' not in instance:
        instance['status'] = "Unknown"
    inst = Instance(id=instance['id'],
                    name=instance['name'],
                    tenant_id=str(tenant['_id']) if tenant else None,
                    tenant_name=tenant['name'] if tenant else None,
                    devhub_ids=devhub_ids,
                    type=InstanceType(instance['type']),
                    fields=fields,
                    status=instance['status'])

    return inst


def parse_provision_form(p_form_j):
    '''
        Parse JSON and return ProvisionForm object
    '''
    form_elements = []
    instructions = None
    if 'instructions' in p_form_j.keys():
        instructions = \
            Instructions(type=p_form_j['instructions']['type'],
                         values=p_form_j['instructions']['values'])

    for el in p_form_j['inputs']:
        # if el['type'] == 'label':
        #     label = Label(type=el['type'],
        #                   field_name=el['field_name'],
        #                   header=el['header'])
        #     if 'conditional' in el.keys():
        #         label.conditional = el['conditional']
        #     form_elements.append(label)
        if el['type'] == 'text':
            # label = Label(type=el['label']['type'],
            #               field_name=el['label']['field_name'],
            #               header=el['label']['header'])
            t_input = TextInput(type=el['type'],
                                name=el['name'],
                                # label=label,
                                # default_text=el['default_text'])
                                label=el['label'],
                                required=el['required'])
            if 'conditional' in el.keys():
                t_input.conditional = \
                    Conditional(for_field=el['conditional']['for_field'],
                                value=el['conditional']['value'])
            if 'description' in el.keys():
                t_input.description = \
                    Instructions(type=el['description']['type'],
                                 values=el['description']['values'])
            form_elements.append(t_input)
        if el['type'] == 'select' or el['type'] == 'checkbox':
            # label = Label(type=el['label']['type'],
            #               field_name=el['label']['field_name'],
            #               header=el['label']['header'])
            selector = Selector(type=el['type'],
                                name=el['name'],
                                # label=label,
                                label=el['label'])
            if 'conditional' in el.keys():
                selector.conditional = \
                    Conditional(for_field=el['conditional']['for_field'],
                                value=el['conditional']['value'])
            if 'description' in el.keys():
                selector.description = \
                    Instructions(type=el['description']['type'],
                                 values=el['description']['values'])
            if 'options' in el.keys():
                selector.options = []
                for o in el['options']:
                    selector.options.append(SelectorOption(label=o['label'],
                                                           value=o['value']))
            form_elements.append(selector)
        if el['type'] == 'devhub_ids':
            devhubids = DevHubList(
                type=el['type'],
                name=el['name'],
                limit=el['limit']
            )
            form_elements.append(devhubids)

    form = ProvisionForm(pillar=p_form_j['pillar'],
                         inputs=form_elements)
    if instructions:
        form.instructions = instructions

    return form


@instance_router.get("/provisionform",
                     dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def get_provision_form(request: Request,
                             pillar: str = 'logging'):
    # token: str = Depends(api_key_header)):
    '''
        Return the Instance Provision Form for *pillar*
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    return _get_provision_form(request, request_log, pillar)


def _get_provision_form(request: Request,
                        request_log: Any,
                        pillar: str):
    provision_form_resp = None
    validate_string(pillar, 'pillar')
    try:
        provision_form_resp = pillars.get_provision_form(pillar)
    except Exception as e:
        msg = f"Request failed: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    # elif pillar == ...

    try:
        provision_form = parse_provision_form(provision_form_resp)
    except Exception as e:
        msg = f"Failed to parse ProvisionForm from pillar {pillar}: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    return provision_form


@instance_router.post("/provision",
                      dependencies=[Depends(HeaderAuth(scope="ocp:instance:provision"))])
async def provision_instance(request: Request,
                             provision_req: provision_request,
                             pillar: str):
    # token: str = Depends(api_key_header)
    '''
        Provision a new Instance
        ...
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    return _provision_instance(request, request_log, provision_req, pillar)


def _provision_instance(request: Request,
                        request_log: Any,
                        provision_req: provision_request,
                        pillar: str):
    # TODO - update provisioning to optionally accept an NTID to provision instance under
    user_id = oidc._get_auth_status_claims(request, request_log)['ntid']

    _tenant = mongo.Tenant()
    tenant = _tenant.get_tenant_by_id(provision_req.tenant_id)

    if not tenant:
        msg = f"No tenants found for ID, {provision_req.tenant_id} "\
            + f"(request_id: {request_log['request_id']})"
        logger.exception(msg)
        raise HTTPException(status_code=404, detail=f"Error: {msg}")

    if not oidc.user_has_required_role(request, tenant['_id'], ['owners']):
        msg = f"Forbidden: User {user_id} does not have privileges "\
            + f"on Tenant '{tenant['name']}'"
        logger.error(msg)
        raise HTTPException(status_code=403, detail=msg)

    iname = ""
    for kv in provision_req.fields:
        if kv.name == 'name':
            iname = kv.value
            break
    # TODO - rework how name is stored, or turn fields into dict?

    pfields = []
    for f in provision_req.fields:
        pfields.append({"name": f.name,
                        "value": f.value})
    # TODO - use 'provision_request_pillar' model here

    ad_groups = \
        mongo.Role().get_ad_groups_oids_by_tenant_id(provision_req.tenant_id)

    p_req_pillar = {"instance_name": iname,
                    "tenant_id": provision_req.tenant_id,
                    "tenant_name": tenant['name'],
                    "git_repo": tenant['git_repo'],
                    "devhub_ids": provision_req.devhub_ids,
                    "ad_groups": ad_groups,
                    "fields": pfields
                    }

    provision_inst_resp = None
    try:
        provision_inst_resp = \
            pillars.provision_instance(p_req_pillar, pillar)
    except Exception as e:
        obj = parse_exception_args(e)
        msg = f"Request failed: {obj}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=obj['status'],
                            detail=f"{obj['error']}")

    provisioned_inst = provision_inst_resp['instance']
    # TODO - review response in contract (key here is redundant)

    status = provisioned_inst['status'] if 'status' in \
        provisioned_inst.keys() else ''

    new_instance = Instance(id=provisioned_inst['id'],
                            name=provisioned_inst['name'],
                            type=InstanceType(provisioned_inst['type']),
                            tenant_id=str(tenant['_id']),
                            tenant_name=tenant['name'],
                            devhub_ids=provisioned_inst['devhub_ids'],
                            fields=provisioned_inst['fields'],
                            status=status)

    return new_instance


@instance_router.post("/provisionform/validate",
                      response_model=ProvisionForm,
                      dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def validate_provision_form(request: Request,
                                  # provision_form: ProvisionForm):
                                  provision_form: dict = Body(...)):
    '''
        Validate Provision Form object
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    p_form = None

    try:
        p_form = parse_provision_form(provision_form)
        return p_form
    except Exception as e:
        msg = f"Failed to parse ProvisionForm: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=422, detail=f"{msg}")


def parse_vizualization(viz_j):
    '''
        Parse JSON and return Visualization object (TSChart or IndicatorType)
    '''
    vizualization = None
    if viz_j['type'] in ["linechart", "sparkline"]:
        t_series = []
        for ts in viz_j['series']:
            labels = []
            for l in ts['labels']:  # noqa: E741
                labels.append(SeriesLabel(label=l['label'],
                                          value=l['value']))
            d_points = []
            for dpt in ts['data_points']:
                d_points.append(DataPoint(timestamp=dpt['timestamp'],
                                          value=dpt['value']))
            t_series.append(TimeSeries(metric=ts['metric'],
                                       unit=ts['unit'],
                                       labels=labels,
                                       data_points=d_points))
        threshs = None
        if 'thresholds' in viz_j.keys():
            threshs = []
            for t in viz_j['thresholds']:
                threshs.append(TSThreshold(label=t['label'],
                                           value=t['value'],
                                           color=t['color']))
        vizualization = TSChart(title=viz_j['title'],
                                type=viz_j['type'],
                                series=t_series,
                                thresholds=threshs)
    elif viz_j['type'] in ["counter", "gauge"]:
        threshs = []
        if 'thresholds' in viz_j.keys() and viz_j['thresholds'] is not None:
            for t in viz_j['thresholds']:
                threshs.append(StatusIndicatorThreshold(
                    threshold_label=t['threshold_label'],
                    value_range=t['value_range']))
        vizualization = StatusIndicator(label=viz_j['label'],
                                        type=viz_j['type'],
                                        value=viz_j['value'])
        if len(threshs) > 0:
            vizualization.thresholds = threshs
    else:
        msg = "Failed to parse Vizualization"
        logger.error(msg)
        raise Exception({"message": f"Error: {msg}"})

    return vizualization


def parse_details(instance_details):
    '''
        Parse JSON and return InstanceDetails object
    '''
    # Parse Info

    try:
        logger.debug(f"\n\n INSTANCE DETAILS: \n{instance_details}\n\n")
        return InstanceDetails(**instance_details)
    except Exception:
        msg = "Failed to parse Instance Details"
        logger.error(msg)
        raise Exception({"message": f"Error: {msg}"})


@instance_router.get("/details/id/{instance_id}",
                     response_model=InstanceDetails,
                     dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def get_instance_details(request: Request,
                               instance_id: str,
                               pillar: str = "metrics"):
    '''
        Return Instance Details object
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    return _get_instance_details(request,
                                 request_log,
                                 pillar,
                                 instance_id)


def _get_instance_details(request: Request,
                          request_log: Any,
                          pillar: str,
                          instance_id: str):
    try:
        instance = pillars.get_instance_by_id(instance_id)
    except Exception as e:
        msg = f"Request failed: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    _tenant = mongo.Tenant()
    tenant = _tenant.get_tenant_by_id(instance['tenant_id'])

    if not tenant:
        msg = f"No tenants found for ID, {instance['tenant_id']} "\
            + f"(request_id: {request_log['request_id']})"
        logger.exception(msg)
        raise HTTPException(status_code=404, detail=f"Error: {msg}")

    authd = False
    if 'authorization' in request.headers.keys():
        authd = True
    else:
        authd = oidc.user_has_required_role(request, tenant['_id'],
                                            ['owners', 'ops', 'users', 'collabs'])
    if not authd:
        claims = oidc._get_auth_status_claims(request, request_log)
        user_id = claims['ntid'] if 'ntid' in claims.keys() else 'OAuth'
        msg = f"Forbidden: User {user_id} does not have Read privileges "\
            + f"on Tenant '{tenant['name']}'"
        logger.error(msg)
        raise HTTPException(status_code=403, detail=msg)

    try:
        details_resp = pillars.get_instance_details(instance_id, pillar)
    except Exception as e:
        msg = f"Request failed: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    try:
        if pillar == "logging":
            details_resp = \
                {"info":
                    {"instance":
                        {"id": details_resp['id'],
                         "name": details_resp['name'],
                         "type": details_resp['type'],
                         "status": details_resp['status'],
                         "tenant_id": details_resp['tenant_id'] if 'tenant_id' in
                            details_resp.keys() else None,
                         "tenant_name": details_resp['tenant_name'] if 'tenant_name' in
                            details_resp.keys() else None,
                         "devhub_ids": details_resp['devhub_ids'] if 'devhub_ids' in
                            details_resp.keys() else None,
                         "fields": details_resp['fields'] if 'fields' in
                            details_resp.keys() else None,
                         "health": details_resp['health'] if 'health' in
                            details_resp.keys() else None}}}
            # temporary fix for missing contract on logging API

        details = parse_details(details_resp)
    except Exception as e:
        msg = f"Failed to parse InstanceDetails from pillar {pillar}: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500, detail=f"{msg}")

    return details


@instance_router.post("/details/validate",
                      response_model=InstanceDetails,
                      dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def validate_details(request: Request,
                           instance_details: dict = Body(...)):
    '''
        Validate Instance Details object
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    try:
        details = parse_details(instance_details)
    except Exception as e:
        msg = f"Failed to parse InstanceDetails: {str(e)}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=422, detail=f"{msg}")

    # return {"status": "valid", "msg": "Details object is valid"}
    return details


@instance_router.get("/schema/provisionform",
                     dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def get_provision_form_schema(request: Request):
    # token: str = Depends(api_key_header)):
    '''
        Return schema for OCP Instance Provision Form
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    schema = ProvisionForm.schema_json()
    return Response(content=schema, media_type="application/json")


@instance_router.get("/schema/details",
                     dependencies=[Depends(HeaderAuth(scope="ocp:instance:read"))])
async def get_instance_details_schema(request: Request):
    # token: str = Depends(api_key_header)):
    '''
        Return schema for OCP Instance Details
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    schema = InstanceDetails.schema_json()
    return Response(content=schema, media_type="application/json")


# ############################################
# TODO -

# @instance_router.get("/instances/regex/{stack_name_regex}")
# async def get_instances_by_regex_search(request: Request,
#                                         stack_name_regex: str,
#   #token: str = Depends(api_key_header)):
#    '''
#       Lookup and return Stacks and metadata by regex search on name
#    '''
#    request_log = await get_request_log(request)
#    logger.info(f"{request_log}")
#
#    if not auth.is_authenticated(request, token):
#        logger.error(f"401 Unauthorized, request_id: "\
#                +f"{request_log['request_id']}")
#        raise credentials_exception
#
#    _instances = mongo.Stacks()
#    instances = _instances.get_instances_by_name_regex(stack_name_regex)
#
#    return instances


# @instance_router.put("/instances/id/{stack_id}")
# async def update_stack(request: Request,
#                        stack: Stack,
#                        token: str = Depends(api_key_header)):
#    '''
#       Update Stack document and return new representation of entity or
#       error based on validation failure or otherwise
#
#       Method accepts JSON blob which must conform to Stack model
#       All fields must be present for update, if no fields are modified
#       and error will be returned
#    '''
#    request_log = await get_request_log(request)
#    logger.info(f"{request_log}")
#
#    if not auth.is_authenticated(request, token):
#        logger.error(f"401 Unauthorized, request_id: "\
#                +f"{request_log['request_id']}")
#        raise credentials_exception
#
#    _instances = mongo.Stacks()
#    updated_stack = _instances.update_stack(stack)
#
#    return updated_stack
