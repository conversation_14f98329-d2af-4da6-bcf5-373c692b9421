import logging
import logging.config
import os

from typing import Any

from fastapi import APIRouter, Request, Body, HTTPException, Response, Depends

from src.clients import mongo
from src.clients import pillars
from src.security.auth import HeaderAuth

from src.util.logging import get_request_log
from src.util.exceptions import parse_exception_args

from src.models.actions import KV, Payload, Conditional, \
    InputElement, InputRow, ActionInterface, ActionInterfaces, Action, \
    Actions, PillarActionRequest
from src.models.input import ActionRequest
from src.validation.input_validator import validate_string

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

ha = HeaderAuth()

actions_router = APIRouter(prefix="/api/v1/instances",
                           tags=["actions"],
                           responses={404: {"description":
                                            "404 Not Found"}})


@actions_router.get("/actions/definitions",
                    dependencies=[Depends(HeaderAuth(scope="ocp:actions:read"))])
async def get_actions_definitions(request: Request,
                                  pillar: str = None):
    '''Get Action definitions for Pillar.
       If pillar parameter is null, pull defintions for all Pillars
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _get_actions_definitions(request, request_log, pillar)


def _get_actions_definitions(request: Request,
                             request_log: Any,
                             pillar: str = None):
    actions_def_resp = None
    if pillar is not None:
        validate_string(pillar, 'pillar')
    try:
        # if pillar==None, get actions for all pillars
        actions_def_resp = pillars.get_actions_definitions(pillar)
    except Exception as e:
        msg = f"Request failed: {e=}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500,
                            detail="500 Internal Server Error")

    try:
        actions_defs = parse_actions_definitions(actions_def_resp)
    except Exception as e:
        msg = f"Failed to parse Actions Definitions from pillar {pillar}: "\
            + f"{e=}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500,
                            detail="500 Internal Server Error")

    return actions_defs


@actions_router.get("/actions/interfaces",
                    dependencies=[Depends(HeaderAuth(scope="ocp:actions:read"))])
async def get_actions_interfaces(request: Request,
                                 pillar: str = None):
    '''Get Action Interface definitions for Pillar.
       If pillar parameter is null, pull interfaces for all Pillars.
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _get_actions_interfaces(request, request_log, pillar)


def _get_actions_interfaces(request: Request,
                            request_log: Any,
                            pillar: str):
    actions_interfaces_def_resp = None
    if pillar is not None:
        validate_string(pillar, 'pillar')
    try:
        actions_interfaces_def_resp = pillars.get_actions_interfaces(pillar)
    except Exception as e:
        msg = f"Request failed: {e=}"
        logger.exception(msg=f" - request: {request_log}")
        raise HTTPException(status_code=500,
                            detail="500 Internal Server Error")

    try:
        actions_interfaces_def = \
            parse_actions_interfaces(actions_interfaces_def_resp)
    except Exception as e:
        msg = "Failed to parse Actions Interfaces Definitions from pillar "\
            + f"{pillar}: {e=}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=500,
                            detail="500 Internal Server Error")

    return actions_interfaces_def


@actions_router.get("/actions/schema/request",
                    dependencies=[Depends(HeaderAuth(scope="ocp:actions:read"))])
async def get_action_request_schema(request: Request):
    '''
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _get_action_request_schema(request, request_log)


def _get_action_request_schema(request: Request,
                               request_log: Any):
    schema = ActionRequest.schema_json()
    return Response(content=schema, media_type="application/json")


def parse_actions_definitions(actions_defs_json, single_set=False):
    ''' Parse JSON object containing Action definitions and return
        dictionary containing Actions object

        Both input and output are keyed on 'pillar', providing
        list of JSON definitions, or object definitions per
        pillar key
    '''
    # TODO - parse Actions per models

    parsed_object_dict = {}

    if single_set:
        actions_defs_json = {'single': actions_defs_json}

    for pillar in actions_defs_json.keys():
        actions_list = []

        try:
            if 'actions' not in actions_defs_json[pillar].keys():
                logger.error(f"Pillar '{pillar}' did not return valid definitions response: "
                             f"{actions_defs_json[pillar]}")
                continue

            for action_def in actions_defs_json[pillar]['actions']:
                payload = None
                payload_req_fields = None
                if 'payload' in action_def.keys():
                    payload_kvs = []
                    if action_def['payload']:
                        for kv in action_def['payload']['fields']:
                            payload_kvs.append(KV(label=kv['label'],
                                                  value=kv['value']))
                        payload = Payload(fields=payload_kvs)
                        payload_req_fields = action_def['required_payload_fields']

                action = \
                    Action(type=action_def['type'],
                           description=action_def['description'],
                           valid_roles=action_def['valid_roles'],
                           valid_states=action_def['valid_states'],
                           destructive=action_def['destructive'])

                if payload:
                    action.payload = payload
                    action.required_payload_fields = payload_req_fields

                actions_list.append(action)

            parsed_object_dict[pillar] = Actions(actions=actions_list)
        except Exception as e:
            logger.error(f"Failed to parse Actions definitions from '{pillar}' Pillar: {e=}")
            continue

    if single_set:
        try:
            parsed_object_dict = parsed_object_dict[pillar]
        except Exception as e:
            logger.error(f"Failed to parse Actions definitions: {e=}")

    return parsed_object_dict


@actions_router.post("/actions/definitions/validate",
                     response_model=Actions,
                     dependencies=[Depends(HeaderAuth(scope="ocp:actions:read"))])
async def validate_actions_definitions(request: Request,
                                       actions_definitions: dict = Body(...)):
    ''' Validate Actions definitions object
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    actions_defs_obj = None
    try:
        actions_defs_obj = \
            parse_actions_definitions(actions_definitions, single_set=True)
        return actions_defs_obj
    except Exception as e:
        msg = f"Failed to parse Actions definitions: {e=}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=422, detail=msg)


def parse_actions_interfaces(action_interfaces_json, single_set=False):
    ''' Parse JSON object containing ActionInterface definitions and return
        dictionary containing ActionInterfaces object

        Both input and output are keyed on 'pillar', providing a list
        of JSON JSON definitions, or object definitons per pillar key
    '''

    # TODO - use model for validation

    parsed_object_dict = {}

    if single_set:
        action_interfaces_json = {'single': action_interfaces_json}

    for pillar in action_interfaces_json.keys():
        try:
            if 'interfaces' not in action_interfaces_json[pillar].keys():
                logger.error(f"Pillar '{pillar}' did not return valid Actions Interfaces "
                             f"object: {action_interfaces_json[pillar]}")
                continue

            actions_ints_list = []
            for action_int in action_interfaces_json[pillar]['interfaces']:
                input_rows_list = None
                if 'input_rows' in action_int.keys():
                    input_rows_list = []
                    if action_int['input_rows']:
                        for ir in action_int['input_rows']:
                            conditional = None
                            if 'conditional' in ir.keys():
                                conditional = \
                                    Conditional(
                                        for_field=ir['conditional']['for_field'],
                                        value=ir['conditional']['value'])
                            input_vals = None
                            input_obj = \
                                InputElement(label=ir['input']['label'],
                                             type=ir['input']['type'])
                            if 'values' in ir['input'].keys():
                                input_vals = [x for x in ir['input']['values']]
                                input_obj.values = input_vals

                            input_rows_list.append(
                                InputRow(input=input_obj,
                                         required=ir['required'],
                                         conditional=conditional))

                actions_ints_list.append(
                    ActionInterface(title=action_int['title'],
                                    instructions=action_int['instructions'],
                                    input_rows=input_rows_list,
                                    action_type=action_int['action_type']))

            parsed_object_dict[pillar] = \
                ActionInterfaces(interfaces=actions_ints_list)
        except Exception as e:
            logger.error(f"Failed to parse Actions interfaces from '{pillar}' Pillar: {e=}")
            continue

    if single_set:
        try:
            parsed_object_dict = parsed_object_dict[pillar]
        except Exception as e:
            logger.error(f"Failed to parse Actions interfaces: {e=}")

    return parsed_object_dict


@actions_router.post("/actions/interfaces/validate",
                     response_model=ActionInterfaces,
                     dependencies=[Depends(HeaderAuth(scope="ocp:actions:read"))])
async def validate_action_interfaces(request: Request,
                                     actions_interfaces: dict = Body(...)):
    ''' Validate Actions Interfaces object
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    actions_ints_obj = None
    try:
        actions_ints_obj = \
            parse_actions_interfaces(actions_interfaces,
                                     single_set=True)
        return actions_ints_obj
    except Exception as e:
        msg = f"Failed to parse Actions definitions: {e=}"
        logger.exception(msg + f" - request: {request_log}")
        raise HTTPException(status_code=422, detail=msg)


@actions_router.post("/actions/execute",
                     dependencies=[Depends(HeaderAuth(scope="ocp:actions:run"))])
async def execute_action(request: Request,
                         action_request: ActionRequest):
    ''' Submit Action to be executed by Pillar
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _execute_action(request,
                           request_log,
                           action_request)


def _execute_action(request: Request,
                    request_log: Any,
                    action_request: ActionRequest):
    # TODO - if payload, validate all 'required_payload_fields' are present in
    #   payload, check by 'label'

    # TODO - handle submission via OAuth (read client_id from bearer
    #       token and pass as 'user' in request

    validate_string(action_request.pillar, 'pillar')
    _role = mongo.Role()
    has_role, role = _role.has_role_user(request,
                                         str(action_request.tenant_id))

    try:
        client_aud = os.environ.get('oidc_client_audience')
        # TODO - build method to just extract payload from JWT
        is_valid, payload = \
            ha.validate_token(request.cookies['id_token'], client_aud,
                              return_payload=True)

        # print(f"\n\n payload: {payload}\n\n")
        logger.debug(f"Payload: {payload}")

        user_id = payload['onpremisessamaccountname']
    except Exception as e:
        logger.exception(f"Failed to pull NTID: {e}")
        raise HTTPException(status_code=500,
                            detail="Request failed, please contact "
                            "support")

    # TODO - cache this in mongo?
    actions_defs = _get_actions_definitions(request, request_log, action_request.pillar)
    valid_roles = []
    for a_def in actions_defs['metrics'].actions:
        if a_def.type == action_request.action_type:
            valid_roles = a_def.valid_roles
            break

    # TODO - also validate intance state, also pull that from cache DB?

    if has_role and role in valid_roles:
        try:
            pillar_action_request = \
                PillarActionRequest(instance_id=action_request.instance_id,
                                    tenant_id=action_request.tenant_id,
                                    action_type=action_request.action_type,
                                    payload=action_request.payload,
                                    user=user_id)

            resp = pillars.execute_action(action_request.pillar,
                                          pillar_action_request)

            return resp
        except Exception as e:
            obj = parse_exception_args(e)
            msg = f"Failed to execute Action {action_request.action_type} "\
                + f"against Pillar {action_request.pillar}: {obj=}"
            logger.error(msg + f" - {request_log}")
            raise HTTPException(status_code=obj['status'],
                                detail=obj['error'])
    else:
        msg = f"Forbidden: User {user_id} does not have privileges "\
            + f"on Tenant '{action_request.tenant_id}'"
        logger.error(f"403 - {msg} - {request_log}")
        raise HTTPException(status_code=403, detail=msg)
