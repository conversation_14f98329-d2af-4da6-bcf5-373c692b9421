import logging
import logging.config
import time
from typing import Optional, Any

from bson.objectid import ObjectId
from fastapi import APIRouter, Request, HTTPException, Depends
from prometheus_client import Counter
from slugify import slugify
from pymongo import errors

from src.util.logging import get_request_log
from src.clients.actived import ADClient
from src.clients.git import GHClient
from src.models.input import DevhubAppID
from src.clients.mongo import Tenant as MongoTenant
from src.clients.mongo import Role as MongoRole
from src.clients.mongo import ADGroup as MongoADGroup
from src.clients.email_client import EmailClient
# from src.clients.pillars import get_instances_by_tenant, provision_instance
from src.clients import pillars
from src.models.entities import Tenant
from src.models.input import create_tenant_request, update_tenant_request
from src.security.auth import HeaderAuth
# from src.security.oidc import _get_auth_status_claims, user_has_required_role
from src.security import oidc
from src.util.exceptions import parse_exception_args
from src.validation.input_validator import validate_regex, validate_tenant_id
from src.validation.input_validator import validate_string

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

tenant_router = APIRouter(prefix="/api/v1/tenants",
                          tags=['tenants'],
                          responses={404: {"description": "404 Not Found"}})

tenant_provision_c = Counter('tenants_provisioned_total',
                             "Total number of Tenants provisioned")


def create_tenant_obj(tenant_data: dict) -> Tenant:
    return Tenant(
        id=str(tenant_data['_id']),
        name=tenant_data['name'],
        description=tenant_data['description'],
        tenant_apps=tenant_data.get('tenant_apps', []),
        slug=tenant_data['slug'],
        git_repo=tenant_data['git_repo'],
        created=tenant_data['created'],
        updated=tenant_data['updated'],
        delete_ts=tenant_data.get('delete_ts', 0)
    )


# TODO - add param to say whether to log error, or exception, we should
#  log traceback on 500 type errors - or automatically do traceback on 500s?
def raise_client_exception(request_log: dict, msg: str, status_code: int):
    logger.error(f"{status_code} - {msg} - {request_log}")
    raise HTTPException(status_code=status_code, detail=msg)


@tenant_router.get("/filter",
                   dependencies=[Depends(HeaderAuth(scope="ocp:tenant:read"))])
async def get_tenants_filter(request: Request,
                             user: bool = False,
                             regex: str = None):
    '''
       Get Tenants by filter
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    try:
        if user and regex:
            raise_client_exception(
                request_log,
                "Filter may be applied by user, or by regex, not both",
                400
            )

        if user:
            return _get_tenants_by_user(request, request_log)

        if regex:
            return _get_tenants_by_regex_search(request, request_log, regex)
    except Exception as e:
        obj = parse_exception_args(e)
        if "status" in obj.keys():
            raise_client_exception(request_log, obj["error"], obj["status"])

        logger.exception(f"Request failed: {e=}")
        raise_client_exception(request_log, "Request failed, please "
                               "contact support", 500)

    raise_client_exception(request_log,
                           "No parameters passed for Tenant filter",
                           400)


def _get_tenants_by_user(request: Request, request_log: Any):
    _role = MongoRole()
    user_roles = _role.get_user_roles(request)

    _tenant = MongoTenant()
    tenants_obj = _tenant.get_tenants_by_ids([t for t in user_roles.keys()])

    return [create_tenant_obj(tenant) for tenant in tenants_obj]


@tenant_router.get("/id/{tenant_id}",
                   dependencies=[Depends(HeaderAuth(scope="ocp:tenant:read"))])
async def get_tenant_by_id(request: Request,
                           tenant_id: str):
    '''
       Get Tenant by ID
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _get_tenant_by_id(request, request_log, tenant_id)


def _get_tenant_by_id(request: Request, request_log: Any, tenant_id: str):
    validate_tenant_id(tenant_id)
    _tenants = MongoTenant()
    tenant = _tenants.get_tenant_by_id(tenant_id)
    user_claims = oidc._get_auth_status_claims(request, request_log)
    user_id = user_claims['ntid'] if 'ntid' in user_claims.keys() else None
    if tenant:
        if not user_id:
            return create_tenant_obj(tenant)
        if oidc.user_has_required_role(request, tenant_id, ['owners', 'ops']):
            return create_tenant_obj(tenant)

        raise_client_exception(
            request_log,
            f"Forbidden: User {user_id} does not have privileges on Tenant "
            f"'{tenant['name']}'",
            403
        )

    raise_client_exception(request_log,
                           f"No tenants found for Id, {tenant_id}",
                           404)


@tenant_router.get("/slug/{slug}",
                   dependencies=[Depends(HeaderAuth(scope="ocp:tenant:read"))])
async def get_tenant_by_slug(request: Request, slug: str):
    '''
       Get Tenant by slug
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _get_tenant_by_slug(request, request_log, slug)


def _get_tenant_by_slug(request: Request, request_log: Any, slug: str):
    validate_string(slug, "slug")
    _tenant = MongoTenant()
    tenant = _tenant.get_tenant_by_slug(slug)
    user_claims = oidc._get_auth_status_claims(request, request_log)
    user_id = user_claims['ntid'] if 'ntid' in user_claims.keys() else None
    if tenant:
        if not user_id:
            return create_tenant_obj(tenant)
        if oidc.user_has_required_role(request, str(tenant['_id']),
                                       ['owners', 'ops', 'users', 'collabs']):
            return create_tenant_obj(tenant)

        raise_client_exception(
            request_log,
            f"Forbidden: User {user_id} does not have privileges on Tenant "
            f"'{tenant['name']}'",
            403
        )

    raise_client_exception(request_log,
                           f"No Tenant found for slug, '{slug}'",
                           404)


def _get_tenants_by_regex_search(
    request: Request, request_log: Any, tenant_name_regex: str
):
    validate_regex(tenant_name_regex)
    _tenant = MongoTenant()
    tenants_obj = _tenant.get_tenants_by_name_regex(tenant_name_regex)

    if tenants_obj:
        tenants = []
        for tenant in tenants_obj:
            if 'authorization' not in request.headers.keys():
                if not oidc.user_has_required_role(request, tenant['_id'],
                                                   ['owners', 'ops', 'users', 'collabs']):
                    continue
            tenants.append(Tenant(id=str(tenant['_id']),
                                  name=tenant['name'],
                                  description=tenant['description'],
                                  tenant_apps=tenant.get('tenant_apps', []),
                                  slug=tenant['slug'],
                                  git_repo=tenant['git_repo'],
                                  created=tenant['created'],
                                  updated=tenant['updated'],
                                  delete_ts=tenant['delete_ts']))
        return tenants

    raise Exception(
        {'method': 'GET',
         'status': "404",
         'url': None,
         'error': "No Tenants found for name regex, "
         f"'{tenant_name_regex}'"})


@tenant_router.get("/devhubid/{devhubid}",
                   dependencies=[Depends(HeaderAuth(scope="ocp:tenant:read"))])
async def get_tenants_by_devhubid(request: Request, devhubid: DevhubAppID):
    '''
        Get Tenants by DevHubID
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    # _tenant.get_tenants_by_devhubid
    user_claims = oidc._get_auth_status_claims(request, request_log)
    user_id = user_claims['ntid'] if 'ntid' in user_claims.keys() else None
    _tenant = MongoTenant()

    results = _tenant.get_tenants_by_devhubid(devhubid)
    tenants = []
    if len(results) > 0:
        for tenant in results:
            authd = False
            if user_id:
                # TODO - build bulk method to check role for many tenants at once
                if oidc.user_has_required_role(request, str(tenant['_id']),
                                               ['owners', 'ops', 'users', 'collabs']):
                    authd = True
            if not user_id or authd:
                tenants.append(
                    Tenant(id=str(tenant['_id']),
                           name=tenant['name'],
                           description=tenant['description'],
                           tenant_apps=tenant.get('tenant_apps', []),
                           slug=tenant['slug'],
                           git_repo=tenant['git_repo'],
                           created=tenant['created'],
                           updated=tenant['updated'],
                           delete_ts=tenant['delete_ts']))
    return tenants


# TODO - migrate to mongo module
def delete_group(
    request: Request, create_req: create_tenant_request, group_id
):
    try:
        _ad_group = MongoADGroup()
        adc = ADClient()

        logger.info(
            f"Deleting AD group: {group_id} for Tenant, '{create_req.name}'"
        )

        adc.delete_ad_group(group_id)
        _ad_group.delete_one({'oid': str(group_id)})
    except Exception as e:
        logger.error(
            f"Error deleting AD group {group_id} for Tenant "
            f"'{create_req.name}': {e}"
        )
        raise


def provision_ad_groups(request: Request,
                        create_req: create_tenant_request,
                        new_tenant):
    try:
        _ad_group = MongoADGroup()
        _role = MongoRole()
        adc = ADClient()

        logger.info(
            f"Provisioning AD groups for Tenant, '{new_tenant['name']}'"
        )

        user_claims = oidc._get_auth_status_claims(request, "null")
        user_obj_id = user_claims['oid']
        user_access_token = request.cookies['access_token']

        t_groups = adc.provision_tenant_groups(
            user_access_token, new_tenant['name'], user_obj_id
        )
        g_objs = t_groups['group_objects']

        t_ad_groups = {
            g: {
                "oid": g_objs[g]['id'],
                "name": g_objs[g]['displayName'],
                "desc": g_objs[g]['description']
            } for g in g_objs.keys()
        }

        for group_info in t_ad_groups.values():
            # TODO - why query? wouldn't insert fail throw exception?
            # if not _ad_group.find_one({'oid': group_info['oid']}):
            _ad_group.insert_one(group_info)

        new_role = {
            'tenant_id': str(new_tenant['_id']),
            'owners': t_ad_groups['owners']['oid'],
            'ops': t_ad_groups['ops']['oid'],
            'users': t_ad_groups['users']['oid'],
            'collabs': t_ad_groups['collabs']['oid']
        }

        _role.insert_one(new_role)

        # Why wait?
        # time.sleep(3)
        return [
            t_ad_groups['owners']['oid'],
            t_ad_groups['ops']['oid'],
            t_ad_groups['users']['oid'],
            t_ad_groups['collabs']['oid']
        ]

    except Exception as e:
        logger.error(
            f"Error provisioning AD groups "
            f"for Tenant '{new_tenant['name']}': {e}"
        )
        raise


# TODO - migrate to git module
def provision_github_resources(new_tenant, user_ntid):
    try:
        ghc = GHClient()
        logger.info(
            f"Provisioning Github Repository "
            f"for Tenant, '{new_tenant['name']}'"
        )

        ghc.provision_tenant_repo(
            new_tenant['slug'], new_tenant['tenant_apps']
        )
        ghc.provision_repo_team(new_tenant['slug'])
        ghc.add_members_to_repo_team(new_tenant['slug'], [user_ntid])
        return [f"{new_tenant['slug']}_owners", new_tenant['slug']]

    except Exception as e:
        logger.error(
            f"Error provisioning GitHub resources "
            f"for Tenant '{new_tenant['name']}': {e}"
        )
        raise


# TODO - migrate this method to pillars module
def provision_visualization_instance(new_tenant):
    try:
        logger.info(
            f"Provisioning Visualization "
            f"Instance for Tenant, '{new_tenant['name']}'"
        )
        # TODO - here we already have the oids that were just
        # provisioned, just pass oids here instead of additional
        # db query
        ad_groups = MongoRole().get_ad_groups_oids_by_tenant_id(
            str(new_tenant['_id'])
        )

        pfields = [
            {
                "name": "name",
                "value": str(new_tenant['name'])
            }
        ]

        p_req_pillar = {
            "instance_name": str(new_tenant['name']),
            "tenant_id": str(new_tenant['_id']),
            "tenant_name": str(new_tenant['name']),
            "git_repo": str(new_tenant['git_repo']),
            "devhub_ids": new_tenant['tenant_apps'],
            "ad_groups": ad_groups,
            "fields": pfields
        }
        logger.info(f"Sending: {p_req_pillar} to the Pillar API")
        pillars.provision_instance(p_req_pillar, 'visualization')
    except Exception as e:
        logger.error(
            f"Error provision visualization instance "
            f"for tenant '{new_tenant['slug']}': {e}"
        )
        raise


def send_provision_email(tenant_slug, user_claims, new_tenant):
    try:
        email_sender = EmailClient()
        email_sender.send_email(
            '<EMAIL>',
            user_claims['email'],
            user_claims['name'],
            new_tenant['name'],
            new_tenant['description'],
            'Tenant Creation Completed',
            tenant_slug
        )
        logger.info(f"Provision email sent to {user_claims['email']} for "
                    f"tenant '{tenant_slug}'")

    except Exception as e:
        logger.error("Error sending provision email to "
                     f"{user_claims['email']} for tenant "
                     f"'{tenant_slug}': {e}")
        raise


# TODO - migrate to mongo module
def _delete_role_and_ad_groups(tenant_id):
    _role = MongoRole()
    _ad_group = MongoADGroup()

    # Find the role document
    # TODO - why query? if insert failed, then provision failed, we can
    # issue delete whether or not record exists, just log on failure
    role = _role.find_one({"tenant_id": str(tenant_id)})
    if not role:
        logger.info(f"Role with tenant ID {tenant_id} not found.")
        return

    # TODO - if group provisioning was successful, then we have OIDs to
    # pass in to this call
    # List of OIDs to delete from ad_group
    oids_to_delete = [
        role.get("owners"),
        role.get("ops"),
        role.get("users"),
        role.get("collabs")
    ]

    # Delete the role document
    _role.delete_one({"_id": ObjectId(role['_id'])})
    logger.info(f"Deleted role with tenant ID {tenant_id}.")

    # Delete corresponding ad_group documents
    _ad_group.delete_many({"oid": {"$in": oids_to_delete}})
    logger.info(f"Deleted ad_group documents with OIDs: {oids_to_delete}")


@tenant_router.post("/create",
                    dependencies=[Depends(HeaderAuth(scope="ocp:tenant:provision"))])
async def create_tenant(request: Request,
                        create_req: create_tenant_request):
    '''
       Create new Tenant \n'name' is a unique field
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _create_tenant(request, request_log, create_req)


def _create_tenant(request: Request,
                   request_log: dict,
                   create_req: create_tenant_request):
    _tenants = MongoTenant()
    _role = MongoRole()
    ghc = GHClient()

    create_req.name = create_req.name.strip()
    create_req.description = create_req.description.strip()

    existing_tenant = _tenants.find_one({'name': create_req.name})
    if existing_tenant:
        raise_client_exception(
            request_log,
            f"Error: Tenant '{existing_tenant['name']}' already exists",
            409
        )

    tenant_slug = slugify(create_req.name, separator="-", allow_unicode=False)
    existing_tenant_slug = _tenants.find_one({'slug': tenant_slug})
    if existing_tenant_slug:
        raise_client_exception(
            request_log,
            f"Error: Internal tenant name ({tenant_slug}) already exists; "
            "choose a different Tenant name!",
            409
        )

    user_claims = oidc._get_auth_status_claims(request, request_log)
    user_ntid = user_claims['ntid']

    created_resources = {
        "ad_groups": [],
        "git_repo": None,
        "git_team": None,
        "tenant": None
    }

    try:
        # Insert tenant record into MongoDB
        _tenants.insert_one({
            'name': create_req.name,
            'description': create_req.description,
            'tenant_apps': create_req.tenant_apps,
            'slug': tenant_slug,
            'git_repo': tenant_slug,
            'created': int(time.time()),
            'updated': int(time.time()),
            'delete_ts': 0
        })
        new_tenant = _tenants.get_tenant_by_name(create_req.name)
        created_resources["tenant"] = new_tenant['_id']

        # Provision AD groups
        ad_groups = provision_ad_groups(request, create_req, new_tenant)
        created_resources["ad_groups"].extend(ad_groups)

        # Provision GitHub resources
        git_team, git_repo = provision_github_resources(new_tenant, user_ntid)
        created_resources["git_team"] = git_team
        created_resources["git_repo"] = git_repo
    except Exception as e:

        logger.exception("CLEANING UP RESOURCES PROVISIONED FOR NEW TENANT,"
                         f"'{create_req.name}': {e=}")

        # Clean up created AD groups
        if created_resources["ad_groups"]:
            time.sleep(5)
            for group_id in created_resources["ad_groups"]:
                try:
                    delete_group(request, create_req, group_id)
                except Exception as cleanup_e:
                    logger.error(
                        f"Failed to cleanup AD group {group_id}: {cleanup_e=}"
                    )
            _role.delete_one({'tenant_id': str(created_resources["tenant"])})

        # Clean up GitHub team
        if created_resources["git_team"]:
            try:
                ghc.cleanup_repo_team(created_resources["git_team"])
            except Exception as e:
                logger.error(f"Failed to cleanup GitHub team "
                             f"{created_resources['git_team']}: {e=}")

        # Clean up GitHub repository
        if created_resources["git_repo"]:
            try:
                ghc.cleanup_tenant_repo(created_resources["git_repo"])
            except Exception as e:
                logger.error("Failed to cleanup GitHub repository "
                             f"{created_resources['git_repo']}: {e=}")

        # Delete tenant record from MongoDB
        if created_resources["tenant"]:
            try:
                _tenants.delete_one({'_id': created_resources["tenant"]})
            except Exception as e:
                logger.error("Failed to delete tenant record "
                             f"{created_resources['tenant']}: {e=}")

        # Delete role and ad group records
        if created_resources["tenant"]:
            try:
                _delete_role_and_ad_groups(created_resources["tenant"])
            except Exception as e:
                logger.error("Failed to delete role/ad_group records "
                             f"{created_resources['tenant']}: {e=}")

        raise_client_exception(request_log,
                               "Tenant creation failed, please contact "
                               + "support",
                               500)

    try:
        # Provision Visualization Instance
        provision_visualization_instance(new_tenant)
    except Exception as e:
        logger.exception("Visualization instance provision failed for Tenant "
                         f"'{create_req.name}': {e=}")

    try:
        # Send provision email
        send_provision_email(
            tenant_slug,
            oidc._get_auth_status_claims(request, request_log),
            new_tenant
        )

        tenant_provision_c.inc()
    except Exception as e:
        logger.exception(e)

    return create_tenant_obj(new_tenant)


@tenant_router.post("/update",
                    dependencies=[Depends(HeaderAuth(scope="ocp:tenant:write"))])
async def update_tenant(request: Request,
                        update_req: update_tenant_request):
    '''
       Update Tenant record
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _update_tenant(request, request_log, update_req)


def _update_tenant(request: Request,
                   request_log: Any,
                   update_req: update_tenant_request):
    # Validate input
    if (not update_req.tenant_id and not update_req.tenant_name) or \
            (update_req.tenant_id and update_req.tenant_name):
        raise_client_exception(
            request_log,
            "Either tenant_id or tenant_name must be provided to update "
            "Tenant record",
            400
        )
        return

    _tenants = MongoTenant()
    try:
        user_id = oidc._get_auth_status_claims(request, request_log)['ntid']
    except KeyError:
        raise_client_exception(
            request_log,
            "Authentication error: Missing or invalid claims",
            401
        )

    tenant_query = (
        {"name": update_req.tenant_name}
        if update_req.tenant_name
        else {"_id": ObjectId(update_req.tenant_id)}
    )

    tenant_to_update = _tenants.find_one(tenant_query)
    if not tenant_to_update:
        raise_client_exception(
            request_log,
            f"Error: Tenant, {tenant_query}, does not exist",
            404
        )

    if oidc.user_has_required_role(request, tenant_to_update['_id'],
                                   ['owners', 'ops']):
        new_values = {'updated': int(time.time())}
        if update_req.update_description:
            new_values['description'] = update_req.update_description
        if update_req.update_tenant_apps:
            new_values['tenant_apps'] = update_req.update_tenant_apps

        try:
            result = _tenants.update_one(tenant_query, new_values)
        except errors.PyMongoError as e:
            logger.exception("Database error: Failed to update tenant: "
                             f"{e=}")
            raise_client_exception(
                request_log,
                "Request failed, please contact support",
                500
            )

        if result.modified_count == 0:
            logger.error("Error: Tenant update failed for "
                         f"{tenant_query}")
            raise_client_exception(
                request_log,
                "Request failed, please contact support",
                500
            )

        try:
            updated_doc = _tenants.get_tenant_by_id(
                str(tenant_to_update['_id'])
            )
        except errors.PyMongoError as e:
            logger.exception("Database error: Failed to retrieve updated"
                             f"tenant: {str(e)}")
            raise_client_exception(
                request_log,
                "Request failed, please contact support",
                500
            )

        return {"status": "success",
                "updated_record": create_tenant_obj(updated_doc)}

    raise_client_exception(
        request_log,
        f"Forbidden: User {user_id} does not have privileges on "
        f"Tenant '{tenant_to_update['name']}'",
        403
    )


@tenant_router.post("/delete",
                    dependencies=[Depends(HeaderAuth(scope="ocp:tenant:delete"))])
async def delete_tenant(request: Request,
                        id: Optional[str] = None,
                        name: Optional[str] = None,
                        revert_delete: Optional[bool] = None):
    '''
       Mark Tenant for deletion, after 7 days the Tenant record and relations
       to AD groups and Instances will be deleted from the O11Y Control Plane
       backend and each Pillar backend related Instances belong to \n
       Either 'id' or 'name' parameter must be passed in request to identify
       Tenant, not both \n
       If 'revert_delete' is set to True, then Tenant that was previously
       marked for deletion will be unmarked
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _delete_tenant(request, request_log, id, name,
                          revert_delete)


# TODO - represent request with model, and perform validation with model
def _delete_tenant(request: Request,
                   request_log: Any,
                   id: Optional[str] = None,
                   name: Optional[str] = None,
                   revert_delete: Optional[bool] = None):
    # Validate input
    if (not id and not name) or (id and name):
        raise_client_exception(
            request_log,
            "Error: Either id or name must be provided to update Tenant "
            "record",
            400
        )

    if id:
        try:
            validate_string(id, 'tenant_id')
        except ValueError as ve:
            raise_client_exception(
                request_log,
                f"Validation error for tenant_id: {str(ve)}",
                400
            )
    if name:
        try:
            validate_string(name, 'tenant_name')
        except ValueError as ve:
            raise_client_exception(
                request_log,
                f"Validation error for tenant_name: {str(ve)}",
                400
            )

    _tenant = MongoTenant()

    tenant_query = {"_id": ObjectId(id)} if id else {"name": name}
    try:
        tenant = _tenant.find_one(tenant_query)
    except errors.PyMongoError as e:
        logger.exception("Database error: Failed to retrieve Tenant record "
                         f"for query, {tenant_query}: {e=}")
        raise_client_exception(
            request_log,
            "Request failed, please contact support",
            500
        )

    if not tenant:
        raise_client_exception(
            request_log,
            f"No Tenant found matching query: {str(tenant_query)}",
            404
        )

    if oidc.user_has_required_role(request, str(tenant['_id']), ['owners']):
        try:
            instances = pillars.get_instances_by_tenant(str(tenant['_id']))
        except Exception as e:
            logger.exception("Error retrieving Instances for Tenant, "
                             f"'{tenant['name']}': {e=}")
            raise_client_exception(
                request_log,
                500
            )

        if len(instances['instances']) > 0:
            raise_client_exception(
                request_log,
                "Cannot delete Tenant with associated instances",
                400
            )

        try:
            result = _tenant.mark_for_delete(tenant_query, not revert_delete)
        except Exception as e:
            logger.exception(f"Tenant mark for delete failed: {e=}")
            raise_client_exception(
                request_log,
                "Request failed, please contact support",
                500
            )

        if result.modified_count == 0:
            logger.error("Error: Tenant mark for delete failed for query: "
                         f"{tenant_query}")
            raise_client_exception(
                request_log,
                "Request failed, please contact support",
                500
            )

        # what are we checking if mongo confirmed update already?
        # try:
        #     updated_doc = _tenant.get_tenant_by_id(str(tenant['_id']))
        # except errors.PyMongoError as e:
        #     raise_client_exception(
        #         request_log,
        #         f"Database error: Failed to retrieve updated tenant: {str(e)}", # noqa E501
        #         500
        #     )

        return {"status": "success"}

        # "updated_record": create_tenant_obj(updated_doc)}

    user_id = oidc._get_auth_status_claims(request, request_log)['ntid']
    raise_client_exception(
        request_log,
        f"Forbidden: User {user_id} does not have privileges on "
        f"Tenant '{tenant['name']}'",
        403
    )


@tenant_router.post("/force_delete",
                    dependencies=[Depends(HeaderAuth(scope="ocp:tenant:delete"))])
async def force_delete_tenant(request: Request,
                              id: Optional[str] = None,
                              name: Optional[str] = None):
    '''
        Execute force delete of Tenant, issuing immediate deletion
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    return _force_delete_tenant(request, request_log, id, name)


# TODO - represent request with model, and perform validation with model
def _force_delete_tenant(request: Request,
                         request_log: Any,
                         id: Optional[str] = None,
                         name: Optional[str] = None):
    _tenant = MongoTenant()
    adc = ADClient()
    ghc = GHClient()
    err_msg = ""
    error = False

    if (not id and not name) or (id and name):
        raise_client_exception(
            request_log,
            "Error: Either id or name must be provided to update Tenant "
            "record",
            400
        )

    if id:
        validate_string(id, 'tenant_id')
    if name:
        validate_string(name, 'tenant_name')

    tenant_query = {"_id": ObjectId(id)} if id else {"name": name}

    tenant = _tenant.find_one(tenant_query)

    if not tenant:
        raise_client_exception(
            request_log,
            f"No Tenant found matching query, {tenant_query}",
            404
        )

    # Authorization check
    if not oidc.user_has_required_role(request, str(tenant['_id']),
                                       ['owners']):
        user_id = oidc._get_auth_status_claims(request, request_log)['ntid']
        raise_client_exception(
            request_log,
            f"Forbidden: User {user_id} does not have privileges on "
            f"Tenant '{tenant['name']}'",
            403
        )

    # Step 1: Retrieve instances associated with the tenant
    try:
        instances = pillars.get_instances_by_tenant(str(tenant['_id']))
        logger.debug(f"Instances for tenant {tenant['_id']}: {instances}")
    except Exception:
        logger.exception("Error retrieving instances for tenant")
        raise_client_exception(request_log,
                               "Failed to retrieve instances for tenant.", 500)

    # Step 2: Filter visualization and metrics instances if any exist
    visualization_metrics_instances = [
        instance for instance in instances.get('instances', [])
        if instance['type'] in ['visualization', 'metrics-ng']
    ]

    # Step 3: Attempt to delete visualization instances
    if visualization_metrics_instances:
        try:
            delete_response = (
                pillars.delete_instances_by_tenant(str(tenant['_id'])))
            logger.info(f"Deletion response: {delete_response['message']}")
        except Exception as e:
            logger.error(f"Error deleting instances \
                         for Tenant '{tenant['_id']}': {e}")
            error = True
            err_msg += f"Error deleting instances: {str(e)} || "

    # Step 4: Confirm no remaining instances
    try:
        remaining_instances = (
            pillars.get_instances_by_tenant(str(tenant['_id'])))
        logger.debug(f"Remaining instances after deletion \
                     attempt for tenant \
                     {tenant['_id']}: {remaining_instances}")
    except Exception as e:
        logger.exception(f"Failed to check remaining \
                         instances after deletion attempt: {e}")
        raise_client_exception(request_log, f"Error checking remaining \
                               instances for tenant: {e}", 500)

    # Step 5: Add error message if instances remain
    if remaining_instances.get('instances'):
        instance_details = "; ".join(
            f"Type: {instance['type']}, ID: {instance['id']}, "
            f"Name: {instance['name']}"
            for instance in remaining_instances['instances']
        )
        err_msg += (
            f"Cannot delete Tenant {tenant['_id']} "
            f"with associated instances - {instance_details}"
        )

    # Raise error if there are issues or instances remain
    if error or remaining_instances.get('instances'):
        logger.error(f"Force delete error: {err_msg.strip(' || ')}")
        raise_client_exception(request_log, err_msg.strip(" || "), 400)

    # Proceed with other cleanup steps if instance deletion is successful
    try:
        adc.cleanup_tenant_groups(tenant['name'])
    except Exception as e:
        logger.error(f"Error deleting Tenant groups, no resources for Tenant "
                     f"'{tenant['name']}' have been deleted: {e}")
        error = True
        err_msg += "Failed to delete Tenant AD groups, no "\
            + f"resources for Tenant '{tenant['name']}' were deleted...  ||  "
    try:
        ghc.cleanup_repo_team(tenant['slug'])
    except Exception as e:
        logger.error("Error deleting Tenant repo team, AD groups for Tenant "
                     f"'{tenant['name']}' have already been deleted: {e}")

        error = True
        err_msg += "Failed to delete repo team for Tenant, "\
            + f"'{tenant['slug']}', Tenant AD groups "\
            + "have been deleted, remaining resources are "\
            + "Tenant repo and Tenant records...  ||  "
    try:
        ghc.cleanup_tenant_repo(tenant['slug'])
    except Exception as e:
        logger.error(f"Error deleting Tenant repo, '{tenant['slug']}', AD "
                     f"groups and Tenant repo have already been deleted: {e}")
        error = True
        err_msg += "Failed to delete Tenant repo, "\
            + f"'{tenant['slug']}', AD groups and repo team "\
            + "have already been deleted, remaining "\
            + "resources are Tenant records...  ||  "

    try:
        # TODO - break up into multiple calls, or use help method to call each
        #   class representing a mongo collection
        _tenant.delete_by_tenant_id(tenant["_id"])
    except Exception as e:
        logger.error(f"Error deleting tenant by ID: {e}")
        error = True
        err_msg += "Failed to delete Tenant records, these are "\
            + "the only remaining resources to delete for "\
            + f"Tenant '{tenant['name']}'..."

    if error:
        raise_client_exception(request_log, err_msg, 500)

    # TODO - record any failures, and report to client if db record delete
    #  and ad group delete fails - otherwise tenant still shows up

    return {
        "status": "success",
        "message": f"Tenant {tenant['_id']} force deleted successfully"
    }


@tenant_router.get("/get_user_roles",
                   dependencies=[Depends(HeaderAuth(scope="invalid"))])
async def get_user_roles(request: Request):
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    try:
        return _get_user_roles(request, request_log)
    except Exception as e:
        logger.exception(f"Failed to retrieve user roles: {e=}")
        raise_client_exception(request_log, "Request failed, please "
                               "contact support", 500)


def _get_user_roles(request: Request, request_log: Any):
    _role = MongoRole()
    return _role.get_user_roles(request)


@tenant_router.get("/get_roles",
                   dependencies=[Depends(HeaderAuth(scope="invalid"))])
async def get_roles(request: Request):
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    try:
        return _get_roles(request, request_log)
    except Exception as e:
        logger.exception(f"Failed to retrieve roles: {e=}")
        raise_client_exception(request_log, "Failed to retrieve roles, "
                               "please contact support", 500)


def _get_roles(request: Request, request_log: Any):
    return {
        "tenant": {
            "read": ["owners", "ops", "users", "collabs"],
            "write": ["owners", "ops"],
            "delete": ["owners"],
            "audit": ["owners"],
            "provision": ["owners"]
        }
    }


# TODO - following 4 functions, migrate to appropriate modules,
#      these represent functionality defined in client modules
def rename_ad_groups(tenant_name: str, new_tenant_name: str):
    adc = ADClient()
    return adc.rename_tenant_groups(tenant_name, new_tenant_name)


def rename_git_repo(old_repo_name: str, new_repo_name: str):
    ghc = GHClient()
    return ghc.rename_tenant_repo(old_repo_name, new_repo_name)


def rename_git_team(old_repo_name: str, new_repo_name: str):
    ghc = GHClient()
    return ghc.rename_repo_team(old_repo_name, new_repo_name)


def rename_tenant_in_db(
    tenant_id: str, new_name: str, new_slug: str
):
    _tenants = MongoTenant()
    try:
        result = _tenants.update_one(
            {"_id": ObjectId(tenant_id)},
            {
                "name": new_name,
                "slug": new_slug,
                "git_repo": new_slug,
                "updated": int(time.time())
            }
        )
        return result.modified_count > 0
    except Exception as e:
        logger.error(f"Error renaming tenant in database: {e}")
        return False


@tenant_router.post("/rename",
                    dependencies=[Depends(HeaderAuth(scope="ocp:tenant:write"))])
async def rename_tenant(request: Request,
                        tenant_id: str,
                        new_name: str):
    '''
    '''
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")

    _tenants = MongoTenant()
    tenant = _tenants.get_tenant_by_id(tenant_id)
    if not tenant:
        raise_client_exception(
            request_log,
            f"Error: Tenant with ID '{tenant_id}' does not exist",
            404
        )
    new_name = new_name.strip()
    old_name = tenant['name']
    old_slug = tenant['slug']
    new_slug = slugify(new_name, separator='-', allow_unicode=False)

    existing_tenant = _tenants.find_one({'name': new_name})
    if existing_tenant:
        raise_client_exception(
            request_log,
            f"Error: Tenant '{new_name}' already exists",
            409
        )

    existing_tenant_slug = _tenants.find_one({'slug': new_slug})
    if existing_tenant_slug:
        raise_client_exception(
            request_log,
            f"Error: Tenant with slug '{new_slug}' already exists",
            409
        )

    # TEMPORARY until pillars adopt rename_tenant pillar contract
    if oidc.user_has_required_role(request, str(tenant['_id']), ['owners']):
        instances = pillars.get_instances_by_tenant(str(tenant['_id']))
        if len(instances['instances']) > 0:
            raise_client_exception(
                request_log,
                "Error: Cannot rename tenant with associated instances",
                400
            )

    # TODO - need to think how to handle failures below, exception at
    # any point during reverts would not be handled - consider queuing system

    exp_message = ""
    try:
        if not rename_ad_groups(old_name, new_name):
            exp_message += f" | Call failed, rename_ad_groups('{old_name}', "\
                + f"'{new_name}')"
            raise Exception("AD Group rename failed")

        if not rename_git_team(old_slug, new_slug):
            rename_ad_groups(new_name, old_name)
            exp_message += f" | Call failed, rename_git_team('{old_slug}', "\
                + f"'{new_slug}'), attempted to rename ad groups "\
                + "back to original - rename_ad_groups('"\
                + f"{new_name}', '{old_name}')"
            raise Exception("Git repository team rename failed")

        if not rename_git_repo(old_slug, new_slug):
            rename_git_team(new_slug, old_slug)
            rename_ad_groups(new_name, old_name)
            exp_message += f" | Call failed, rename_git_repo('{old_slug}',"\
                + f"'{new_slug}'), attempted to renaame ad groups"\
                + f"to original, rename_ad_groups('{new_name}', "\
                + f"'{old_name}'), and attempted to rename git "\
                + f"team to original, rename_git_team('{new_name}"\
                + f"', '{old_name}')"
            raise Exception("Git repository rename failed")

        if not rename_tenant_in_db(tenant_id, new_name, new_slug):
            rename_git_repo(new_slug, old_slug)
            rename_git_team(new_slug, old_slug)
            rename_tenant_in_db(tenant_id, old_name, old_slug)
            exp_message += " | Call failed, rename_tenant_in_db('"\
                           + f"{tenant_id}',  '{new_name}', '{new_slug}'),"\
                           + ", attempted to renaame ad groups"\
                           + f"to original, rename_ad_groups('{new_name}', "\
                           + f"'{old_name}'), and attempted to rename git "\
                           + f"team to original, rename_git_team('{new_name}"\
                           + f"', '{old_name}'), and attempted to rename "\
                           + "tenant db record, rename_tenant_in_db('"\
                           + f"{tenant_id}', '{old_name}', '{old_slug}')"
            raise Exception("Database rename failed")

        return {
            "status": "success",
            "msg": f"Tenant '{old_name}' renamed to '{new_name}'"
        }

    except Exception as e:
        logger.exception(f"Rename reqeust failed: {e=}  {exp_message}")
        raise_client_exception(request_log,
                               "Request failed, please contact support",
                               500)


# @tenant_router.post("/add_instances",
#                     dependencies=[Depends(HeaderAuth())])
# async def add_instances_to_tenant(request: Request,
#                                   add_instances_request:
#                                   add_instances_request):
#     '''
#        Add Instance to Tenant
#        Either tenant_id or tenant_name is required
#     '''
#     # TODO - validation must be run before logging any data from
#     #       request object
#
#     request_log = await get_request_log(request)
#     logger.info(f"{request_log}")
#
#
#     return _add_instances_to_tenant(request, request_log,
#                                     add_instances_request)
#
#
# def _add_instances_to_tenant(request: Request,
#                              request_log: Any,
#                              add_instances_request: add_instances_request):
#     tenant_query = {}
#     if add_instances_request.tenant_id:
#         tenant_query['_id'] = ObjectId(add_instances_request.tenant_id)
#     elif add_instances_request.tenant_name:
#         tenant_query['name'] = add_instances_request.tenant_name
#
#     _tenant = mongo.Tenant()
#     user_id = oidc._get_auth_status_claims(request, request_log)['ntid']
#     tenant = _tenant.find_one(tenant_query)
#
#     if not tenant:
#         raise HTTPException(status_code=400,
#                             detail="Tenant does not exist, "
#                             + f"{add_instances_request.tenant_id}")
#     elif _tenant.has_access(add_instances_request.tenant_id, request):
#
#         add_instances_resp = metrics_api.import_instances_to_tenant(
#                                 str(tenant['_id']),
#                                 add_instances_request.instance_ids)
#
#         logger.debug(f"metrics_api import_instances resp: "
#                      f"'{add_instances_resp}'")
#
#         return {"status": "success", "msg": "Instances, "
#                 + f"{[iid for iid in add_instances_request.instance_ids]}"
#                 + f", added to Tenant '{tenant['name']}'"}
#     else:
#         msg = f"Forbidden: User {user_id} does not have privileges "\
#             + f"on Tenant '{tenant['name']}'"
#         logger.error(msg)
#         raise HTTPException(status_code=403, detail=msg)
