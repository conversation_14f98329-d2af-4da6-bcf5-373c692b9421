# TODO - replace this generated code with calls to a Python library
# flake8: noqa

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: types.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import src.library.protobuf.gogo_pb2 as gogo__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0btypes.proto\x12\nprometheus\x1a\ngogo.proto\"\xf8\x01\n\x0eMetricMetadata\x12\x33\n\x04type\x18\x01 \x01(\x0e\x32%.prometheus.MetricMetadata.MetricType\x12\x1a\n\x12metric_family_name\x18\x02 \x01(\t\x12\x0c\n\x04help\x18\x04 \x01(\t\x12\x0c\n\x04unit\x18\x05 \x01(\t\"y\n\nMetricType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07\x43OUNTER\x10\x01\x12\t\n\x05GAUGE\x10\x02\x12\r\n\tHISTOGRAM\x10\x03\x12\x12\n\x0eGAUGEHISTOGRAM\x10\x04\x12\x0b\n\x07SUMMARY\x10\x05\x12\x08\n\x04INFO\x10\x06\x12\x0c\n\x08STATESET\x10\x07\"*\n\x06Sample\x12\r\n\x05value\x18\x01 \x01(\x01\x12\x11\n\ttimestamp\x18\x02 \x01(\x03\"U\n\x08\x45xemplar\x12\'\n\x06labels\x18\x01 \x03(\x0b\x32\x11.prometheus.LabelB\x04\xc8\xde\x1f\x00\x12\r\n\x05value\x18\x02 \x01(\x01\x12\x11\n\ttimestamp\x18\x03 \x01(\x03\"\x87\x04\n\tHistogram\x12\x13\n\tcount_int\x18\x01 \x01(\x04H\x00\x12\x15\n\x0b\x63ount_float\x18\x02 \x01(\x01H\x00\x12\x0b\n\x03sum\x18\x03 \x01(\x01\x12\x0e\n\x06schema\x18\x04 \x01(\x11\x12\x16\n\x0ezero_threshold\x18\x05 \x01(\x01\x12\x18\n\x0ezero_count_int\x18\x06 \x01(\x04H\x01\x12\x1a\n\x10zero_count_float\x18\x07 \x01(\x01H\x01\x12\x34\n\x0enegative_spans\x18\x08 \x03(\x0b\x32\x16.prometheus.BucketSpanB\x04\xc8\xde\x1f\x00\x12\x17\n\x0fnegative_deltas\x18\t \x03(\x12\x12\x17\n\x0fnegative_counts\x18\n \x03(\x01\x12\x34\n\x0epositive_spans\x18\x0b \x03(\x0b\x32\x16.prometheus.BucketSpanB\x04\xc8\xde\x1f\x00\x12\x17\n\x0fpositive_deltas\x18\x0c \x03(\x12\x12\x17\n\x0fpositive_counts\x18\r \x03(\x01\x12\x33\n\nreset_hint\x18\x0e \x01(\x0e\x32\x1f.prometheus.Histogram.ResetHint\x12\x11\n\ttimestamp\x18\x0f \x01(\x03\"4\n\tResetHint\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03YES\x10\x01\x12\x06\n\x02NO\x10\x02\x12\t\n\x05GAUGE\x10\x03\x42\x07\n\x05\x63ountB\x0c\n\nzero_count\",\n\nBucketSpan\x12\x0e\n\x06offset\x18\x01 \x01(\x11\x12\x0e\n\x06length\x18\x02 \x01(\r\"\xc0\x01\n\nTimeSeries\x12\'\n\x06labels\x18\x01 \x03(\x0b\x32\x11.prometheus.LabelB\x04\xc8\xde\x1f\x00\x12)\n\x07samples\x18\x02 \x03(\x0b\x32\x12.prometheus.SampleB\x04\xc8\xde\x1f\x00\x12-\n\texemplars\x18\x03 \x03(\x0b\x32\x14.prometheus.ExemplarB\x04\xc8\xde\x1f\x00\x12/\n\nhistograms\x18\x04 \x03(\x0b\x32\x15.prometheus.HistogramB\x04\xc8\xde\x1f\x00\"$\n\x05Label\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"1\n\x06Labels\x12\'\n\x06labels\x18\x01 \x03(\x0b\x32\x11.prometheus.LabelB\x04\xc8\xde\x1f\x00\"\x82\x01\n\x0cLabelMatcher\x12+\n\x04type\x18\x01 \x01(\x0e\x32\x1d.prometheus.LabelMatcher.Type\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x01(\t\"(\n\x04Type\x12\x06\n\x02\x45Q\x10\x00\x12\x07\n\x03NEQ\x10\x01\x12\x06\n\x02RE\x10\x02\x12\x07\n\x03NRE\x10\x03\"|\n\tReadHints\x12\x0f\n\x07step_ms\x18\x01 \x01(\x03\x12\x0c\n\x04\x66unc\x18\x02 \x01(\t\x12\x10\n\x08start_ms\x18\x03 \x01(\x03\x12\x0e\n\x06\x65nd_ms\x18\x04 \x01(\x03\x12\x10\n\x08grouping\x18\x05 \x03(\t\x12\n\n\x02\x62y\x18\x06 \x01(\x08\x12\x10\n\x08range_ms\x18\x07 \x01(\x03\"\xaf\x01\n\x05\x43hunk\x12\x13\n\x0bmin_time_ms\x18\x01 \x01(\x03\x12\x13\n\x0bmax_time_ms\x18\x02 \x01(\x03\x12(\n\x04type\x18\x03 \x01(\x0e\x32\x1a.prometheus.Chunk.Encoding\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\x0c\"D\n\x08\x45ncoding\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03XOR\x10\x01\x12\r\n\tHISTOGRAM\x10\x02\x12\x13\n\x0f\x46LOAT_HISTOGRAM\x10\x03\"a\n\rChunkedSeries\x12\'\n\x06labels\x18\x01 \x03(\x0b\x32\x11.prometheus.LabelB\x04\xc8\xde\x1f\x00\x12\'\n\x06\x63hunks\x18\x02 \x03(\x0b\x32\x11.prometheus.ChunkB\x04\xc8\xde\x1f\x00\x42\x08Z\x06prompbb\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'types_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'Z\006prompb'
  _EXEMPLAR.fields_by_name['labels']._options = None
  _EXEMPLAR.fields_by_name['labels']._serialized_options = b'\310\336\037\000'
  _HISTOGRAM.fields_by_name['negative_spans']._options = None
  _HISTOGRAM.fields_by_name['negative_spans']._serialized_options = b'\310\336\037\000'
  _HISTOGRAM.fields_by_name['positive_spans']._options = None
  _HISTOGRAM.fields_by_name['positive_spans']._serialized_options = b'\310\336\037\000'
  _TIMESERIES.fields_by_name['labels']._options = None
  _TIMESERIES.fields_by_name['labels']._serialized_options = b'\310\336\037\000'
  _TIMESERIES.fields_by_name['samples']._options = None
  _TIMESERIES.fields_by_name['samples']._serialized_options = b'\310\336\037\000'
  _TIMESERIES.fields_by_name['exemplars']._options = None
  _TIMESERIES.fields_by_name['exemplars']._serialized_options = b'\310\336\037\000'
  _TIMESERIES.fields_by_name['histograms']._options = None
  _TIMESERIES.fields_by_name['histograms']._serialized_options = b'\310\336\037\000'
  _LABELS.fields_by_name['labels']._options = None
  _LABELS.fields_by_name['labels']._serialized_options = b'\310\336\037\000'
  _CHUNKEDSERIES.fields_by_name['labels']._options = None
  _CHUNKEDSERIES.fields_by_name['labels']._serialized_options = b'\310\336\037\000'
  _CHUNKEDSERIES.fields_by_name['chunks']._options = None
  _CHUNKEDSERIES.fields_by_name['chunks']._serialized_options = b'\310\336\037\000'
  _METRICMETADATA._serialized_start=40
  _METRICMETADATA._serialized_end=288
  _METRICMETADATA_METRICTYPE._serialized_start=167
  _METRICMETADATA_METRICTYPE._serialized_end=288
  _SAMPLE._serialized_start=290
  _SAMPLE._serialized_end=332
  _EXEMPLAR._serialized_start=334
  _EXEMPLAR._serialized_end=419
  _HISTOGRAM._serialized_start=422
  _HISTOGRAM._serialized_end=941
  _HISTOGRAM_RESETHINT._serialized_start=866
  _HISTOGRAM_RESETHINT._serialized_end=918
  _BUCKETSPAN._serialized_start=943
  _BUCKETSPAN._serialized_end=987
  _TIMESERIES._serialized_start=990
  _TIMESERIES._serialized_end=1182
  _LABEL._serialized_start=1184
  _LABEL._serialized_end=1220
  _LABELS._serialized_start=1222
  _LABELS._serialized_end=1271
  _LABELMATCHER._serialized_start=1274
  _LABELMATCHER._serialized_end=1404
  _LABELMATCHER_TYPE._serialized_start=1364
  _LABELMATCHER_TYPE._serialized_end=1404
  _READHINTS._serialized_start=1406
  _READHINTS._serialized_end=1530
  _CHUNK._serialized_start=1533
  _CHUNK._serialized_end=1708
  _CHUNK_ENCODING._serialized_start=1640
  _CHUNK_ENCODING._serialized_end=1708
  _CHUNKEDSERIES._serialized_start=1710
  _CHUNKEDSERIES._serialized_end=1807
# @@protoc_insertion_point(module_scope)
