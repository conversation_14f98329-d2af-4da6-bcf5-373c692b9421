import typing
from typing import Optional, List, Union
from enum import Enum
from pydantic import BaseModel, validator, conint
from src.validation.input_validator import validate_input

DevhubAppID = conint(ge=1000, le=999999)


class ad_group(BaseModel):
    oid: str
    name: str
    description: str
    _validate_ = validator('oid', 'name', 'description',
                           allow_reuse=True)(validate_input)

# TODO - rename 'tenant_apps' to 'devhub_apps'


class Tenant(BaseModel):
    id: str
    name: str
    description: str
    tenant_apps: List[DevhubAppID]
    slug: str
    # owner: typing.List[ad_group]
    # instances: typing.List[int] #List of instance IDs
    git_repo: str
    created: int
    updated: int
    delete_ts: int
#    _validate_ = validator('id', 'name', 'description', 'slug', 'git_repo'
#                           'created', 'updated', 'delete_ts',
#                           allow_reuse=True)(validate_input)
# TODO - fix validation ^^


# class TenantGroup(BaseModel):
#     id: str
#     name: str
#     member: typing.List[str]    # Members are ADGroups
#     description: str
#     created: int
#     updated: int
#     _validate_ = validator('id', 'name', 'member', 'description', 'created',
#                            'updated', allow_reuse=True)(validate_input)


class ADGroup(BaseModel):
    id: str
    oid: str
    name: str
    description: str
    _validate_ = validator('id', 'oid', 'name', 'description',
                           allow_reuse=True)(validate_input)


class InstanceField(BaseModel):
    '''
        InstanceField represents any key/value
        pair defining Pillar specific metadata related to
        a Service Instance (deployed Pillar resource)
    '''
    key: str
    value: Union[str, List[int]]
    _validate_ = validator('key', 'value', allow_reuse=True)(validate_input)


class InstanceType(str, Enum):
    METRICS = "metrics"
    METRICSNG = "metrics-ng"
    LOGGING = "logging"
    TRACING = "tracing"
    MAAS = "maas"
    LAAS = "laas"
    VISUALIZATION = "visualization"


class InstanceHealth(str, Enum):
    NOMINAL = "nominal"
    DEGRADED = "degraded"
    HAZCON = "hazcon"
    OUTAGE = "outage"


class Instance(BaseModel):
    '''
        This model represents Instance objects as returned by Pillars.  The
        'fields' field represents any Pillar specific metadata.
    '''
    id: str
    name: str
    type: InstanceType
    status: str = "Unknown"
    tenant_id: Optional[str]
    tenant_name: Optional[str]
    devhub_ids: Optional[typing.List[DevhubAppID]]
    # git_repo: str
    fields: Optional[typing.List[InstanceField]]  # TODO - name 'metadata'
    health: Optional[InstanceHealth]
    # _validate_ = validator('id', 'name', 'type', 'tenant_id', 'tenant_name',
    #                        'fields', 'status',
    #                        allow_reuse=True)(validate_input)
    # TODO - review validation requirements (values here are
    #        not entered by user)

# class Pillar(BaseModel):
#     id: str
#     name: str
#     description: str
#     owner: str  # object id for ADGroup
#     url: str
#     _validate_ = validator('id', 'name', 'description', 'owner', 'url',
#                            allow_reuse=True)(validate_input)
