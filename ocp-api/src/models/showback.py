from datetime import datetime
from typing import List, Optional, Literal

from pydantic import BaseModel, Field, confloat

# Handle different Pydantic versions for HttpUrl import
try:
    from pydantic import HttpUrl
except ImportError:
    try:
        from pydantic.types import HttpUrl
    except ImportError:
        from pydantic.networks import HttpUrl


class ErrorResponse(BaseModel):
    title: str
    detail: str
    source: str
    code: str
    id: str
    idType: Literal["REQUEST_ID", "RESOURCE_ID", "RATE_ID", "TX_ID"]
    reference: Optional[HttpUrl]


class ResourceIDDatum(BaseModel):
    resourceID: str


class RateIDDatum(BaseModel):
    rateID: str


class ResourcesV1PostRequest(BaseModel):
    unit: str
    providerID: str


class ResourcesV1PostBulkRequest(BaseModel):
    resources: List[ResourcesV1PostRequest]


class ResourcesV1PatchRequest(BaseModel):
    resourceID: str
    unit: str
    providerID: str


class ResourcesV1PatchBulkRequest(BaseModel):
    resources: List[ResourcesV1PatchRequest]


class RatesV1PostRequest(BaseModel):
    resourceID: str
    partner: str
    consumerID: str
    costPerUnit: confloat(ge=0)
    costCurrencyCode: str
    capexPercent: confloat(ge=0, le=1)
    startValidity: datetime = Field(..., description="ISO 8601 format: YYYY-MM-DDTHH:MM:SSZ")
    endValidity: datetime = Field(..., description="ISO 8601 format: YYYY-MM-DDTHH:MM:SSZ")


class RatesV1PostBulkRequest(BaseModel):
    rates: List[RatesV1PostRequest]


class UsagesV1PutRequest(BaseModel):
    txID: str
    resourceID: str
    consumerID: str
    partner: str
    quantity: confloat(gt=0)
    timestamp: datetime = Field(..., description="ISO 8601 format: YYYY-MM-DDTHH:MM:SSZ")


class UsagesV1PutBulkRequest(BaseModel):
    usages: List[UsagesV1PutRequest]
