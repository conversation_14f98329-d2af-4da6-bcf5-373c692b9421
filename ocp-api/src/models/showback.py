from pydantic import BaseModel, Field, HttpUrl, constr, confloat
from datetime import datetime
from typing import List, Optional, Literal

class ErrorResponse(BaseModel):
    title: constr(min_length=4, max_length=64)
    detail: constr(min_length=4, max_length=512)
    source: constr(min_length=4, max_length=64)
    code: constr(min_length=4, max_length=128)
    id: constr(min_length=27, max_length=27)
    idType: Literal["REQUEST_ID", "RESOURCE_ID", "RATE_ID", "TX_ID"]
    reference: Optional[HttpUrl]


class ResourceIDDatum(BaseModel):
    resourceID: constr(min_length=27, max_length=27)


class RateIDDatum(BaseModel):
    rateID: constr(min_length=27, max_length=27)


class ResourcesV1PostRequest(BaseModel):
    unit: constr(min_length=2, max_length=64, regex="^[a-z]([a-z0-9]_?)*[a-z0-9]$")
    providerID: constr(min_length=4, max_length=8, regex="^[0-9]+$")


class ResourcesV1PostBulkRequest(BaseModel):
    resources: List[ResourcesV1PostRequest]


class ResourcesV1PatchRequest(BaseModel):
    resourceID: constr(min_length=27, max_length=27)
    unit: constr(min_length=2, max_length=64, regex="^[a-z]([a-z0-9]_?)*[a-z0-9]$")
    providerID: constr(min_length=4, max_length=8, regex="^[0-9]+$")


class ResourcesV1PatchBulkRequest(BaseModel):
    resources: List[ResourcesV1PatchRequest]


class RatesV1PostRequest(BaseModel):
    resourceID: constr(min_length=27, max_length=27)
    partner: constr(min_length=2, max_length=64, regex="^[a-z][a-z0-9_\\-]*[a-z0-9]$")
    consumerID: constr(min_length=4, max_length=32, regex="^([0-9]{4,32})$|^([a-zA-Z0-9]{27})$")
    costPerUnit: confloat(ge=0)
    costCurrencyCode: constr(min_length=3, max_length=3, regex="^[A-Z]+$")
    capexPercent: confloat(ge=0, le=1)
    startValidity: datetime = Field(..., description="ISO 8601 format: YYYY-MM-DDTHH:MM:SSZ")
    endValidity: datetime = Field(..., description="ISO 8601 format: YYYY-MM-DDTHH:MM:SSZ")


class RatesV1PostBulkRequest(BaseModel):
    rates: List[RatesV1PostRequest]


class UsagesV1PutRequest(BaseModel):
    txID: constr(min_length=27, max_length=27)
    resourceID: constr(min_length=27, max_length=27)
    consumerID: constr(min_length=4, max_length=32, regex="^([0-9]{4,32})$|^([a-zA-Z0-9]{27})$")
    partner: constr(min_length=2, max_length=64, regex="^[a-z][a-z0-9_\\-]*[a-z0-9]$")
    quantity: confloat(gt=0)
    timestamp: datetime = Field(..., description="ISO 8601 format: YYYY-MM-DDTHH:MM:SSZ")


class UsagesV1PutBulkRequest(BaseModel):
    usages: List[UsagesV1PutRequest]
