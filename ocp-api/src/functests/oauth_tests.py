from src.auth.oauth_auth import get_oauth_token
from src.clients import melt_api as ma


def test_oauth_token():
    acc_tok = get_oauth_token()
    assert 'access_token' in acc_tok
    assert 'token_type' in acc_tok
    assert 'expires_in' in acc_tok
    assert acc_tok['access_token'] != '' and \
        isinstance(acc_tok['access_token'], str)
    assert acc_tok['token_type'] != '' and \
        isinstance(acc_tok['token_type'], str)
    assert acc_tok['expires_in'] != '' and \
        isinstance(acc_tok['expires_in'], int)


def test_oauth_api_calls():
    auth_header = ma.create_header()
    assert auth_header['Authorization'] != '' and \
        isinstance(auth_header['Authorization'], str)
    instance_by_user = ma.get_instances_by_user('gchand339')
    assert isinstance(instance_by_user, dict)
    instance_by_tenant = \
        ma.get_instances_by_tenant('63fcf0bd28940a8d10d37115')
    assert isinstance(instance_by_tenant, dict)
    instnce_to_ten = \
        ma.import_instances_to_tenant('63fcf0bd28940a8d10d37115',
                                      'e11e2ade-268a-4ea3-a511-3930b45bff32')
    assert isinstance(instnce_to_ten, dict)

    # TODO ^^^ setup method required to populate db with records for test


if __name__ == "__main__":
    test_oauth_token()
    test_oauth_api_calls()
