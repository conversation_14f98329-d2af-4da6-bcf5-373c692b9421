import configparser
import jwt
import logging
import logging.config
import time
import os
from fastapi import HTTPException, Request
from fastapi.security.http import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from jwt import PyJWKClient

from src.util.retry_adapter import RetryAdapter

logging.config.fileConfig('config/logging.conf', disable_existing_loggers=False)
logger = logging.getLogger(__name__)

config = configparser.ConfigParser()
config.read('config/auth.ini')

retry_adapter = RetryAdapter()

env = os.environ.get('ENV')
env = os.environ.get('PLATFORMENV', 'local')
if env == 'local' or env == 'docker':
    ocp_proxy = None
else:
    ocp_proxy = os.environ.get('API_PROXY')
proxies = {'https': ocp_proxy} if ocp_proxy else {}

# iss = os.environ.get('OAUTH_ISS').strip()
# iss_url = os.environ.get('OAUTH_ISS_URL')
# jwks_url = os.environ.get('OAUTH_JWKS_URL')
jwks_endpoint = config.get('endpoints', 'jwks')
oauth_jwks = config.get('oauth', 'jwks-prod')
client_aud = os.environ.get('oidc_client_audience')
client_tid = os.environ.get('oidc_client_tid')
admin_scope = "ocp:admin"


class HeaderAuth(HTTPBearer):
    def __init__(cls, auto_error: bool = False, scope="ocp:admin"):
        super(HeaderAuth, cls).__init__(auto_error=auto_error)
        cls.scope = scope

    async def __call__(cls, request: Request):
        logger.debug(f"URI: {request.scope['path']} -- scope-assigned: {cls.scope}")
        credentials: HTTPAuthorizationCredentials = await super(HeaderAuth, cls).__call__(request)
        if credentials:
            if not credentials.scheme == "Bearer":
                raise HTTPException(status_code=401,
                                    detail="Invalid authentication scheme")
            if not cls.validate_bearer_token(credentials.credentials, scope=cls.scope):
                raise HTTPException(status_code=401, detail="Invalid token")

            return credentials.credentials

        if 'id_token' in request.cookies.keys():
            valid, payload = cls.validate_token(request.cookies['id_token'],
                                                return_payload=True)
            if valid:
                return True

        raise HTTPException(status_code=401, detail="Unauthorized")

    def validate_bearer_token(cls, token, scope=admin_scope):
        if 'Bearer ' in token:
            token = token[7:]
        if ocp_proxy:      # Set proxy for urllib to use in PyJWKClient requests
            os.environ['http_proxy'] = ocp_proxy
            os.environ['https_proxy'] = ocp_proxy
        else:              # Clear any previously set proxies if running locally
            os.environ.pop('http_proxy', None)
            os.environ.pop('https_proxy', None)
        try:
            jwks_client = PyJWKClient(oauth_jwks)
            # Get the signing key based on the JWT's kid (key ID) header
            signing_key = jwks_client.get_signing_key_from_jwt(token)
            # Decode and validate the JWT
            payload = jwt.decode(token, signing_key.key, algorithms=["RS256"],
                                 options={"verify_signature": True})

            logger.debug(f"payload: {payload}")
            logger.info(f"call-scope: {scope}, admin-scope: {admin_scope}, "
                        f"payload-scope: {payload['capabilities']}")

            now = int(time.time())
            if cls.scope not in payload['capabilities'] and \
                    admin_scope not in payload['capabilities']:
                logger.exception("Invalid scope")
                raise HTTPException(status_code=403, detail="Forbidden")
            if payload['exp'] < now:
                logger.exception("Token expired")
                return False
            # SAT tokens have not returned consistent values for 'iss', so
            # we assume this claim must be valid bc the JWKS endpoint provided
            # the correct signing key to decode the token
            # if payload['iss'] != iss:
            #     logger.exception("Invalid issuer: payload: {payload['iss']")
            #     return False
        except Exception as e:
            if isinstance(e, HTTPException):
                raise e
            logger.exception(f"Unable to validate token: {e=}")
            return False
        finally:
            if ocp_proxy:  # Remove proxy settings immediately after the call
                os.environ.pop('http_proxy', None)
                os.environ.pop('https_proxy', None)
        return True

    def validate_token(cls, token, aud=client_aud, return_payload=False):
        if ocp_proxy:      # Set proxy for urllib to use in PyJWKClient requests
            os.environ['http_proxy'] = ocp_proxy
            os.environ['https_proxy'] = ocp_proxy
        else:              # Clear any previously set proxies if running locally
            os.environ.pop('http_proxy', None)
            os.environ.pop('https_proxy', None)
        try:
            jwk_client = PyJWKClient(jwks_endpoint)
            signing_key = jwk_client.get_signing_key_from_jwt(token)
            payload = jwt.decode(token, signing_key.key, algorithms=["RS256"], audience=aud)
            now = int(time.time())

            if payload['iat'] > now or payload['nbf'] > now:
                logger.exception("Invalid token")
                return False, {} if return_payload else False
            if payload['exp'] < now:
                logger.exception("Token expired")
                return False, {} if return_payload else False
            if payload['tid'] != client_tid:
                logger.exception("Token tid claim invalid")
                return False, {} if return_payload else False
        except Exception as e:
            logger.exception(f"Unable to validate token: {e=}")
            if return_payload:
                return False, {}
            return False
        finally:
            if ocp_proxy:  # Remove proxy settings immediately after the call
                os.environ.pop('http_proxy', None)
                os.environ.pop('https_proxy', None)
        if return_payload:
            return True, payload
        return True
