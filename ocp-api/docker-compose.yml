services:
  ocpapi:
    profiles: ["ocpapi", ""]
    build: .
    image: ocpapi:latest
    #image: hub.comcast.net/devx-run-ocp/api/ocpapi:1.7.1
    env_file:
      - .env
    environment:
      PLATFORMENV: docker
    ports:
      - 7443:7443
    depends_on:
      - ocpmongo
    entrypoint:
      - /app/docker-init.sh
    volumes:
      - ./src:/app/src
      - ./config:/app/config
    secrets:
      - ssl_cert
      - ssl_key
      - ssl_certkey_file
      - ssl_ca_file
  ocpmongo:
    profiles: ["ocpapi", ""]
    image: hub.comcast.net/devx-run-ocp/db/o11y-mongo:7.0.15-1-local
    #image: o11y-mongo:latest
    env_file:
      - .env
    environment:
      PLATFORMENV: docker
    ports:
      - 27018:27017
    entrypoint:
      - /app/docker-init.sh
    volumes:
      - ocp_mongo_data:/data/db
    secrets:
      - ssl_certkey_file
      - ssl_ca_file
    depends_on:
      init-ocpmongo:
        condition: service_completed_successfully
  init-ocpmongo:
    profiles: ["ocpapi", ""]
    image: hub.comcast.net/devx-run-ocp/db/o11y-mongo:7.0.15-1-local
    #image: o11y-mongo:latest
    ports:
      - 27018:27017
    env_file:
      - .env
    entrypoint:
      - /app/docker-init-setup.sh
    volumes:
      - ocp_mongo_data:/data/db

  vector:
    profiles: ["ocpapi", ""]
    image: timberio/vector:0.36.1-alpine
    ports:
      - 8010:8010
    env_file:
      - .env
    entrypoint:
      - /usr/local/bin/vector
      - -c
      - /etc/vector/config.d/*.toml
    volumes:
      - ./config/vector-aggregator.toml:/etc/vector/config.d/vector-aggregator.toml
      - vector_data:/etc/vector/

#  metricsapi:
#    profiles: ["metricspillar", "pillars", ""]
#    image: hub.comcast.net/o11y_control_plane/metrics_api/cp-virtual-pillar-api:v1.34.0
#    env_file:
#      - .env
#    ports:
#      - 443:8443
#    depends_on:
#      - mongo
#
#  mongo:
#    profiles: ["metricspillar", "pillars", ""]
#    #image: mongo:4.4.25
#    image: mongo:4.4.1
#    ports:
#      - 27017:27017
#    env_file:
#      - .env
#    volumes:
#      - ./setup/init_mongo.sh:/docker-entrypoint-initdb.d/init-mongo.sh
#      - mongo_data:/data/db
#    depends_on:
#      init-ocpmongo:
#        condition: service_completed_successfully
#      #delay start until init-ocpmongo is complete

#  loggingapi:
#    image:
#    env_file:
#      - .env
#    ports:
#      - 8443:80
#    depends:
#      - loggingmongo
#
#  loggingmongo:
#    image: mongo:4.4.1
#    ports:
#      - 27019:27018
#    depends_on:
#      init-ocpmongo:
#        condition: service_completed_successfully
#    volumes:
#      - ./setup/init_mongo.sh:/docker-entrypoint-initdb.d/init-mongo.sh
#      - loggingmongo_data:/data/db


secrets:
  ssl_cert:
    file: certs/localhost/localhost.cert
  ssl_key:
    file: certs/localhost/localhost.key
  ssl_certkey_file:
    file: certs/localhost/localhost-certkey.pem
  ssl_ca_file:
    file: certs/localhost/rootCA.pem

volumes:
  ocp_mongo_data:
  vector_data:
#  mongo_data:
#  loggingmongo_data: