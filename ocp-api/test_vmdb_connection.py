#!/usr/bin/env python3
"""
Test script to validate VMDB connection using environment variables from .env file.
Run this locally before deploying to ensure VMDB integration works correctly.

Usage:
    python test_vmdb_connection.py
"""

import os
import sys
import logging

# Note: Environment variables should be loaded from .env file automatically
# by your development environment or you can run: export $(cat .env | xargs)

# Add src to path
sys.path.extend("./src")

# Configure simple logging for testing (avoid remote logging errors)
logging.basicConfig(
    level=logging.WARNING,  # Reduce log level to avoid remote logging
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]  # Only console output
)

def test_vmdb_integration():
    """Test VMDB client initialization and connection"""

    print("🔧 Testing VMDB Integration for Showback Metrics")
    print("=" * 60)

    # Check environment variables
    print("\n1. Checking Environment Variables:")
    required_vars = ["OCP_METRICS_INSTANCE_ID", "OCP_METRICS_RW_USER", "OCP_METRICS_RW_PASS"]

    for var in required_vars:
        value = os.environ.get(var)
        if value:
            # Mask sensitive values
            if "PASS" in var:
                display_value = "*" * len(value)
            elif "USER" in var:
                display_value = value[:4] + "*" * (len(value) - 4)
            else:
                display_value = value
            print(f"   ✅ {var}: {display_value}")
        else:
            print(f"   ❌ {var}: NOT SET")
            return False

    # Test VMDB client initialization
    print("\n2. Testing VMDB Client Initialization:")
    try:
        from src.clients.vmdb import VMClient
        vmdb_client = VMClient()
        print("   ✅ VMDB client initialized successfully")
    except Exception as e:
        print(f"   ❌ VMDB client initialization failed: {e}")
        return False

    # Test connection
    print("\n3. Testing VMDB Connection:")
    try:
        success = vmdb_client.test_connection()
        if success:
            print("   ✅ VMDB connection test successful")
        else:
            print("   ❌ VMDB connection test failed")
            return False
    except Exception as e:
        print(f"   ❌ VMDB connection test error: {e}")
        return False

    # Test showback metrics simulation
    print("\n4. Testing Showback Metrics Simulation:")
    try:
        import time
        timestamp = int(time.time())

        # Simulate the three metrics that showback will send
        test_metrics = [
            {
                "metric_name": "o11y-showback_run_state",
                "labels": {
                    "__name__": "o11y-showback_run_state",
                    "status": "success",
                    "env": "test",
                    "platform": "local"
                },
                "value": 1.0
            },
            {
                "metric_name": "o11y-showback_pillar_count",
                "labels": {
                    "__name__": "o11y-showback_pillar_count",
                    "env": "test",
                    "platform": "local"
                },
                "value": 1.0
            },
            {
                "metric_name": "o11y-showback_pillar_devhub-id_count",
                "labels": {
                    "__name__": "o11y-showback_pillar_devhub-id_count",
                    "pillar": "metrics",
                    "env": "test",
                    "platform": "local"
                },
                "value": 5.0
            }
        ]

        for metric in test_metrics:
            vmdb_client.remote_write_internal_gauge(
                metric_name=metric["metric_name"],
                labels=metric["labels"],
                value=metric["value"],
                timestamp=timestamp
            )
            print(f"   ✅ Sent test metric: {metric['metric_name']}")

        print("   ✅ All showback metrics sent successfully")

    except Exception as e:
        print(f"   ❌ Showback metrics test failed: {e}")
        return False

    print("\n" + "=" * 60)
    print("🎉 VMDB Integration Test PASSED!")
    print("✅ Your .env file is configured correctly")
    print("✅ VMDB client can connect and send metrics")
    print("✅ Ready for showback cron deployment")
    print("\nNext steps:")
    print("1. Deploy showback cron with VMDB environment variables")
    print("2. Monitor Victoria Database for o11y-showback_* metrics")
    print("3. Set up alerts/dashboards based on these metrics")

    return True

if __name__ == "__main__":
    success = test_vmdb_integration()
    sys.exit(0 if success else 1)
