# Showback API Configuration
# Set this to the appropriate Showback API endpoint for your environment
# Production: https://gw-ce-data.api.dh.comcast.com/showback
# Stage: https://gw-ce-data.api.dh.comcast.com/showback/stage
SHOWBACK_API_URL=https://gw-ce-data.api.dh.comcast.com/showback

# VMDB Configuration (required for metrics reporting)
OCP_METRICS_INSTANCE_ID=415fbe7a-6c01-4e11-97d6-a6cd5f967ec4
OCP_METRICS_RW_USER=your-vmdb-username
OCP_METRICS_RW_PASS=your-vmdb-password

# Environment Configuration
DEPLOYENV=local
ENV=local
PLATFORMENV=local

# MongoDB Configuration (for local testing)
MONGO_HOST=127.0.0.1
MONGO_PORT=27018
MONGO_APP_DATABASE=ocp
MONGO_APP_USER=your-mongo-user
MONGO_APP_PASS=your-mongo-password

# Vault Configuration (if needed)
CP_VAULT_PASS=your-vault-password
VAULT_ROLE_ID=your-vault-role-id
VAULT_SECRET_ID=your-vault-secret-id
