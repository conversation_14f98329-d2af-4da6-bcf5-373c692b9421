log_schema.host_key = "host.hostname"
log_schema.timestamp_key = "@timestamp"


#    . = parse_json!(.message)

[transforms.remap]
  type = "remap"
  inputs = ["http"]
  source = '''

    .host.hostname = get_hostname!()
    .tags.environment = get_env_var!("CLOUD_PROVIDER")
    .tags.region = get_env_var!("CLOUD_REGION")
    .tags.namespace = get_env_var!("CLOUD_NAMESPACE")

  '''

[sources.http]
  type = "http_server"
  address = "0.0.0.0:8010"
  decoding.codec = "json"

#[sinks.out]
#  type = "console"
#  inputs = ["remap"]
#  target = "stdout"
#  encoding.codec = "json"

[sinks.es]
  type = "elasticsearch"
  inputs = ["remap"]
  auth.strategy = "basic"
  auth.user = "$ELK_USERNAME"
  auth.password = "$ELK_PASSWORD"
  endpoints = ["$ELK_URL"]
  bulk.index = "logz-ocp-api"
  bulk.action = "create"
