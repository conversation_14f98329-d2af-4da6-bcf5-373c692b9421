[loggers]
keys=root

[logger_root]
level=INFO
handlers=console, vector 

[handlers]
keys=console, vector

# TODO - filters not supported in fileConfig, convert to dictConfig
#[filters]
#keys=ocp, favico

[formatters]
keys=simple, json

[handler_console]
class=StreamHandler
level=DEBUG
formatter=simple
args=(sys.stdout,)

[handler_vector]
class=src.library.logging.handlers.JSONHTTPHandler
args=('vector:8010', '/', 'POST')
#filters=[favico]
formatter=json

#[filter_ocp]
#class=src.lib.logging.filters.EndpointFilter_favicon
#
#[filter_favico]
#class=src.lib.logging.filters.EndPointFilter_a

[formatter_simple]
format=%(asctime)s - %(name)s - %(threadName)s - %(levelname)s - %(funcName)s - %(filename)s ln:%(lineno)d - %(message)s

[formatter_json]
class = pythonjsonlogger.jsonlogger.JsonFormatter
format=%(asctime)s - %(name)s - %(threadName)s - %(levelname)s - %(funcName)s - %(filename)s ln:%(lineno)d - %(message)s

