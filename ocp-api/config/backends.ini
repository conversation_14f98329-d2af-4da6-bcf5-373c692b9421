[pillars]
METRICS-API-PROD = https://metrics-api-p.monitoring.comcast.net:8443
#METRICS-API-LOCAL = https://localhost
#METRICS-API-DOCKER = https://metricsapi:8443
METRICS-API-LOCAL = https://metrics-as-rdei-lb-01p.monitoring.comcast.net:8001
METRICS-API-DOCKER = https://metrics-as-rdei-lb-01p.monitoring.comcast.net:8001
METRICS-API-VERSION = 2
#METRICS-API-DOCKER = https://metrics-ho-rdei-lb-01p.monitoring.comcast.net:8001

LOGGING-API-PROD = https://logging-ocp.eaas.comcast.net
#LOGGING-API-PROD = https://logging-ocp-stg.eaas.comcast.net
LOGGING-API-LOCAL = https://logging-ocp-int.eaas.comcast.net
#LOGGING-API-LOCAL = http://host.docker.internal:8000
LOGGING-API-DOCKER = https://logging-ocp-int.eaas.comcast.net
LOGGING-API-VERSION = 2
#LOGGING-API-DOCKER = https://host.docker.internal:8443

VIZ-API-PROD = https://grafana-api-p.monitoring.comcast.net:8443
VIZ-API-LOCAL = https://grafana-api-p.monitoring.comcast.net:8443
VIZ-API-DOCKER = https://grafana-api-p.monitoring.comcast.net:8443
VIZ-API-VERSION = 1

[git]
URL = https://githubcloud.comcast.com
API = https://api.githubcloud.comcast.com
TENANT_ORG = comcast-observability-tenants

[vault]
VAULT_ADDR = https://or.vault.comcast.com
VAULT_PATH = comcast-devx-run-ocp/dev
VAULT_PATH_PROD = comcast-devx-run-ocp/prod

[devhub]
DEVHUB_API = https://fusion.comcast.net/graphql
DEVHUB_API-STAGE = https://fusion.comcast.net/graphql
#DEVHUB_API-STAGE = https://fusion-dev.discover.comcast.net/graphql

[showback]
SHOWBACK_API = https://gw-ce-data.api.dh.comcast.com/showback/stage

[retry]
MAX_RETRIES = 6
BACKOFF_FACTOR = 0.015
