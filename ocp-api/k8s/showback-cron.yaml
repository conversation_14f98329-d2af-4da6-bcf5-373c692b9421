apiVersion: batch/v1
kind: CronJob
metadata:
  name: showback-processor
  namespace: ocp-prod
  labels:
    app: showback-processor
    component: cron
spec:
  # Run daily at 23:00 UTC
  schedule: "0 23 * * *"
  timeZone: "UTC"
  concurrencyPolicy: Forbid
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: showback-processor
            component: cron
        spec:
          restartPolicy: OnFailure
          containers:
          - name: showback-processor
            image: hub.comcast.net/devx-run-ocp/api/ocpapi:latest
            command:
            - python
            - src/scripts/showback_cron.py
            env:
            # Showback API Configuration
            - name: SHOWBACK_API_URL
              valueFrom:
                secretKeyRef:
                  name: showback-api-url
                  key: SHOWBACK_API_URL
            
            # VMDB Configuration
            - name: OCP_METRICS_INSTANCE_ID
              valueFrom:
                secretKeyRef:
                  name: ocp-metrics-instance-id
                  key: OCP_METRICS_INSTANCE_ID
            - name: O<PERSON>_METRICS_RW_USER
              valueFrom:
                secretKeyRef:
                  name: ocp-metrics-rw-user
                  key: OCP_METRICS_RW_USER
            - name: OCP_METRICS_RW_PASS
              valueFrom:
                secretKeyRef:
                  name: ocp-metrics-rw-pass
                  key: OCP_METRICS_RW_PASS
            
            # Environment Configuration
            - name: DEPLOYENV
              value: "prod"
            - name: ENV
              value: "rdei"
            - name: PLATFORMENV
              value: "rdei"
            
            # MongoDB Configuration
            - name: MONGO_HOST
              value: "ocp-mongo-service"
            - name: MONGO_PORT
              value: "27017"
            - name: MONGO_APP_DATABASE
              valueFrom:
                secretKeyRef:
                  name: mongo-app-database
                  key: MONGO_APP_DATABASE
            - name: MONGO_APP_USER
              valueFrom:
                secretKeyRef:
                  name: mongo-app-user
                  key: MONGO_APP_USER
            - name: MONGO_APP_PASS
              valueFrom:
                secretKeyRef:
                  name: mongo-app-pass
                  key: MONGO_APP_PASS
            
            # Vault Configuration
            - name: CP_VAULT_PASS
              valueFrom:
                secretKeyRef:
                  name: cp-vault-pass
                  key: CP_VAULT_PASS
            - name: VAULT_ROLE_ID
              valueFrom:
                secretKeyRef:
                  name: vault-role-id
                  key: VAULT_ROLE_ID
            - name: VAULT_SECRET_ID
              valueFrom:
                secretKeyRef:
                  name: vault-secret-id
                  key: VAULT_SECRET_ID
            
            resources:
              requests:
                memory: "512Mi"
                cpu: "250m"
              limits:
                memory: "1Gi"
                cpu: "500m"
            
            volumeMounts:
            - name: config-volume
              mountPath: /app/config
              readOnly: true
            - name: cache-volume
              mountPath: /app/showback_cache
          
          volumes:
          - name: config-volume
            configMap:
              name: ocp-api-config
          - name: cache-volume
            emptyDir: {}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ocp-api-config
  namespace: ocp-prod
data:
  backends.ini: |
    [git]
    URL = https://githubcloud.comcast.com
    API = https://api.githubcloud.comcast.com
    TENANT_ORG = comcast-observability-tenants

    [vault]
    VAULT_ADDR = https://or.vault.comcast.com
    VAULT_PATH = comcast-devx-run-ocp/dev
    VAULT_PATH_PROD = comcast-devx-run-ocp/prod

    [devhub]
    DEVHUB_API = https://fusion.comcast.net/graphql
    DEVHUB_API-STAGE = https://fusion.comcast.net/graphql

    [showback]
    # This will be overridden by SHOWBACK_API_URL environment variable
    SHOWBACK_API = https://gw-ce-data.api.dh.comcast.com/showback

    [retry]
    MAX_RETRIES = 6
    BACKOFF_FACTOR = 0.015
