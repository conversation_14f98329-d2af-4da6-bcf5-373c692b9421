FROM python:3.9.16-alpine3.17
WORKDIR /app
COPY . .
COPY docker-init.sh /docker-init.sh
RUN chmod u+x /docker-init.sh

RUN apk update
RUN apk add musl-dev
RUN apk add gcc
RUN apk add libffi-dev
RUN apk add bash
#RUN apk add ansible
RUN apk add py-pip
RUN apk add msmtp
RUN apk add jq

RUN apk add --update --virtual .deps --no-cache gnupg && \
    cd /tmp && \
    wget https://releases.hashicorp.com/vault/1.18.2/vault_1.18.2_linux_amd64.zip && \
    wget https://releases.hashicorp.com/vault/1.18.2/vault_1.18.2_SHA256SUMS && \
    wget https://releases.hashicorp.com/vault/1.18.2/vault_1.18.2_SHA256SUMS.sig && \
    wget -qO- https://www.hashicorp.com/.well-known/pgp-key.txt | gpg --import && \
    gpg --verify vault_1.18.2_SHA256SUMS.sig vault_1.18.2_SHA256SUMS && \
    grep vault_1.18.2_linux_amd64.zip vault_1.18.2_SHA256SUMS | sha256sum -c && \
    unzip /tmp/vault_1.18.2_linux_amd64.zip -d /tmp && \
    mv /tmp/vault /usr/local/bin/vault && \
    rm -f /tmp/vault_1.18.2_linux_amd64.zip vault_1.18.2_SHA256SUMS 1.18.2/vault_1.18.2_SHA256SUMS.sig && \
    apk del .deps


RUN pip install -r /app/requirements.txt

EXPOSE 7443
