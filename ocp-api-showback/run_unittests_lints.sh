#!/bin/bash

echo "Pulling secrets..."
eval `./get_secrets.sh`

if [[ $PLATFORMENV == 'local' ]]; then
  echo;echo "Starting vector..."
  if [[ "$OSTYPE" == "darwin22"* ]]; then
    sed -i '' 's/vector:8010/localhost:8010/g' config/logging.conf
    docker-compose up vector > /dev/null &
  else
    sed -i 's/vector:8010/localhost:8010/g' config/logging.conf
    docker-compose up vector > /dev/null &
  fi
  sleep 6
  echo;echo
fi

echo;echo "Running OCPAPI Unit Tests...";echo
#poetry run python -W ignore:ResourceWarning -m unittest src/unittests/*tests.py
echo "______________________________________________________";echo
echo "Running Mongo Client Tests..."
poetry run python -W ignore:ResourceWarning -m unittest src/unittests/client_mongo_tests.py
echo "______________________________________________________";echo
echo;echo "Running Instance Routes Tests..."
poetry run python -W ignore:ResourceWarning -m unittest src/unittests/instance_tests.py
echo "______________________________________________________";echo
echo;echo "Running Tenant Routes Tests..."
poetry run python -W ignore:ResourceWarning -m unittest src/unittests/tenant_tests.py
echo "______________________________________________________";echo
echo;echo "Running Auth Functions Tests..."
poetry run python -W ignore:ResourceWarning -W ignore:RuntimeWarning -m unittest src/unittests/auth_tests.py

echo;echo;echo

if [[ $PLATFORMENV == 'local' ]]; then
  echo;echo;echo "Stopping Vector...";echo;echo
  if [[ "$OSTYPE" == "darwin22"* ]]; then
    sed -i '' 's/localhost:8010/vector:8010/g' config/logging.conf
    docker-compose down &
  else
    sed -i 's/localhost:8010/vector:8010/g' config/logging.conf
    docker-compose down &
  fi
  sleep 10
  echo;echo;echo
fi

echo;echo;echo "Running flake8 lint (PyFlakes / PEP8) against source..."
poetry run flake8 src/ --ignore=W901,W503 --max-line-length=100 --count  --show-source --statistics
echo

