#!/bin/bash

set -e

echo "=> Creating a new database and user"
mongo ${MONGO_INITDB_DATABASE} -u ${MONGO_INITDB_ROOT_USERNAME} -p ${MONGO_INITDB_ROOT_PASSWORD} --authenticationDatabase admin <<EOF
  db.createUser({
    user: "${MONGO_APP_USERNAME}",
    pwd: "${MONGO_APP_PASSWORD}",
    roles: [ { role: "readWrite", db: "${MONGO_INITDB_DATABASE}" } ]
  });
EOF

#echo "=> Creating a new CP database and user"
#mongo ${CP_MONGO_DB} -u ${MONGO_INITDB_ROOT_USERNAME} -p ${MONGO_INITDB_ROOT_PASSWORD} --authenticationDatabase admin <<EOF
#  db.createUser({
#    user: "${CP_MONGO_USER}",
#    pwd: "${CP_MONGO_PASS}",
#    roles: [ { role: "readWrite", db: "${CP_MONGO_DB}" } ]
#  });
#EOF

#  use ${CP_MONGO_INITDB_DATABASE};
