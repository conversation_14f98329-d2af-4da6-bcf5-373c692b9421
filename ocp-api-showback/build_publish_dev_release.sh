#!/bin/bash
set -e

# Ask to bump version ...
#python ver-bump-release.py -- TODO - fix this script
#echo "Bumping release [$(cat version)]"

echo "Building image..."
docker buildx build -t ocpapi -o type=docker --platform=linux/amd64 .
echo "Tagging image 'hub.comcast.net/devx-run-ocp/api/ocpapi:$(cat version)'"
docker tag ocpapi:latest hub.comcast.net/devx-run-ocp/api/ocpapi:$(cat version)
echo "Publishing image 'hub.comcast.net/devx-run-ocp/api/ocpapi:$(cat version)'"
docker push hub.comcast.net/devx-run-ocp/api/ocpapi:$(cat version)
