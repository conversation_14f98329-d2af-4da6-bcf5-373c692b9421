import subprocess
import sys

flkf = open("report-flake8.txt", "w")
subprocess.call("flake8 src/ --ignore=W901,W503 --max-line-length=100", shell=True, stdout=flkf)
flkf.close()

fout = open("report-flake8.txt", "r")
lines = []
for line in fout:
    lines.append(line)
fout.close()

f_score = len(lines)
print(f"Flake8 identified {f_score} errors/violations ")
f_line_threshold = 0

if f_score > f_line_threshold:
    print(f"Flake8 test failed, more than {f_line_threshold}"
          + " lint errors/violations identified:\n")
    for line in lines:
        print(line, end='')
    print()
    sys.exit(1)
sys.exit(0)
