import configparser
import json
import logging
import logging.config
import os
# import typing

from src.auth.oauth import get_metrics_oauth_token
from src.auth.oauth import get_logging_oauth_token, get_viz_oauth_token
# from src.auth.oauth import get_logging_oauth_token  # TODO
from src.clients.retry_adapter import RetryAdapter

env = os.environ.get('PLATFORMENV', 'local')

config = configparser.ConfigParser()
config.read('config/backends.ini')

retry_adapter = RetryAdapter()

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

pillars = ['metrics', 'logging', 'visualization']

pillar_eps = {
    'metrics': {
        'version': config.get('pillars', 'METRICS-API-VERSION'),
        'local': config.get('pillars', 'METRICS-API-LOCAL'),
        'docker': config.get('pillars', 'METRICS-API-DOCKER'),
        'rdei': os.environ.get('METRICS_API',
                               config.get('pillars', 'METRICS-API-PROD'))
    },
    'logging': {
        'version': config.get('pillars', 'LOGGING-API-VERSION'),
        'local': config.get('pillars', 'LOGGING-API-LOCAL'),
        'docker': config.get('pillars', 'LOGGING-API-DOCKER'),
        'rdei': os.environ.get('LOGGING_API',
                               config.get('pillars', 'LOGGING-API-PROD'))
    },
    'visualization': {
        'version': config.get('pillars', 'VIZ-API-VERSION'),
        'local': config.get('pillars', 'VIZ-API-LOCAL'),
        'docker': config.get('pillars', 'VIZ-API-DOCKER'),
        'rdei': os.environ.get('VIZ',
                               config.get('pillars', 'VIZ-API-PROD'))
    }
}

# if env == 'rdei':
#     metrics_api = config.get('pillars', 'METRICS-API')
# if env == 'local':
#     metrics_api = config.get('pillars', 'METRICS-API-LOCAL')
#     logging_api = config.get('pillars', 'LOGGING-API-LOCAL')
# if env == 'docker':
#     metrics_api = config.get('pillars', 'METRICS-API-DOCKER')
#     logging_api = config.get('pillars', 'LOGGING-API-DOCKER')
# metrics_api = config.get('pillars', 'METRICS-API')


def create_auth_header(pillar):
    access_token = None
    if pillar == 'metrics':
        access_token = get_metrics_oauth_token()['access_token']
    if pillar == 'logging':
        # access_token = get_logging_oauth_token()['access_token']
        access_token = get_logging_oauth_token()['access_token']
        # TODO - require client configured for logging OAuth service
    if pillar == 'visualization':
        access_token = get_viz_oauth_token()['access_token']

    headers = {'Authorization': 'Bearer ' + access_token}
    return headers
    # TODO ^^^ cache token on disk (memcachd?) for expiry time


# This API method is defunct
# def get_instances_by_user(nt_id: str, pillar: str):
#     headers = create_auth_header()
#     instances_resp = None
#     if pillar == 'metrics':
#         instances_resp = requests.get(metrics_api +
#                                       f"/api/v1/instances/by_user/{nt_id}",
#                                       headers=headers, verify=False)
#     if instances_resp.status_code != 200:
#         msg = f"{instances_resp.status_code} Error retrieving Instances"\
#             + f" from {pillar.upper()} API: "\
#             + f" {instances_resp.text}"
#         logger.error(msg)
#         raise Exception({"message": msg})
#     return instances_resp.json()

def execute_request(method: str,
                    pillar: str,
                    endpoint: str,
                    headers=None,
                    timeout=10,
                    verify=False,
                    json=None,
                    params=None,
                    data=None):
    """
    Execute an HTTP request with retry logic and error handling.

    :param method: HTTP method (GET, POST, PUT, DELETE, etc.)
    :param pillar: Pillar name to retrieve the base URL and version
    :param endpoint: Endpoint path with placeholders (e.g.,
        "/instances/tenant/id/{tenant_id}")
    :param headers: Headers to include in the request
    :param timeout: Timeout for the request
    :param verify: Whether to verify SSL certificates
    :param json: JSON body for the request (used with POST, PUT, etc.)
    :param params: URL parameters for the request (used with GET, DELETE, etc.)
    :param data: Data payload for the
        request (alternative to JSON)
    :return: A dictionary with keys 'error'
        (bool) and 'msg' (response or error message)
    """
    try:
        # Assemble the full URL
        logger.info(f"Executing request for pillar {pillar}, endpoint: "
                    f"{endpoint}")
        api_base_url = pillar_eps[pillar][env]
        api_version = "/api/v" + str(pillar_eps[pillar]['version'])
        full_url = api_base_url + api_version + endpoint

        logger.info(f"Executing {method.upper()} request to {full_url}")

        # Select the appropriate method on the retry_adapter
        response = retry_adapter.request(method.upper(),
                                         full_url,
                                         headers=headers,
                                         timeout=timeout,
                                         verify=verify,
                                         json=json,
                                         params=params,
                                         data=data)
        return response
    except Exception as e:
        err_message = str(e).replace('"', "'")
        logger.error(f"Error executing {method.upper()} request: "
                     f"{err_message}")
        raise e


def get_instances_by_tenant(tenant_id: str):
    pillars = ['metrics', 'logging', 'visualization']
    instances = {'instances': []}
    msgs = []
    found = False
    for pillar in pillars:
        resp = _get_instances_by_tenant(tenant_id, pillar)
        if resp['error']:
            msgs.append(resp['msg'])
            continue
        else:
            found = True
            instances['instances'].extend(resp['msg']['instances'])

    if not found:
        raise Exception({"message": resp['msg']})

    return instances


def _get_instances_by_tenant(tenant_id: str, pillar):
    logger.info("Getting instances for tenant %s from %s API",
                tenant_id, pillar.upper())
    try:
        endpoint = f"/instances/tenant/id/{tenant_id}"
        headers = create_auth_header(pillar)
        instances_resp = execute_request(
            method="GET",
            pillar=pillar,
            endpoint=endpoint,
            headers=headers,
            timeout=10,
            verify=False
        )
        return {"error": False, "msg": instances_resp.json()}
    except Exception as e:
        erm = str(e).replace('"', "'")
        msg = f"Error retrieving Instances from '{pillar=}' API: {erm}"
        return {"error": True, "msg": msg}


def get_instance_by_id(instance_id):
    pillars = ['metrics', 'logging', 'visualization']
    msgs = []
    found = False
    resp = {}
    for pillar in pillars:
        # print(f"\n\nDEBUG - pillar: {pillar} \n\n")
        resp = _get_instance_by_id(instance_id, pillar)
        if resp['error']:
            msgs.append(resp['msg'])
            continue
        else:
            found = True
            break

    if not found:
        raise Exception({"message": msgs})

    return resp['msg']


def _get_instance_by_id(instance_id, pillar):
    logger.info("Getting instance %s from %s API", instance_id, pillar.upper())
    try:
        endpoint = f"/instances/id/{instance_id}"
        headers = create_auth_header(pillar)
        instance_resp = execute_request(
            method="GET",
            pillar=pillar,
            endpoint=endpoint,
            headers=headers,
            timeout=10,
            verify=False
        )
        return {"error": False, "msg": instance_resp.json()}

    except Exception as e:  # noqa
        msg = f"Error retrieving Instance {instance_id} from '{pillar=}' "\
            + "API: {str(e).replace('"', "'")}"
        return {"error": True, "msg": msg}


# def import_instances_to_tenant(
#                           tenant_id: str,
#                           instance_ids: typing.List[str]):
#     headers = create_auth_header()
#     params = {"instances": []}
#     for iid in instance_ids:
#         params["instances"].append({"id": iid})
#
#     add_instances_resp = requests.post(
#         metrics_api + f"/api/v1/instances/import/{tenant_id}",
#         json=params, headers=headers,
#         verify=False)
#     if add_instances_resp.status_code != 200:
#         msg = f"Error adding Instances '{instance_ids}' to Tenant, ID "\
#             + f"'{tenant_id}': {str(e)}"
#         logger.error(f"{add_instances_resp.status_code} - "+msg)
#         raise Exception({"message": msg})
#
#     return add_instances_resp.json()


def get_provision_form(pillar: str):
    logger.info("Getting Provision Form from %s pillar API", pillar.upper())

    headers = create_auth_header(pillar)
    try:
        endpoint = "/instances/provision_form"
        headers = create_auth_header(pillar)
        prov_form_resp = execute_request(
            method="GET",
            pillar=pillar,
            endpoint=endpoint,
            headers=headers,
            timeout=10,
            verify=False
        )
        return json.loads(prov_form_resp.text)
    except Exception as e:  # noqa
        erm = str(e).replace('"', "'")
        msg = "Error retrieving Provision Instance Form from "\
            + f"'{pillar}' API: {erm}"
        logger.error(msg)
        raise RuntimeError(msg) from e


# TODO - rework to use dynamic fields
# TODO - define SLAs on pillar timeout (seconds at most)
def provision_instance(provision_form, pillar):
    logger.info("Provisioning instance in %s API", pillar.upper())
    headers = create_auth_header(pillar)
    try:
        endpoint = "/instances"
        headers = create_auth_header(pillar)
        prov_instance_resp = execute_request(
            method="POST",
            pillar=pillar,
            endpoint=endpoint,
            headers=headers,
            timeout=60,
            verify=False,
            json=provision_form
        )
        return prov_instance_resp.json()
    except Exception as e:
        erm = str(e).replace('"', "'")
        # TODO - pull instance name for error message
        msg = f"Error provisioning Instance: {erm} from '{pillar=}'"
        logger.error(msg)
        raise RuntimeError(msg) from e


def get_instance_details(instance_id, pillar):
    logger.info("Getting details for instance %s from %s API",
                instance_id, pillar.upper())

    headers = create_auth_header(pillar)
    try:
        endpoint = f"/instances/id/{instance_id}/details"
        # TODO - update once logging API provides details endpoint
        if pillar == "logging":
            endpoint = f"/instances/id/{instance_id}"
        headers = create_auth_header(pillar)
        instance_details_resp = execute_request(
            method="GET",
            pillar=pillar,
            endpoint=endpoint,
            headers=headers,
            timeout=10,
            verify=False
        )
        return instance_details_resp.json()
    except Exception as e:
        erm = str(e).replace('"', "'")
        msg = f"Error retrieving Instance {instance_id} from '{pillar=}' "\
            + f"API: {erm}"
        logger.error(msg)
        raise RuntimeError(msg) from e


def get_actions_definitions(pillar):
    p_str = "all Pillar APIs" if pillar is None else f"{pillar.upper()} API"
    logger.info(f"Getting Actions definitions from {p_str}")
    action_defs = {}
    pillars_to_query = [pillar] if pillar is not None else pillars

    for p in pillars_to_query:
        try:
            headers = create_auth_header(p)
            api_url = pillar_eps[p][env]

            actions_def_resp = \
                retry_adapter.get(api_url
                                  + "/api/v2/instances/actions/definitions",
                                  headers=headers, verify=False,
                                  timeout=10)

            action_defs[p] = actions_def_resp.json()
            # TODO - parse first or send in Response object?
        except Exception as e:
            erm = str(e).replace('"', "'")
            msg = f"Error retrieving Action definitions from {pillars_to_query} "\
                + f"APIs: {erm}"
            logger.error(msg)

    return action_defs


def get_actions_interfaces(pillar):
    p_str = "all Pillar API" if pillar is None else f"{pillar.upper()} API"
    logger.info(f"Getting Actions definitions from {p_str}")
    action_ints = {}
    pillars_to_query = [pillar] if pillar is not None else pillars

    for p in pillars_to_query:
        try:
            headers = create_auth_header(p)
            api_url = pillar_eps[p][env]
            actions_ints_resp = \
                retry_adapter.get(api_url
                                  + "/api/v2/instances/actions/interfaces",
                                  headers=headers, verify=False,
                                  timeout=10)
            action_ints[p] = actions_ints_resp.json()
        except Exception as e:
            erm = str(e).replace('"', "'")
            msg = f"Error retrieving Action definitions from {pillars_to_query} "\
                + f"APIs: {erm}"
            logger.error(msg)

    return action_ints


def execute_action(pillar, action_request):
    logger.info(f"Submitting Action request to {pillar.upper()} API for "
                f"Instance {action_request.instance_id}")
    try:
        headers = create_auth_header(pillar)
        api_url = pillar_eps[pillar][env]
        req = action_request.dict()
        # TODO - fix SSL verification

        action_ex_resp = \
            retry_adapter.post(api_url
                               + "/api/v2/instances/actions/execute",
                               json=req, headers=headers, verify=False,
                               timeout=10)

        return action_ex_resp.json()
    except Exception as e:
        erm = str(e).replace('"', "'")
        msg = f"Error executing Action: {erm}"
        logger.error(msg)
        raise RuntimeError(msg) from e


def delete_instances_by_tenant(tenant_id: str):
    # Define pillars to iterate over
    # logging pillar is not implemented yet
    # metrics and visualization force delete works only with instance

    pillars = ['metrics', 'logging', 'visualization']
    msgs = []
    success = True

    # Retrieve all instances for the tenant
    instances = get_instances_by_tenant(tenant_id)

    for pillar in pillars:
        # Adjust for metrics pillar with '-ng' suffix directly in the filter
        pillar_type = f"{pillar}-ng" if pillar == 'metrics' else pillar

        # Filter instances to include only those for the current pillar
        pillar_instances = [
            instance for instance in instances['instances']
            if instance['type'] == pillar_type
        ]

        # Skip pillars without delete functionality implemented
        if pillar in ['logging']:
            logger.debug(f"\n\nSkipping deletion for {pillar} as"
                         f"delete endpoint is not implemented yet.\n\n")
            continue

        # Loop through each instance in the pillar and delete by instance ID
        for instance in pillar_instances:
            instance_id = instance['id']
            resp = _delete_instance_by_id(instance_id, pillar)

            if resp['error']:
                msgs.append(f"{resp['msg']} (Instance ID: {instance_id} "
                            f"in {pillar} pillar)")
                success = False
            else:
                logger.debug(f"Successfully deleted instance {instance_id}")

    # Raise exception if any deletions failed
    if not success:
        raise Exception({"message": "; ".join(msgs)})

    return {"message": f"All instances for tenant "
            f"{tenant_id} deleted successfully."}


def _delete_instance_by_id(instance_id, pillar):
    """
    Internal helper function to delete an instance
    by ID for a specific pillar.
    """
    logger.info("Deleting instance %s from %s API",
                instance_id, pillar.upper())
    try:
        # Construct the delete endpoint URL for the instance
        endpoint = f"/instances/id/{instance_id}/force"
        headers = create_auth_header(pillar)
        delete_resp = execute_request(
            method="DELETE",
            pillar=pillar,
            endpoint=endpoint,
            headers=headers,
            timeout=10,
            verify=False
        )

        # Check if the deletion was successful
        # Assuming 204 No Content indicates success
        if delete_resp.status_code == 204 or delete_resp.status_code == 200:
            return {
                "error": False,
                "msg": (
                    f"Instance {instance_id} deleted successfully "
                    f"in {pillar} pillar")
            }
        else:
            # Handle cases where deletion did not succeed as expected
            msg = f"Failed to delete instance {instance_id} \
                in {pillar} pillar: {delete_resp.text}"
            logger.error(msg)
            return {"error": True, "msg": msg}

    except Exception as e:
        # Handle exceptions during the request execution
        msg = f"""Error deleting instance {instance_id} \
            from '{pillar=}' API: {str(e).replace('"', "'")}"""
        logger.error(msg)
        return {"error": True, "msg": msg}
