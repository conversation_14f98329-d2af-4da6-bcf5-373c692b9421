import logging
import logging.config
import os
import uvicorn

from fastapi import Fast<PERSON><PERSON>  # , Depends
from fastapi.middleware.cors import CORSMiddleware
# from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from prometheus_fastapi_instrumentator import Instrumentator, metrics

import sys
sys.path.extend("./src")

from src.auth.oidc import auth_router  # noqa: E402
from src.handlers.instances import instance_router  # noqa: E402
from src.handlers.tenants import tenant_router  # noqa: E402
from src.handlers.devhub import devhub_router  # noqa: E402
from src.handlers.motd import motd_router  # noqa: E402
from src.handlers.actions import actions_router  # noqa: E402


# from logstash_async.constants import constants
# constants.SOCKET_TIMEOUT = 10.0

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)
logger.info("Starting OCP API server...")

# Test for required secrets for SSO
# TODO - test against all required secrets
client_id = os.environ.get('oidc_client_id')
client_secret = os.environ.get('oidc_client_secret')
client_aud = os.environ.get('oidc_client_audience')
client_tid = os.environ.get('oidc_client_tid')
if not client_id or not client_secret or not client_aud or not client_tid:
    logger.fatal("Failed to set some of these properties: client_id, "
                 "client_secret, client_aud, client_tid")
    raise Exception("Required secrets not available: client_id, "
                    "client_secret, client_aud, client_tid")

desc = \
    "The O11Y Control Plane is a resource management tool allowing users " \
    + "to interact with resources across all Pillars (Metrics, Logging, " \
    + "Tracing, etc) from a central location.  Ownership of resources " \
    + "is managed via AD Groups, in order to provision access for teams " \
    + "or individuals, an AD Group must first exist or be created, then " \
    + "assigned to a Tenant, which may own one or more Instances from any " \
    + "number of Pillars." \
    + "\n\n" \
    + "All 'instances' and 'tenants' methods require authentication.  Load" \
    + " /api/v1/auth/login in browser to complete SSO login to authenticate"\
    + " session.  (OAuth implementation TBD)"


tags_md = [
    # {"name": "login",
    #  "description": "Methods to faciliate SSO"
    #  },
    {"name": "auth",
     "description": "Authentication operations"
     },
    {"name": "instances",
     "description": "Instances management operations"
     },
    {"name": "tenants",
     "description": "Tenants management operations"
     },
    {"name": "motd",
     "description": "MOTD management operations, create/update/delete require"
     " admin permissions"
     },
    {"name": "devhub",
     "description": "DevHub search operations"
     },
    {"name": "actions",
     "description": "Actions operations.  Beta version, admin permission "
     "required"
     }]

v = open('version', 'r')
version = v.read().strip()
v.close()

fastapi = FastAPI(title="Observability Control Plane API",
                  description=desc,
                  version=version,
                  contact={"name": "MELT",
                           "email": "<EMAIL>"
                           },
                  openapi_tags=tags_md,
                  swagger_ui_parameters={"defaultModelsExpandDepth": -1,
                                         "syntaxHighlight.theme": "arta",
                                         "validatorUrl": "null"})

fastapi.include_router(auth_router)
fastapi.include_router(instance_router)
fastapi.include_router(tenant_router)
fastapi.include_router(devhub_router)
fastapi.include_router(motd_router)
fastapi.include_router(actions_router)

# fastapi.add_middleware(HTTPSRedirectMiddleware)
# TODO - issues with this middleware, how should we manage this?

origins = ["https://localhost",
           "https://localhost:3000",
           "https://***********",
           "https://***********:3000",
           "https://dev-ocp.ip.comcast.net",
           "https://dev-ocp.ip.comcast.net:3000",
           "https://dev.ocp.comcast.net",
           "https://dev.ocp.comcast.net:3000",
           "https://test.ocp.comcast.net",
           "https://ocp.comcast.net",
           "https://api.ocp.comcast.net"
           ]
fastapi.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    # allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"])


@fastapi.get("/")
def get_root():
    return {"app": "Observability Control Plane API (OCPAPI)",
            "version": version}


@fastapi.get('/favicon.ico', include_in_schema=False)
def favicon():
    return None


instrumentator = Instrumentator(
    should_group_status_codes=False,
    should_ignore_untemplated=True,
    should_respect_env_var=True,
    should_instrument_requests_inprogress=True,
    excluded_handlers=[".*admin.*", "/metrics"],
    env_var_name="ENABLE_METRICS",
    inprogress_name="inprogress",
    inprogress_labels=True,
)

instrumentator.add(
    metrics.request_size(
        should_include_handler=True,
        should_include_method=False,
        should_include_status=True,
        metric_namespace="ocp",
        metric_subsystem="api",
    )
).add(
    metrics.response_size(
        should_include_handler=True,
        should_include_method=False,
        should_include_status=True,
        metric_namespace="ocp",
        metric_subsystem="api",
    )
)

instrumentator.instrument(fastapi).expose(fastapi)

if __name__ == "__main__":
    env = os.environ.get('PLATFORMENV', 'local')
    keyfile = None
    certfile = None
    if env == 'local':
        keyfile = "./certs/localhost/localhost.key"
        certfile = "./certs/localhost/localhost.cert"
    elif env == 'docker':
        keyfile = "/run/secrets/ssl_key"
        certfile = "/run/secrets/ssl_cert"
    else:
        keyfile = "./certs/key.pem"
        certfile = "./certs/crt.pem"

    # TODO - read workers val from env var and store in k8s configmap
    uvicorn.run("ocpapi:fastapi", log_level="debug" if env != "prod" else
                "info", workers=6 if env == 'rdei' else 1, loop="asyncio",
                # limit_concurrency=24, loop="uvloop",
                reload=True if env == 'local' or env == 'docker' else False,
                host="0.0.0.0", port=7443,
                ssl_keyfile=keyfile,
                ssl_certfile=certfile,
                log_config="config/logging.conf")
