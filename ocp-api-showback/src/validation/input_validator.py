import logging
import logging.config
import re
from fastapi import HTTPException
from typing import Any

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)


def validate_input(value, field):
    if field.name == 'type':
        return value
    # TODO - does enum cover validation of value?

    if field.name == 'closeable':
        if isinstance(value, bool):
            return value

    if isinstance(value, str):
        validate_string(value, field.name)
        return value
    # TODO - use separate validator for bson ObjectIds, refactor to identify
    # inputs that are id's vs strings

    if isinstance(value, int):
        if field.name == 'delete_ts':
            if not re.search(r'^(0|\d{10})$', str(value)):
                raise HTTPException(status_code=400,
                                    detail="Value should be either zero or"
                                           " valid epoch timestamp")
            return value
        elif not re.search(r'^\d{10}$', str(value)):
            raise HTTPException(status_code=400,
                                detail="Epoch timestamp must be 10 digits "
                                       f"(seconds): '{field.name}'={value}")
        return value
    # TODO - refactor for additional int fields

    if isinstance(value, float):
        return value
    # TODO - what to validate here?

    if isinstance(value, list):
        if field.name in ['instance_ids', 'member']:
            for val in value:
                validate_string(val, field.name)
            return value
        return value
    # TODO - make more generic to accept lists containing different types
    #      - and lists from any field


def validate_string(value, field):
    if re.search(r'[!@#$%^&*()+={}\|\\;"?~`]', value):
        logging.debug(f"String value invalid: {value}")
        raise HTTPException(status_code=400,
                            detail=f"Only alphanumerics allowed: {field}")
    elif len(value) > 500:
        raise HTTPException(status_code=400,
                            detail=f"Length must be less than 500 "
                                   f"characters: {field}")


def validate_tenant_id(value):
    if not re.match(r'^[a-zA-Z0-9]{24}$', value):
        raise HTTPException(status_code=400,
                            detail="Field tenant_id is a 24 character "
                                   "alphanumeric")


def validate_regex(value):
    if not re.match(r'^[a-zA-Z0-9 \-\[\]\(\)\*/\^\.\?\$]{1,100}$', value):
        raise HTTPException(status_code=400,
                            detail="Invalid regular expression")
    return value


def validate_tenant_apps(value):
    if not isinstance(value, list):
        raise ValueError("tenant_apps must be a list of integers")

    if len(value) == 0:
        raise ValueError("tenant_apps list cannot be empty")

    for item in value:
        if not 1000 <= item <= 999999:
            raise ValueError("Each ID in tenant_apps must be 4 to 6 digits"
                             "long")

    return value


def validate_link(value):
    """ Validates the link for a MOTD button.
        Ensures it is a fully qualified or relative URL.
    """
    # Check if it starts with http://, https://, or /
    if not re.match(r'^(https?://|http://|/)', value):
        raise ValueError('Invalid link provided. Link must be a fully '
                         'qualified URL or a relative URL starting with "/".')
    return value


def validate_devhub_apps(cls, value, values, field):
    if field.name == 'app_name':
        if values.get('app_ids') and value.strip() != "":
            raise ValueError("app_name must be an empty string when app_ids "
                             "is provided.")
        if len(value) > 100:
            raise ValueError("app_name length must be 100 characters or less.")

    elif field.name == 'app_ids':
        # Ensure app_name is empty when app_ids is provided
        app_name = values.get('app_name', '').strip()
        if app_name:
            raise ValueError("app_ids can only be used when app_name is an "
                             "empty string.")

        # Validate each ID in app_ids
        if value is not None:
            for app_id in value:
                if not 1000 <= app_id <= 999999:
                    raise ValueError("Each ID in app_ids must be 4 to 6 "
                                     "digits long.")

    return value


def validate_motd_message(value: Any, field: str) -> Any:
    """
    Validates the fields within MOTDMessage.
    Ensures the type field is within an accepted list of values, etc.
    """
    if field == 'type':
        valid_types = ['info', 'warning', 'alert', 'delete', 'inprogress']
        if value not in valid_types:
            raise ValueError(f"Type must be one of {valid_types}.")
    elif field in ['id', 'title', 'text']:
        if not isinstance(value, str) or not value.strip():
            raise ValueError(f"{field.capitalize()} must be a non-empty "
                             "string.")
    elif field == 'closeable':
        if not isinstance(value, bool):
            raise ValueError("Closeable must be a boolean.")
    return value

# TODO - refactor validation to raise exceptions that are meant for
# clients or are handled internally (only raise HTTP exception when
# a validator is run on input passed by a client)
