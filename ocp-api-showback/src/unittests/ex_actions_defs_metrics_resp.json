{"actions": [{"type": "View VMAuth Log", "description": "View VMAuth log", "payload": null, "valid_roles": ["owner", "operator"], "valid_states": ["provisioning_completed", "cluster_restarting", "remapping_completed", "nominal"], "destructive": false}, {"type": "Update Metadata", "description": "Update metadata for Instance", "payload": {"fields": [{"label": "description", "value": "a new description"}, {"label": "name", "value": "a new name"}]}, "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "cluster_restarting", "remapping_completed", "nominal"], "destructive": false}, {"type": "Restart VMAgent", "description": "Restart the VMAgent server", "payload": null, "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": false}, {"type": "<PERSON>art <PERSON>", "description": "Restart VMAuth server", "payload": null, "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": false}, {"type": "Delete Instance", "description": "Delete Instance", "payload": null, "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": true}]}