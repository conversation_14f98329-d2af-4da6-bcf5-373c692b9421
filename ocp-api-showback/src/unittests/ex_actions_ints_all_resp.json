{"metrics": {"interfaces": [{"title": "View Log Interface", "instructions": "Submit to view logs", "action_type": "View Log"}, {"title": "Update Metadata Interface", "instructions": "Fill out required fields and submit to update metadata", "input_rows": [{"input": {"label": "name", "type": "shorttext"}, "required": true}, {"input": {"label": "description", "type": "longtext"}, "required": true}], "action_type": "Update Metadata"}, {"title": "Restart VMAgent Interface", "instructions": "Submit to restart VMAgent", "action_type": "Restart VMAgent"}, {"title": "<PERSON>art <PERSON>", "instructions": "Submit to restart VMAuth", "action_type": "<PERSON>art <PERSON>"}, {"title": "Delete Instance", "instructions": "Submit to start Instance deletion", "action_type": "Delete Instance"}]}, "logging": {"interfaces": [{"title": "View Log Interface", "instructions": "Submit to view logs", "action_type": "View Log"}, {"title": "Update Metadata Interface", "instructions": "Fill out required fields and submit to update metadata", "input_rows": [{"input": {"label": "name", "type": "shorttext"}, "required": true}, {"input": {"label": "description", "type": "longtext"}, "required": true}], "action_type": "Update Metadata"}, {"title": "Restart Cluster Interface", "instructions": "Submit to restart the ES cluster", "action_type": "Restart Cluster"}, {"title": "Restart <PERSON> Interface", "instructions": "Submit to restart the Kibana server", "action_type": "<PERSON><PERSON>"}, {"title": "Delete Instance Interface", "instructions": "Submit to start Instance deletion", "action_type": "Delete Instance"}]}}