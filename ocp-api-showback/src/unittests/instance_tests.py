import json
import logging
import unittest

from unittest.mock import patch, MagicMock
from fastapi import Request
from bson.objectid import ObjectId

from src.clients import mongo
from src.clients import pillars
from src.handlers import instances as instances_h
# from src.models.input import create_instance_request
from src.models.provision import ProvisionForm
from src.models.instance_details import InstanceDetails
from src.auth import oidc as auth  # noqa: F401


logging.getLogger("src.handlers.instances").setLevel(logging.CRITICAL)


req_log = {'uri': '/',
           'server': ('127.0.0.1', 8008),
           'client': ('127.0.0.1', 40174), 'body': None, 'url':
           'https://localhost:8008/instances/tenant/...',
           'request_id':
           'UX6bhIq5xk8MGnw7271l0VwLuloIiomB2CYUUIFlEes',
           'cookies': {}}

form_f = open('src/unittests/example_provision_form_resp.json')
provision_form_resp_val = json.load(form_f)
form_f.close()

details_f = open('src/unittests/example_instance_details_resp.json')
inst_details_resp_val = json.load(details_f)
details_f.close()


class TestInstanceHandlers(unittest.TestCase):

    metrics_api_inst_by_user_resp = \
        {"instances": [{"id": "33496dab-8734-4d4e-bfda-c03f5dd24658",
                        "name": "PPTT", "region": "HO-B", "type": "metrics"},
                       {"id": "98d61cd0-4c51-4b9f-be13-4633958325ec",
                        "name": "dashboard", "region": "AS-D", "type": "metrics"},
                       {"id": "bfcb089c-1967-4ad2-bac7-2e11fba398ce",
                        "name": "Synthetic Metrics", "region": "HO-B",
                        "type": "metrics"}],
         "metadata": {"total_instances": 3}}

    metrics_api_inst_by_tenant_resp = \
        {"instances": [{"id": "73d92c40-9884-464e-8382-226ac6238bc2",
                        "name": "cpp-production", "region": "AS-D",
                        "type": "metrics"},
                       {"id": "b539d8e9-c5d4-4ae7-88d1-20b08e2193ba",
                        "name": "squirrel_dna", "region": "AS-D",
                        "type": "metrics"},
                       {"id": "98d61cd0-4c51-4b9f-be13-4633958325ec",
                        "name": "dashboard", "region": "AS-D",
                        "type": "metrics"}],
         "metadata": {"total_instances": 3}}

    mongo_get_tenant_resp = \
        {'_id': ObjectId('63d818472a700f02fc396ae7'),
         'name': 'Test Tenant xyz', 'description': 'This is a tenant for '
         + 'testing 3',
         'owner': [{'oid': '01410c98-8d33-4e58-b0bf-9360d7a32a92',
                    'name': 'PMMS Interviews', 'description':
                    'PMMS Interviews'},
                   {'oid': '49ae0084-879d-4030-bb03-93021db29329', 'name':
                    'DevX', 'description': 'This is the public Team for the'
                    + 'DevX organization'}],
         'created': 1675106375, 'updated': 1675194616, 'delete_ts': 0}

    metrics_api_get_cr_schema_resp = \
        {"name": "metrics",
         "inputs": [{"type": "text",
                     "label": "Region",
                     "name": "region"},
                    {"type": "text",
                     "label": "Name",
                     "name": "name"}]
         }

    metrics_api_create_inst_resp = \
        {"id": "7076ff03-2200-4917-8e06-c50e1fb7089a",
         "name": "New Instance for Test",
         "application": "maas",  # noqa: E999
         "regions": "AS-D",
         "users": ["a_ntid"],
         }

    get_auth_resp = \
        {"status": "Authenticated",
         "ntid": "a_ntid"}

    add_inst_tenant_val = \
        {'_id': ObjectId('93a118472a7002fc92345521'),
         "name": "New Tenant for Test", "description": "Test Create Tenant X",
         "owner": [{"oid": "01410c98-8d33-4e58-b0bf-9360d7a32a92",
                    "name": "PMMS Interviews",
                    "description": "PMMS Interviews"}],
         "created": 1675709043, "updated": 1675709043, 'delete_ts': 0}

    auth_status_val = \
        {"status": "Authenticated",
         "expireTime": 3592,
         "name": "Alex Dezendorf",
         "email": "<EMAIL>",
         "ntid": "adezen200"}

    get_tenant_by_id_val = \
        {'_id': ObjectId('13b818472a700f02fc396ae2'),
         'name': 'Test Tenant ABC', 'description': 'This is a testing Tenant',
         'owner': [{'oid': '01410c98-8d33-4e58-b0bf-9360d7a32a92', 'name':
                    'PMMS Interviews', 'description': 'PMMS Interviews'},
                   {'oid': '49ae0084-879d-4030-bb03-93021db29329',
                    'name': 'DevX', 'description':
                    'This is the public Team for the DevX organization'}],
         'created': 1675106375, 'updated': 1675194616, 'delete_ts': 0}

    inst_by_id_val = \
        {"id": "f3c5ada9-e0a0-499a-920e-17703e45879f",
         "name": "xfp-prod", "type": "metrics", "tenant_id":
         "13b818472a700f02fc396ae2", "fields":
         [{"key": "tenant_group", "value": "XFP"},
          {"key": "region", "value": "HO-B"},
          {"key": "description", "value": "XFP-Prod Metrics HO-B"}]}

    get_tenants_by_adg_oids_val_id = \
        [{'_id': ObjectId('63d818472a700f02fc396ae7'),
          'name': 'Test Tenant xyz', 'description': 'This is a tenant for '
         + 'testing 3',
          'owner': [{'oid': '01410c98-8d33-4e58-b0bf-9360d7a32a92',
                    'name': 'PMMS Interviews', 'description':
                     'PMMS Interviews'},
                    {'oid': '49ae0084-879d-4030-bb03-93021db29329', 'name':
                    'DevX', 'description': 'This is the public Team for the'
                     + 'DevX organization'}],
          'created': 1675106375, 'updated': 1675194616, 'delete_ts': 0}]

    get_tenants_by_adg_oids_val_create = \
        [{'_id': ObjectId('93a118472a7002fc92345521'),
          'name': 'Test Tenant xyz', 'description': 'This is a tenant for '
                                                    + 'testing 3',
          'owner': [{'oid': '01410c98-8d33-4e58-b0bf-9360d7a32a92',
                     'name': 'PMMS Interviews', 'description':
                         'PMMS Interviews'},
                    {'oid': '49ae0084-879d-4030-bb03-93021db29329', 'name':
                        'DevX',
                     'description': 'This is the public Team for the'
                                    + 'DevX organization'}],
          'created': 1675106375, 'updated': 1675194616, 'delete_ts': 0}]

    get_ad_groups_val = \
        [{"oid": "01410c98-8d33-4e58-b0bf-9360d7a32a92",
         "name": "PMMS Interviews", "description": "PMMS Interviews"},
         {"oid": "01e60fed-c029-4f9d-a903-1edb8d0ea458",
          "name": "[CLIPS - Interface POCs]", "description": ""},
         {"oid": "022089c3-3912-46cc-aa0f-a4afd6e91bfd",
          "name": "[CHQ -- CollibraDev]",
          "description": "Used for Collibra development."}]

#     @patch.object(pillars, 'get_instances_by_user',
#                   return_value=metrics_api_inst_by_user_resp)
#     def test_get_instances_by_user(self, mock_melt_get_inst):
#         print("\nTest get_instances_by_user")
#         req_log['uri'] = '/instances/user'
#
#         instances_by_user_resp = \
#             instances_h._get_instances_by_user(None, req_log, 'user123',
#                                                'metrics')
#
#         self.assertEqual(len(instances_by_user_resp), 3)
#         self.assertEqual(instances_by_user_resp[0].id,
#                          '33496dab-8734-4d4e-bfda-c03f5dd24658')
#         self.assertEqual(instances_by_user_resp[0].name, 'PPTT')
#         self.assertEqual(instances_by_user_resp[0].tenant_id, None)
#         self.assertEqual(instances_by_user_resp[0].tenant_name, None)
#         self.assertEqual(instances_by_user_resp[0].type, 'metrics')

    # @patch.object(mongo.Role, 'has_role_user',
    #               return_value=(True, 'owner'))

    @patch.object(pillars, 'get_instances_by_tenant',
                  return_value=metrics_api_inst_by_tenant_resp)
    @patch.object(mongo.Tenant, 'find_one',
                  return_value=mongo_get_tenant_resp)
    @patch.object(auth, '_get_user_ad_groups',
                  return_value=get_ad_groups_val)
    @patch.object(auth, 'user_has_required_role',
                  return_value=True)
    @patch.object(auth, '_get_auth_status_claims',
                  return_value={'ntid': 'mock_ntid'})
    def test_get_instances_by_tenant(self, mock_auth_status,
                                     mock_user_has_role,
                                     mock_get_user_ad_groups,
                                     mock_melt_get_inst,
                                     mock_mongo_get_tenant):
        print("\n\nTest get_instances_by_tenant")
        req_log['uri'] = '/instances/tenant/...'

        # Mock request object
        mock_request = MagicMock(spec=Request)
        mock_request.cookies = {'access_token': 'mock_access_token'}
        mock_request.headers = {'Authorization': 'Bearer mock_access_token'}
        mock_request.scope = {
            'path': '/api/v1/tenants/create',
            'server': ('127.0.0.1', 8008),
            'client': ('127.0.0.1', 40174)
        }

        instances_by_tenant_resp = \
            instances_h._get_instances_by_tenant(mock_request, req_log,
                                                 '63d818472a700f02fc396ae7')

        self.assertEqual(len(instances_by_tenant_resp), 3)
        self.assertEqual(instances_by_tenant_resp[0].id,
                         '73d92c40-9884-464e-8382-226ac6238bc2')
        self.assertEqual(instances_by_tenant_resp[0].name, 'cpp-production')
        self.assertEqual(instances_by_tenant_resp[0].tenant_id,
                         '63d818472a700f02fc396ae7')
        self.assertEqual(instances_by_tenant_resp[0].tenant_name,
                         'Test Tenant xyz')
        self.assertEqual(instances_by_tenant_resp[0].type, 'metrics')
        try:
            instances_h._get_instances_by_tenant(None, req_log,
                                                 '63d818472a700f02fc396a')
        except Exception as e:
            self.assertEqual(e.status_code, 400)
            self.assertEqual(e.detail,
                             "Field tenant_id is a 24 character alphanumeric")
        try:
            instances_h._get_instances_by_tenant(None, req_log,
                                                 '63d81847%^#396a')
        except Exception as e:
            self.assertEqual(e.status_code, 400)
            self.assertEqual(e.detail,
                             "Field tenant_id is a 24 character alphanumeric")

    # @patch.object(mongo.Role, 'has_role_user',
    #               return_value=(True, 'owner'))

    @patch.object(pillars, 'get_instances_by_tenant',
                  return_value=metrics_api_inst_by_tenant_resp)
    @patch.object(mongo.Tenant, 'find_one',
                  return_value=mongo_get_tenant_resp)
    @patch.object(auth, '_get_user_ad_groups',
                  return_value=get_ad_groups_val)
    @patch.object(auth, 'user_has_required_role',
                  return_value=True)
    @patch.object(auth, '_get_auth_status_claims',
                  return_value={'ntid': 'mock_ntid'})
    def test_get_instances_by_tenant_slug(self, mock_mongo_get_tenant,
                                          mock_melt_get_inst,
                                          mock_get_user_ad_groups,
                                          mock_get_tenants_by_adg_oids,
                                          mock_auth_status):
        print("\n\nTest get_instances_by_tenant_slug")
        req_log['uri'] = '/instances/tenant/...'

        instances_by_tenant_slug_resp = \
            instances_h._get_instances_by_tenant_slug(None, req_log,
                                                      'cpp-production',
                                                      'metrics')

        self.assertEqual(len(instances_by_tenant_slug_resp), 3)
        self.assertEqual(instances_by_tenant_slug_resp[0].id,
                         '73d92c40-9884-464e-8382-226ac6238bc2')
        self.assertEqual(instances_by_tenant_slug_resp[0].name,
                         'cpp-production')
        self.assertEqual(instances_by_tenant_slug_resp[0].tenant_id,
                         '63d818472a700f02fc396ae7')
        self.assertEqual(instances_by_tenant_slug_resp[0].tenant_name,
                         'Test Tenant xyz')
        self.assertEqual(instances_by_tenant_slug_resp[0].type, 'metrics')
        try:
            instances_h._get_instances_by_tenant_slug(None, req_log,
                                                      '63d81847%^#396a',
                                                      'metrics')
        except Exception as e:
            self.assertEqual(e.status_code, 400)
            self.assertEqual(e.detail, "Only alphanumerics allowed: slug")

    @patch.object(pillars, 'get_instances_by_tenant',
                  return_value=metrics_api_inst_by_tenant_resp)
    @patch.object(mongo.Tenant, 'find_one',
                  return_value=mongo_get_tenant_resp)
    @patch.object(auth, '_get_user_ad_groups',
                  return_value=get_ad_groups_val)
    @patch.object(mongo.Role, 'has_role_user',
                  return_value=(False, None))
    @patch.object(auth, '_get_auth_status_claims',
                  return_value={'ntid': 'mock_ntid'})
    def test_get_instances_by_tenant_noaccess(self, mock_mongo_get_tenant,
                                              mock_melt_get_inst,
                                              mock_get_user_ad_groups,
                                              mock_get_tenants_by_adg_oids,
                                              mock_get_auth_status_claims):
        try:
            print("\n\nTest get_instances_by_tenant (no access)")
            req_log = {'uri': '/instances/tenant/...',
                       'server': ('127.0.0.1', 8008),
                       'client': ('127.0.0.1', 40174), 'body': None, 'url':
                           'https://localhost:8008/instances/tenant/...',
                       'request_id':
                           'UX6bhIq5xk8MGnw7271l0VwLuloIiomB2CYUUIFlEes',
                       'cookies': {}}

            instances_h._get_instances_by_tenant(None, req_log,
                                                 '63d818472a700f02fc396ae7')
        except Exception as e:
            self.assertEqual(e.detail, "Forbidden: User mock_ntid does not "
                             + "have privileges on Tenant 'Test Tenant xyz'")

    def test_parse_provision_form(self):
        print("\n\nTest parse_provision_form")
        req_log['uri'] = '/instances/provisionform/validate'

        form_resp = instances_h.parse_provision_form(provision_form_resp_val)

        self.assertIsInstance(form_resp, ProvisionForm)

#    @patch.object(metrics_api, 'create_instance',
#                  return_value=metrics_api_create_inst_resp)
#    @patch.object(auth, '_get_auth_status_claims',
#                  return_value=get_auth_resp)
#    @patch.object(mongo.Tenant, 'find_one',
#                  return_value=add_inst_tenant_val)
#    def test_provision_instance_metrics(self, mock_get_tenant, mock_get_auth,
#                                        mock_create_inst_metrics):
#        print("\nTest create_instance (metrics)")
#        req_log['uri'] = '/instances/provision/...'
#
#        # TODO - rework provision test for new models
#
#        prov_inputs = [...
#
#        cr_inst_req = \
#            provision_request(pillar="Metrics",
#                              tenant_id="93a118472a7002fc92345521",
#                              inputs=...
#
#                              # name="New Instance for Test",
#                              # region="AS-D",
#                              # type="Standard")
#
#        create_instance_metrics_resp = \
#            instances_h._create_instance(None, req_log, cr_inst_req)
#
#        assert create_instance_metrics_resp
#        expected_response_dict = \
#            {"id": "7076ff03-2200-4917-8e06-c50e1fb7089a",
#             "name": "New Instance for Test",
#             "tenant_id": "93a118472a7002fc92345521",
#             "tenant_name": "New Tenant for Test",
#             "type": "Metrics",
#             "region": "AS-D",
#             "description": ""
#             }
#
#        self.assertEqual(create_instance_metrics_resp.id,
#                         expected_response_dict['id'])
#        self.assertEqual(create_instance_metrics_resp.name,
#                         expected_response_dict['name'])
#        self.assertEqual(create_instance_metrics_resp.type,
#                         expected_response_dict['type'])
#        self.assertEqual(create_instance_metrics_resp.region,
#                         expected_response_dict['region'])
#        try:
#            cr_inst_req_inval = \
#                create_instance_request(pillar="Metrics",
#                                        name="New I@nstance for Test",
#                                        region="AS-D",
#                                        type="Standard",
#                                        tenant_id="93a1184720##02fc92345521")
#            instances_h._create_instance(None, req_log, cr_inst_req_inval)
#        except Exception as e:
#            self.assertEqual(e.status_code, 400)
#            self.assertEqual(e.detail,
#                             "Only alphanumerics allowed in strings")
#
# TODO - rework provisioning, pending metricsapi changes

    @patch.object(pillars, 'get_provision_form',
                  return_value=provision_form_resp_val)
    def test_get_provision_form(self, mock_get_pform_metrics):
        print("\n\nTest get_provision_form (metrics)")
        req_log['uri'] = '/instances/provisionform'

        prov_form_metrics = instances_h._get_provision_form(
            request=None, request_log=req_log, pillar="metrics")

        assert prov_form_metrics
        self.assertEqual(prov_form_metrics.pillar, "metrics")
        self.assertEqual(prov_form_metrics.instructions.type, "numbered")
        self.assertEqual(len(prov_form_metrics.inputs), 7)
        self.assertEqual(prov_form_metrics.inputs[1].name, "provisionTarget")
        self.assertEqual(len(prov_form_metrics.inputs[2].options), 3)
        self.assertEqual(len(prov_form_metrics.inputs[2].options), 3)
        self.assertEqual(prov_form_metrics.inputs[2].conditional.value, "aws")
        self.assertEqual(len(prov_form_metrics.inputs[3].options), 6)
        self.assertEqual(prov_form_metrics.inputs[4].label, "RDEI Size")

#        ep = open('src/unittests/example_provision_form_parsed.json')
#        expected_response = json.load(ep)
#        ep.close()
#        self.assertEquals(get_prov_form_resp, expected_response)
# TODO - must first serialize ProvisionForm objects ^^^

#    @patch.object(metrics_api, 'provision_instance',
#                  return_value=prov_instance_resp_val)
#    def test_provision_instance(self, mock_prov_inst_resp):
#        print("\nTest provision_instance (metrics)")
#        req_log['uri'] ='/instances/provision'
#
#        prov_instance_resp = instances_h._provision_instance(
#            request=None, req_log=req_log,
#            provision_req=provision_req_val)
#
#        assert prov_instance_resp
# TODO - rework provisioning based on new requirements

    @patch.object(mongo.Tenant, 'get_tenant_by_id',
                  return_value=mongo_get_tenant_resp)
    def test_parse_details(self, mock_tenant_id):
        print("\n\n Test parse_details (metrics)")
        req_log['uri'] = '/instances/details/validate'

        details_resp = instances_h.parse_details(inst_details_resp_val)

        assert details_resp
        self.assertIsInstance(details_resp, InstanceDetails)

    # @patch.object(mongo.Role, 'has_role_user',
    #               return_value=(True, 'owner'))
    # TODO - find import method to patch this

    @patch.object(pillars, 'get_instance_by_id',
                  return_value=inst_by_id_val)
    @patch.object(auth, 'user_has_required_role',
                  return_value=True)
    @patch.object(pillars, 'get_instance_details',
                  return_value=inst_details_resp_val)
    @patch.object(mongo.Tenant, 'get_tenant_by_id',
                  return_value=get_tenant_by_id_val)
    @patch.object(auth, '_get_auth_status_claims',
                  return_value=auth_status_val)
    def test_get_instance_details(self, mock_auth_status_resp,
                                  mock_tenant_by_id_val,
                                  mock_inst_det_resp,
                                  mock_has_access,
                                  mock_inst_by_id_resp):
        print("\n\nTest get_instance_details (metrics)")
        req_log['uri'] = '/instances/details/...'

        details = instances_h._get_instance_details(
            request=None,
            request_log=req_log,
            pillar="metrics",
            instance_id="73d92c40-9884-464e-8382-226ac6238bc2")

        assert details

        self.assertEqual(details.info.instance.name, "xfp-prod")
        self.assertEqual(details.info.instance.tenant_id,
                         "13b818472a700f02fc396ae2")
        self.assertEqual(len(details.info.instance.fields), 3)
        self.assertEqual(
            details.info.stack[0].link,
            "https://xfp-prod-ho-b.consul.metrics.devx.comcast.net")
        self.assertEqual(len(details.info.stack), 2)
        self.assertEqual(len(details.metrics.panels), 1)
        self.assertEqual(details.showback.title, "Showback Costs vs Util")

#        ep = open('src/unittests/example_instance_details_parsed.json')
#        expected_response = json.load(ep)
#        ep.close()
#
#        self.assertEqual(
#            json.dumps(details_resp,
#                       default=lambda o: o.__dict__),
#                          expected_response)
# TODO - must first serialize InstanceDetails object ^^^

# TODO - add unit tests to cover validation on:
# |  get_instances_by_user
# |  get_provision_form
# |    - add unit tests to cover validation on each field in
# |     input models
# | ...
