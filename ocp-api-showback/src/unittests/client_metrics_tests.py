# import os
# import time
# import unittest
# from src.clients import metrics_api


# class TestMetricsClient(unittest.TestCase):
#
#     @classmethod
#     def setUpClass(self):
#         print("\nStarting METRICS API...")
#         os.system("docker compose up metricsapi mongo > /dev/null &")
#         time.sleep(4)  # give a few seconds for containers to spin up
#
#     def test_get_instances_by_user(self):
#         pass
