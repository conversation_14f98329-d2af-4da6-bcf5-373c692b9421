import json
import unittest
from unittest.mock import patch

from src.clients import pillars

from src.models.actions import Actions, ActionInterfaces
from src.handlers import actions as actions_h

req_log = {'uri': '/',
           'server': ('127.0.0.1', 8008),
           'client': ('127.0.0.1', 40174), 'body': None, 'url':
           'https://localhost:8008/instances/actions/...',
           'request_id':
           'UX6bhIq5xk8MGnw7271l0VwLuloIiomB2CYUUIFlEes',
           'cookies': {}}

with open('src/unittests/ex_actions_ints_all_resp.json') as file:
    actions_ints_all_resp = json.load(file)

with open('src/unittests/ex_actions_defs_metrics_resp.json') as file:
    actions_defs_metrics_resp = json.load(file)

with open('src/unittests/ex_actions_defs_logging_resp.json') as file:
    actions_defs_metrics_resp = json.load(file)

with open('src/unittests/ex_actions_defs_all_resp.json') as file:
    actions_defs_all_resp = json.load(file)


class TestActionsHandlers(unittest.TestCase):
    def setUp(self):
        pass

    def test_parse_actions_definitions(self):
        print("\nTest parse_actions_definitions")
        req_log['uri'] = "/instances/actions/validate"

        actions_parse_resp = \
            actions_h.parse_actions_definitions(actions_defs_all_resp)

        for p_key in actions_parse_resp:
            self.assertIsInstance(actions_parse_resp[p_key], Actions)

    def test_parse_actions_interfaces(self):
        print("\nTest parse_actions_interfaces")
        req_log['uri'] = "/instances/actions/validate"

        actions_ints_parse_resp = \
            actions_h.parse_actions_interfaces(actions_ints_all_resp)

        for p_key in actions_ints_parse_resp:
            self.assertIsInstance(actions_ints_parse_resp[p_key],
                                  ActionInterfaces)

    @patch.object(pillars, 'get_actions_definitions',
                  return_value=actions_defs_all_resp)
    def test_get_actions_definitions(self, mock_pillars_get_actions
                                     ):
        print("\nTest get_actions_definitions")
        req_log['uri'] = "/instances/actions/definitions"

        actions_def_resp = \
            actions_h._get_actions_definitions(None, req_log)

        # TODO - find better solution here, object is mix of dictionary
        #   and objects,
        for pillar in actions_def_resp.keys():
            pillar_actions = {'actions': []}
            for a in actions_def_resp[pillar].actions:
                pillar_actions['actions'].append(json.loads(a.json()))

            for a in pillar_actions['actions']:
                if a['payload'] is None:
                    del a['payload']
                    del a['required_payload_fields']

            self.assertEqual(pillar_actions, actions_defs_all_resp[pillar])

    def test_get_actions_interfaces(self):
        pass
