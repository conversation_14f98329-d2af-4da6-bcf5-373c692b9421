import logging
import unittest

from unittest.mock import patch, MagicMock, ANY
from fastapi import Request
from bson.objectid import ObjectId

from src.handlers.tenants import (
    _get_tenants_by_user,
    _get_tenant_by_id,
    _get_tenant_by_slug,
    _get_tenants_by_regex_search,
    provision_ad_groups,
    provision_github_resources,
    provision_visualization_instance,
    send_provision_email,
    _create_tenant,
    _delete_tenant,
    _get_roles,
    _delete_role_and_ad_groups,
    rename_git_repo,
    rename_tenant_in_db,
)

from src.handlers import tenants as tenants_h

from src.clients.mongo import Tenant, Role, ADGroup
from src.clients.git import GHClient
from src.clients.email_client import EmailClient
from src.clients.actived import ADClient
from src.clients import pillars
from src.auth import oidc as auth  # noqa: F401

logging.getLogger("src.clients.git").setLevel(logging.CRITICAL)


# Mock Data
mock_create_tenant_request = MagicMock()
mock_create_tenant_request.name = 'New Tenant for Test'
mock_create_tenant_request.description = 'This is a \
    description for a new tenant.'
mock_create_tenant_request.tenant_apps = [1001, 1002]  # Valid values

# Mock Data for src.clients.ADGroup
mock_ad_group_record = {
    '_id': ObjectId('66aacee981bb8661b0866678'),
    'oid': '69df8c74-f9e9-4c89-b927-77c7c89517fe',
    'name': 'O11Y - TEST SUSHOVAN TEST - Owners',
    'desc': 'OCP Tenant Group for TEST SUSHOVAN TEST Owners'
}

# Mock Data for Provision Response
provision_groups_resp_val = {
    'tenant': 'New Tenant for Test',
    'group_names': [
        'test_owners', 'test_ops', 'test_users', 'test_collabs'
    ],
    'group_objects': {
        'owners': {
            'id': '01410c98-8d33-4e58-b0bf-9360d7a32a92',
            'displayName': 'Test Tenant Owners',
            'description': ''
        },
        'ops': {
            'id': '01410c98-8d33-4e58-b0bf-9360d7a32342',
            'displayName': 'Test Tenant Ops',
            'description': ''
        },
        'users': {
            'id': '01410c98-8d33-4e58-b0bf-9360d7ab2d11',
            'displayName': 'Test Tenant Users',
            'description': ''
        },
        'collabs': {
            'id': '01410c98-8d33-4e58-b0bf-9360d7266666',
            'displayName': 'Test Tenant Collabs',
            'description': ''
        },
    }
}

create_tenant_response = {
    '_id': ObjectId('93a118472a7002fc92345521'),
    "name": "New Tenant for Test", "description": "Test Create Tenant X",
    "slug": "new-tenant-for-test",
    'tenant_apps': [1001, 1002],
    'git_repo': 'new-tenant-for-test',
    "created": 1675709043, "updated": 1675709043, 'delete_ts': 0
}

mock_provision_response = {
    "instance": {
        "id": "mock_id",
        "name": "Mock Instance",
        "type": "visualization",  # or whatever valid enum value you have
        "status": "active",
        "tenant_id": "mock_tenant_id",
        "tenant_name": "Mock Tenant",
        "fields": [
            {"key": "mock_key1", "value=": "mock_value1"},
            {"key": "mock_key2", "value": 123},
            {"key": "mock_key3", "value": True}
        ]
    }
}

create_tenant_ad_group_val = \
    {"_id": ObjectId("63d818472a700f02fc396ae6"),
     "oid": "01410c98-8d33-4e58-b0bf-9360d7a32a92", "name":
     "PMMS Interviews", "description": "PMMS Interviews"
     }

# Mock Data for src.clients.Role
mock_role_record = \
    {'_id': ObjectId('66aacee981bb8661b086667c'),
     'tenant_id': '64d3e74e7f28d3b29f7d8f36',
     'owners': '69df8c74-f9e9-4c89-b927-77c7c89517fe',
     'ops': '43ac9c33-0203-467a-96dc-0583540a2e75',
     'users': '78b5e99c-4541-4f3c-98b4-98cc6e263af4',
     'collabs': 'aec741b7-c249-4fbc-8d65-540eb56e7943'
     }

# Mock Data for src.clients.Tenant
mock_tenant_record = {
    '_id': ObjectId('64d3e74e7f28d3b29f7d8f36'),
    'name': 'New Tenant for Test',
    'description': 'A tenant for testing',
    'tenant_apps': [1001, 1002],  # Valid values
    'slug': 'test-tenant',
    'git_repo': 'test-tenant-repo',
    'created': 1692355694,
    'updated': 1692355694,
    'delete_ts': 0
}

# Mock request log data
mock_request_log = {
    'uri': '/api/v1/tenants/create',
    'server': ('127.0.0.1', 8008),
    'client': ('127.0.0.1', 40174),
    'body': b'{}',
    'url': "https://localhost:8008/api/v1/tenants/create",
    'request_id': 'mock_request_id'
}

# Mock user claims
mock_user_claims = {
    'ntid': 'mock_ntid',
    'email': '<EMAIL>',
    'name': 'Mock User',
    'oid': 'mock_oid'
}

# Mock tenant data for operations like get_tenant_by_id, etc.
mock_tenant_data = {
    '_id': ObjectId('64d3e74e7f28d3b29f7d8f36'),
    'name': 'New Tenant for Test',
    'description': 'A tenant for testing',
    'tenant_apps': [1001, 1002],  # Valid values
    'slug': 'test-tenant',
    'git_repo': 'test-tenant-repo',
    'created': 1692355694,
    'updated': 1692355694,
    'delete_ts': 0
}

# Mock request object
mock_request = MagicMock(spec=Request)
mock_request.cookies = {'access_token': 'mock_access_token'}
mock_request.headers = {'Authorization': 'Bearer mock_access_token'}
mock_request.scope = {
    'path': '/api/v1/tenants/create',
    'server': ('127.0.0.1', 8008),
    'client': ('127.0.0.1', 40174)
}
mock_request.url = "https://localhost:8008/api/v1/tenants/create"

# Mock AD groups response from ADClient
mock_ad_groups = {
    'owners': {'id': 'owners_id',
               'displayName': 'Owners',
               'description': 'Owners group'},
    'ops': {'id': 'ops_id',
            'displayName': 'Ops',
            'description': 'Ops group'},
    'users': {'id': 'users_id',
              'displayName': 'Users',
              'description': 'Users group'},
    'collabs': {'id': 'collabs_id',
                'displayName': 'Collaborators',
                'description': 'Collaborators group'}
}


class TestTenantHandlers(unittest.TestCase):

    @patch.object(tenants_h, '_delete_role_and_ad_groups', return_value=None)
    @patch.object(Tenant, 'delete_one', return_value=None)
    @patch.object(GHClient, 'delete_repo', return_value=None)
    @patch.object(GHClient, 'delete_team', return_value=None)
    @patch.object(ADGroup, 'delete_one', return_value=None)
    @patch.object(ADClient, 'delete_ad_group', return_value=None)
    @patch.object(EmailClient, 'send_email', return_value=None)
    @patch.object(pillars, 'provision_instance',
                  return_value={'status': 'success'})
    @patch.object(Role, 'get_ad_groups_oids_by_tenant_id', return_value=None)
    @patch.object(GHClient, "add_members_to_repo_team", return_value=None)
    @patch.object(GHClient,
                  'provision_repo_team',
                  return_value={"id": "1",
                                "name": "new-tenant-for-test_owners",
                                "permission": "admin"})
    @patch.object(GHClient,
                  'provision_tenant_repo',
                  return_value={"repo_name": "new-tenant-for-test",
                                "repo_url": "https://github.com/comcast-"
                                + "observability-tenants/new-tenant-for-test",
                                "repo_team": "new-tenant-for-test_owners"})
    @patch.object(Role, 'insert_one', return_value=True)
    @patch.object(ADGroup, 'insert_one', return_value=True)
    @patch.object(ADClient, 'provision_tenant_groups',
                  return_value=provision_groups_resp_val)
    @patch('src.auth.oidc._get_auth_status_claims',
           return_value=mock_user_claims)
    @patch.object(ADGroup, '__init__', return_value=None)
    @patch.object(Tenant, 'get_tenant_by_name',
                  return_value=create_tenant_response)
    @patch.object(Tenant, 'insert_one', return_value=True)
    @patch.object(Tenant, 'find_one', return_value=None)
    @patch.object(Role, '__init__', return_value=None)
    @patch.object(Tenant, '__init__', return_value=None)
    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    @patch.object(GHClient, 'get_installation_access_token',
                  return_value=None)
    def test_create_tenant(self,
                           mock_ghc_init,
                           mock_adc_init,
                           mock_tenant_init,
                           mock_role_init,
                           mock_tenant_find_one,
                           mock_tenant_insert,
                           mock_get_tenant_by_name,
                           mock_db_adgroup_init,
                           mock_get_auth_status,
                           mock_adc_provision_tenant_groups,
                           mock_db_adgroup_insert_one,
                           mock_db_role_insert_one,
                           mock_ghc_provision_tenant_repo,
                           mock_ghc_provision_repo_team,
                           mock_ghc_add_members_to_team,
                           mock_db_role_get_ad_group_oids_by_tenant_id,
                           mock_pillars_provision_viz_instance,
                           mock_ec_send_email,
                           mock_adc_delete_ad_group,
                           mock_db_adgroup_delete_one,
                           mock_ghc_delete_team,
                           mock_ghc_delete_repo,
                           mock_db_tenant_delete_one,
                           mock_tenantsh_delete_role_and_ad_groups):

        print("\n\nTest create_tenant")
        req_log = mock_request_log

        # Mock request object
        mock_request = MagicMock(spec=Request)
        mock_request.cookies = {'access_token': 'mock_access_token'}
        mock_request.headers = {'Authorization': 'Bearer mock_access_token'}
        mock_request.scope = {
            'path': '/api/v1/tenants/create',
            'server': ('127.0.0.1', 8008),
            'client': ('127.0.0.1', 40174)
        }

        # Create tenant request data
        mock_create_tenant_request = MagicMock()
        mock_create_tenant_request.name = "  New Tenant for Test  "
        mock_create_tenant_request.description = "  Test Create Tenant X  "
        mock_create_tenant_request.tenant_apps = [1001, 1002]

        # Act
        tenant_create_resp = _create_tenant(mock_request,
                                            req_log,
                                            mock_create_tenant_request)

        # Assert
        assert tenant_create_resp.name == mock_tenant_data['name']
        mock_ghc_provision_tenant_repo.assert_called_once_with(
            'new-tenant-for-test', [1001, 1002])
        mock_ghc_provision_repo_team.assert_called_once_with(
            'new-tenant-for-test')
        mock_ghc_add_members_to_team.assert_called_once_with(
            'new-tenant-for-test', ['mock_ntid'])
        mock_pillars_provision_viz_instance.assert_called_once()
        mock_ec_send_email.assert_called_once()
        mock_adc_provision_tenant_groups.assert_called_once_with(
            'mock_access_token', 'New Tenant for Test', 'mock_oid')

        # TODO - review process after refactors, see backlog for
        #    ocpapi refactor stories
        # mock_group_find.assert_called()
        # mock_group_insert.assert_called()
        # mock_adgroup_del_one.assert_not_called()

    @patch('src.clients.mongo.Role.get_user_roles',
           return_value={'64d3e74e7f28d3b29f7d8f36': 'owners'})
    @patch('src.clients.mongo.Tenant.get_tenants_by_ids',
           return_value=[mock_tenant_data])
    def test_get_tenants_by_user(self, mock_get_tenants_by_ids,
                                 mock_get_user_roles):
        print("\n\nTest _get_tenants_by_user")
        # Act
        tenants = _get_tenants_by_user(mock_request, mock_request_log)

        # Assert
        self.assertEqual(len(tenants), 1)
        self.assertEqual(tenants[0].name, mock_tenant_data['name'])
        mock_get_user_roles.assert_called_once_with(mock_request)
        mock_get_tenants_by_ids.assert_called_once_with(['64d3e74e7f28d3b29f7d8f36']) # noqa E501

    @patch('src.auth.oidc._get_auth_status_claims',
           return_value=mock_user_claims)
    @patch('src.clients.mongo.Tenant.get_tenant_by_id',
           return_value=mock_tenant_data)
    @patch('src.clients.mongo.Role.has_role_user',
           return_value=(True, 'owners'))
    def test_get_tenant_by_id(self, mock_has_role_user,
                              mock_get_tenant_by_id,
                              mock_get_auth_status_claims):
        print("\n\nTest _get_tenant_by_id")
        # Act
        tenant = _get_tenant_by_id(mock_request,
                                   mock_request_log,
                                   str(mock_tenant_data['_id']))

        # Assert
        self.assertEqual(tenant.name, mock_tenant_data['name'])
        mock_get_tenant_by_id.assert_called_once_with(str(mock_tenant_data['_id'])) # noqa E501
        mock_has_role_user.assert_called_once_with(mock_request,
                                                   str(mock_tenant_data['_id'])) # noqa E501

    @patch('src.auth.oidc._get_auth_status_claims',
           return_value=mock_user_claims)
    @patch('src.clients.mongo.Tenant.get_tenant_by_slug',
           return_value=mock_tenant_data)
    @patch('src.clients.mongo.Role.has_role_user',
           return_value=(True, 'owners'))
    def test_get_tenant_by_slug(self, mock_has_role_user,
                                mock_get_tenant_by_slug,
                                mock_get_auth_status_claims):
        print("\n\nTest _get_tenant_by_slug")
        # Act
        tenant = _get_tenant_by_slug(mock_request,
                                     mock_request_log,
                                     'test-tenant')

        # Assert
        self.assertEqual(tenant.name, mock_tenant_data['name'])
        mock_get_tenant_by_slug.assert_called_once_with('test-tenant')
        mock_has_role_user.assert_called_once_with(mock_request,
                                                   str(mock_tenant_data['_id'])) # noqa E501

    @patch('src.clients.mongo.Tenant.get_tenants_by_name_regex',
           return_value=[mock_tenant_data])
    @patch('src.clients.mongo.Role.has_role_user',
           return_value=(True, 'owners'))
    def test_get_tenants_by_regex_search(self,
                                         mock_has_role_user,
                                         mock_get_tenants_by_name_regex):
        print("\n\nTest _get_tenants_by_regex_search")
        # Act
        tenants = _get_tenants_by_regex_search(mock_request,
                                               mock_request_log,
                                               '.*test.*regex')

        # Assert
        self.assertEqual(len(tenants), 1)
        self.assertEqual(tenants[0].name, mock_tenant_data['name'])
        mock_get_tenants_by_name_regex.assert_called_once_with('.*test.*regex')
        mock_has_role_user.assert_called_once_with(mock_request,
                                                   mock_tenant_data['_id'])

    @patch('src.auth.oidc._get_auth_status_claims',
           return_value=mock_user_claims)
    @patch('src.clients.actived.ADClient.provision_tenant_groups',
           return_value={'group_objects': mock_ad_groups})
    @patch('src.clients.mongo.ADGroup.find_one', return_value=None)
    @patch('src.clients.mongo.ADGroup.insert_one')
    @patch('src.clients.mongo.Role.insert_one')
    @patch.object(ADClient, 'get_service_principal_token',
                  return_value='MOCK TOKEN')
    def test_provision_ad_groups(self, mock_adc_init, mock_insert_role,
                                 mock_insert_ad_group,
                                 mock_find_one, mock_provision_tenant_groups,
                                 mock_get_auth_status_claims):
        print("\n\nTest provision_ad_groups")
        # Act
        ad_group_oids = provision_ad_groups(mock_request,
                                            mock_create_tenant_request,
                                            mock_tenant_data)

        # Assert
        self.assertEqual(len(ad_group_oids), 4)
        mock_provision_tenant_groups.assert_called_once()
        mock_insert_ad_group.assert_called()
        mock_insert_role.assert_called_once()

    @patch('src.clients.git.GHClient.provision_tenant_repo')
    @patch('src.clients.git.GHClient.provision_repo_team')
    @patch('src.clients.git.GHClient.add_members_to_repo_team')
    def test_provision_github_resources(self, mock_add_members,
                                        mock_provision_team,
                                        mock_provision_repo):
        print("\n\nTest provision_github_resources")
        # Act
        result = provision_github_resources(mock_tenant_data, 'mock_ntid')

        # Assert
        self.assertEqual(result, ['test-tenant_owners', 'test-tenant'])
        mock_provision_repo.assert_called_once_with(
            'test-tenant', mock_tenant_data['tenant_apps'])
        mock_provision_team.assert_called_once_with('test-tenant')
        mock_add_members.assert_called_once_with('test-tenant', ['mock_ntid'])

    @patch.object(Role, 'get_ad_groups_oids_by_tenant_id',
                  return_value={'owners': '69df8c74-f9e9-4c89-b927-77c7c89517fe',
                                'ops': '43ac9c33-0203-467a-96dc-0583540a2e75',
                                'users': '78b5e99c-4541-4f3c-98b4-98cc6e263af4',
                                'collabs': 'aec741b7-c249-4fbc-8d65-540eb56e7943'})
    @patch.object(pillars, 'provision_instance', return_value={})
    def test_provision_visualization_instance(self, mock_provision_instance,
                                              mock_get_ad_groups_oids):
        print("\n\nTest provision_visualization_instance")
        # Setup the mock return value to avoid AttributeError
        mock_response = MagicMock()
        mock_response.json.return_value = {"status": "success"}
        mock_provision_instance.return_value = mock_response

        # Act
        try:
            provision_visualization_instance(mock_tenant_data)
        except Exception as e:
            self.fail(f"provision_visualization_instance \
                      raised {e} unexpectedly!")

        # Assert
        mock_get_ad_groups_oids.assert_called_once_with(
            str(mock_tenant_data['_id']))
        mock_provision_instance.assert_called_once_with({
            "instance_name": str(mock_tenant_data['name']),
            "tenant_id": str(mock_tenant_data['_id']),
            "tenant_name": str(mock_tenant_data['name']),
            "git_repo": str(mock_tenant_data['git_repo']),
            "devhub_ids": mock_tenant_data['tenant_apps'],
            "ad_groups": {
                'owners': '69df8c74-f9e9-4c89-b927-77c7c89517fe',
                'ops': '43ac9c33-0203-467a-96dc-0583540a2e75',
                'users': '78b5e99c-4541-4f3c-98b4-98cc6e263af4',
                'collabs': 'aec741b7-c249-4fbc-8d65-540eb56e7943'
            },
            "fields": [
                {
                    "name": "name",
                    "value": str(mock_tenant_data['name'])
                }
            ]
        }, 'visualization')

    @patch('src.clients.email_client.EmailClient.send_email')
    def test_send_provision_email(self, mock_send_email):
        print("\n\nTest send_provision_email")
        # Act
        try:
            send_provision_email('test-tenant',
                                 mock_user_claims, mock_tenant_data)
        except Exception as e:
            self.fail(f"send_provision_email raised {e} unexpectedly!")

        # Assert
        mock_send_email.assert_called_once()

    @patch('src.clients.mongo.Tenant.find_one',
           return_value=mock_tenant_data)
    @patch('src.clients.mongo.Tenant.mark_for_delete',
           return_value=MagicMock(modified_count=1))
    @patch('src.clients.mongo.Tenant.get_tenant_by_id',
           return_value=mock_tenant_data)
    @patch('src.auth.oidc._get_auth_status_claims',
           return_value=mock_user_claims)
    @patch('src.clients.mongo.Role.has_role_user',
           return_value=(True, 'owners'))
    @patch('src.clients.pillars.get_instances_by_tenant',
           return_value={'instances': []})
    def test_delete_tenant(self,
                           mock_get_instances_by_tenant,
                           mock_has_role_user,
                           mock_get_auth_status_claims,
                           mock_get_tenant_by_id,
                           mock_mark_for_delete,
                           mock_find_one):
        print("\n\nTest _delete_tenant")
        # Act
        result = _delete_tenant(mock_request,
                                mock_request_log,
                                id=str(mock_tenant_data['_id']))

        # Assert
        self.assertEqual(result['status'], 'success')
        mock_find_one.assert_called_once()
        mock_mark_for_delete.assert_called_once()
        # mock_get_tenant_by_id.assert_called_once_with(
        #     str(mock_tenant_data['_id']))

    # @patch('src.auth.oidc.user_has_required_role', return_value=True)

# TODO - fix this unit test
#   for this test, also assert responses from backends, and assert the message
#   returned to client on successful run, then create at least one unit test
#   proving the correct message is returned to client on an unsuccessful run

#    @patch('src.clients.mongo.Tenant.find_one',
#           return_value=mock_tenant_data)
#    @patch('src.clients.actived.ADClient.cleanup_tenant_groups')
#    @patch('src.clients.git.GHClient.cleanup_repo_team')
#    @patch('src.clients.git.GHClient.cleanup_tenant_repo')
#    @patch('src.clients.mongo.Tenant.delete_by_tenant_id')
#    @patch('src.auth.oidc._get_auth_status_claims',
#           return_value=mock_user_claims)
#    @patch('src.clients.mongo.Role.has_role_user',
#           return_value=(True, 'owners'))
#    @patch('src.clients.pillars.get_instances_by_tenant',
#           return_value={'instances': []})
#    @patch.object(ADClient, 'get_service_principal_token',
#                  return_value='MOCK TOKEN')
#    def test_force_delete_tenant(self, mock_adc_init,
#                                 mock_get_instances_by_tenant,
#                                 mock_has_role,
#                                 mock_get_auth_claims,
#                                 mock_delete_by_tenant_id,
#                                 mock_cleanup_tenant_repo,
#                                 mock_cleanup_repo_team,
#                                 mock_cleanup_tenant_groups,
#                                 mock_find_one):
#        print("\n\nTest _force_delete_tenant")
#        # Act
#        result = _force_delete_tenant(mock_request,
#                                      mock_request_log,
#                                      id=str(mock_tenant_data['_id']))
#
#        # Assert
#        self.assertEqual(result['status'], 'success')
#        mock_find_one.assert_called_once()
#        mock_cleanup_tenant_groups.assert_called_once()
#        mock_cleanup_repo_team.assert_called_once()
#        mock_cleanup_tenant_repo.assert_called_once()
#        mock_delete_by_tenant_id.assert_called_once_with(
#                mock_tenant_data['_id'])

    def test_get_roles(self):
        print("\n\nTest _get_roles")
        # Act
        roles = _get_roles(mock_request, mock_request_log)

        # Assert
        self.assertEqual(roles['tenant']['read'],
                         ["owners", "ops",
                          "users", "collabs"])

# TODO - either migrate to auth or mongo module, or run in integration test
#    @patch('src.clients.mongo.Role.get_user_roles',
#           return_value={'64d3e74e7f28d3b29f7d8f36': 'owners'})
#    def test_get_user_roles(self, mock_get_user_roles):
#        print("\n\nTest _get_user_roles")
#        # Act
#        roles = _get_user_roles(mock_request, mock_request_log)
#
#        # Assert
#        self.assertEqual(roles, {'64d3e74e7f28d3b29f7d8f36': 'owners'})
#        mock_get_user_roles.assert_called_once_with(mock_request)

    @patch('src.clients.mongo.Role.find_one', return_value={
        '_id': ObjectId('64d3e74e7f28d3b29f7d8f36'),
        'tenant_id': '64d3e74e7f28d3b29f7d8f36',
        'owners': 'owners_id',
        'ops': 'ops_id',
        'users': 'users_id',
        'collabs': 'collabs_id'
    })
    @patch('src.clients.mongo.Role.delete_one')
    @patch('src.clients.mongo.ADGroup.delete_many')
    def test_delete_role_and_ad_groups(self,
                                       mock_delete_many,
                                       mock_delete_one,
                                       mock_find_one):
        print("\n\nTest _delete_role_and_ad_groups")
        # Act
        _delete_role_and_ad_groups('64d3e74e7f28d3b29f7d8f36')

        # Assert
        mock_find_one.assert_called_once_with(
            {"tenant_id": '64d3e74e7f28d3b29f7d8f36'})
        mock_delete_one.assert_called_once()
        mock_delete_many.assert_called_once_with(
            {"oid": {"$in": ['owners_id',
                             'ops_id',
                             'users_id',
                             'collabs_id']}})

    @patch('src.clients.git.GHClient.rename_tenant_repo', return_value=True)
    def test_rename_git_repo(self, mock_rename_tenant_repo):
        print("\n\nTest rename_git_repo")
        # Act
        result = rename_git_repo('old-repo', 'new-repo')

        # Assert
        self.assertTrue(result)
        mock_rename_tenant_repo.assert_called_once_with('old-repo', 'new-repo')

    @patch('src.clients.mongo.Tenant.update_one',
           return_value=MagicMock(modified_count=1))
    def test_rename_tenant_in_db(self, mock_update_one):
        print("\n\nTest rename_tenant_in_db")
        # Act
        result = rename_tenant_in_db('64d3e74e7f28d3b29f7d8f36',
                                     'New Tenant Name',
                                     'new-tenant-slug')

        # Assert
        self.assertTrue(result)
        mock_update_one.assert_called_once_with(
            {"_id": ObjectId('64d3e74e7f28d3b29f7d8f36')},
            {
                "name": 'New Tenant Name',
                "slug": 'new-tenant-slug',
                "git_repo": 'new-tenant-slug',
                "updated": ANY  # Correctly using ANY matcher without casting
            }
        )
