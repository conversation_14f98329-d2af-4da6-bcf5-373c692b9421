import logging
import os
import time
import unittest
from pymongo import mongo_client
from src.clients import mongo

logging.getLogger("pymongo").setLevel(logging.ERROR)


class TestMongoClient(unittest.TestCase):

    env = os.environ.get('PLATFORMENV', 'local')

    @classmethod
    def setUpClass(self):
        if self.env == 'local':
            print("\nStarting Mongo...\n")
            os.system("docker compose up init-ocpmongo ocpmongo > /dev/null &")
            time.sleep(10)

    def test_init(self):
        print("\n\nTest tenant_init")

        _tenant = mongo.Tenant()
        assert isinstance(_tenant._conn, mongo_client.MongoClient)

    def test_base_funcs(self):
        print("\n\nTest base_collection")

        _tenant = mongo.Tenant()
        assert _tenant
        try:
            _tenant.delete_many({'name': {'$regex': "Unit Test.*"}})
        except Exception:
            pass

        new_tenant = {
            "name": "Unit Test Tenant A", "description": "Unit Tests Tenant",
            "owner": [{"oid": "5e9d6613-37c1-444b-adda-50cb62d9675d", "name":
                       "AZ-Control Plane API - DEV-User", "description":
                       "AZ Group for App ID: 68f2009d-5e3e-4a4e-bc90-0a6da7"
                       + "cd9661 #added by adezen200 on Fri Jan 20 22:12:19"
                       + " UTC 2023 from WebSec Developer Portal"}],
            "created": 1676054946, "updated": 1676054946, "delete_ts": 0}
        _tenant.insert_one(new_tenant)

        res = _tenant.find_one({"name": "Unit Test Tenant A"})
        assert res
        self.assertEqual(res, new_tenant)

        _tenant.update_one({"name": "Unit Test Tenant A"},
                           {"name": "Unit Test Tenant X"})

        res = _tenant.find_one({"name": "Unit Test Tenant X"})
        self.assertEqual(res["name"], "Unit Test Tenant X")

        _tenant.delete_one({"name": "Unit Test Tenant X"})
        res = _tenant.find_one({"name": "Unit Test Tenant X"})
        assert not res

        new_tenants = [
            {"name": "Unit Test Tenant 1", "description": "Unit Tests Tenant",
             "owner": [{"oid": "5e9d6613-37c1-444b-adda-50cb62d9675d", "name":
                        "AZ-Control Plane API - DEV-User", "description":
                        "AZ Group for App ID: 68f2009d-5e3e-4a4e-bc"}],
             "created": 1676054946, "updated": 1676054946, "delete_ts": 0},
            {"name": "Unit Test Tenant 2", "description": "Unit Tests Tenant",
             "owner": [{"oid": "5e9d6613-37c1-444b-adda-50cb62d9675d", "name":
                        "AZ-Control Plane API - DEV-User", "description":
                        "AZ Group for App ID: 68f2009d-5e3e-4a4e-bc"}],
             "created": 1676054946, "updated": 1676054946, "delete_ts": 0},
            {"name": "Unit Test Tenant 3", "description": "Unit Tests Tenant",
             "owner": [{"oid": "5e9d6613-37c1-444b-adda-50cb62d9675d", "name":
                        "AZ-Control Plane API - DEV-User", "description":
                        "AZ Group for App ID: 68f2009d-5e3e-4a4e-bc"}],
             "created": 1676054946, "updated": 1676054946, "delete_ts": 0}]

        _tenant.insert_many(new_tenants)
        res = _tenant.find_many({'name': {'$regex': "Unit Test.*"}})
        assert res
        self.assertEqual(len(res), 3)

        _tenant.update_many({'name': {'$regex': "Unit Test.*"}},
                            {'delete_ts': 1676482438})

        res = _tenant.find_many({'name': {'$regex': "Unit Test.*"}})
        [self.assertEqual(res[x]['delete_ts'], 1676482438) for x in
         range(0, len(res) - 1)]

        _tenant.delete_many({'name': {'$regex': "Unit Test.*"}})
        res = _tenant.find_many({'name': {'$regex': "Unit Test.*"}})
        assert not res

    def test_tenant_class(self):
        print("\n\nTest tenant_class")

        _tenant = mongo.Tenant()
        try:
            _tenant.delete_many({'name': {'$regex': "Unit Test.*"}})
        except Exception:
            pass

        new_tenants = [
            {"name": "Unit Test Tenant B", "description": "Unit Tests Tenant",
             "owner": [{"oid": "5e9d6613-37c1-444b-adda-50cb62d9675d", "name":
                        "AZ-Control Plane API - DEV-User", "description":
                        "AZ Group for App ID: 68f2009d-5e3e-4a4e-bc"},
                       {"oid": "2b3aa523-37c1-444b-adda-50cb62d7e2ab", "name":
                        "AZ-CPAPI - STAGE-User", "description":
                        "AZ Group for App ID: 945ee2a9-4cc2-7217-a2"}],
             "created": 1676054000, "updated": 1676054000, "delete_ts": 0},
            {"name": "Unit Test Tenant C", "description": "Unit Tests Tenant",
             "owner": [{"oid": "8e523f11-37c1-444b-adda-50cb62d551da", "name":
                        "AZ-Pillar API - DEV-User", "description":
                        "AZ Group for App ID: 68f2009d-5e3e-4a4e-bc"},
                       {"oid": "aa3824f1-37c1-444b-adda-50cb010a23df", "name":
                        "AZ-ITRC - STAGE-User", "description":
                        "AZ Group for App ID: 2349aad2-2da1-7223-13"}],
             "created": 1676054000, "updated": 1676054000, "delete_ts": 0}]
        _tenant.insert_many(new_tenants)

        res = _tenant.get_tenant_by_name("Unit Test Tenant B")
        assert res
        self.assertEqual(res["name"], "Unit Test Tenant B")
        self.assertEqual(len(res["owner"]), 2)

        res = _tenant.get_tenant_by_id(str(res["_id"]))
        assert res
        self.assertEqual(res["name"], "Unit Test Tenant B")

        res = _tenant.get_tenants_by_ad_group_oids(
            ["2b3aa523-37c1-444b-adda-50cb62d7e2ab",
             "aa3824f1-37c1-444b-adda-50cb010a23df"])

        assert res
        self.assertEqual(len(res), 2)
        self.assertEqual(res[1]["name"], "Unit Test Tenant C")

        res = _tenant.get_tenants_by_name_regex("Unit Test.*")
        assert res
        self.assertEqual(len(res), 2)

        _tenant.mark_for_delete({"name": "Unit Test Tenant B"}, True)
        res = _tenant.get_tenant_by_name("Unit Test Tenant B")

        assert res["delete_ts"] > int(time.time()) - 100

        _tenant.delete_many({'name': {'$regex': "Unit Test.*"}})

    def test_adgroup_class(self):
        print("\n\nTest adgroup_class")
        _adgroup = mongo.ADGroup()
        try:
            _adgroup.delete_many({'name': {'$regex': "Unit Test.*"}})
        except Exception:
            pass

        new_adgroup = {
            "name": "Unit Test ADGroup", "description": "Unit Test AD Group",
            "oid": "2b3aa523-37c1-444b-adda-50cb62d7e2ab"
        }
        _adgroup.insert_one(new_adgroup)
        unit = _adgroup.find_one(new_adgroup)

        # Retrieve by OID
        res = _adgroup.get_ad_group_by_id(unit["oid"])
        # print(f"DEBUG - AD Group retrieved: {res}")
        assert res, f"Expected AD group to be found, but got {res}"

        res = _adgroup.get_ad_groups_by_name("Unit.*Test.*")
        # print(f"DEBUG - AD Groups found by name: {list(res)}")
        assert res, "Expected AD groups to be found"
        self.assertEqual(len(list(res)), 1)
        self.assertEqual(list(res)[0]["name"], "Unit Test ADGroup")

        _adgroup.delete_group_by_oid(res[0]["oid"])
        res = _adgroup.get_ad_group_by_id(res[0]["oid"])
        assert not res, "Expected AD group to be deleted"

    def test_role_class(self):
        print("\n\nTest role_class")

        _role = mongo.Role()
        try:
            _role.delete_many({'tenant_id': {'$regex': "Unit Test.*"}})
        except Exception:
            pass

        new_role = {
            "tenant_id": "Unit Test",
            "owners": "5e9d6613-37c1-444b-adda-50cb62d9675d",
            "ops": "2b3aa523-37c1-444b-adda-50cb62d7e2ab",
            "users": "8e523f11-37c1-444b-adda-50cb62d551da",
            "collabs": "aa3824f1-37c1-444b-adda-50cb010a23df"}
        _role.insert_one(new_role)

        # Retrieve roles by AD group OIDs
        res = _role.get_roles_by_adg_oids(['5e9d6613-37c1-444b-adda-50cb62d9675d'])
        # print(f"Roles by AD group OIDs: {res}")  # Debugging line
        assert res, f"Expected roles to be found, but got {res}"

        _role.delete_role_by_tenant_id(new_role["tenant_id"])
        res = _role.find_one({"tenant_id": new_role["tenant_id"]})
        assert not res, "Expected role to be deleted"

    @classmethod
    def tearDownClass(self):
        if self.env == 'local':
            print("\n\nStopping Mongo...")
            os.system(
                "docker compose down init-ocpmongo ocpmongo > /dev/null &")
            time.sleep(15)
            print("\n")
