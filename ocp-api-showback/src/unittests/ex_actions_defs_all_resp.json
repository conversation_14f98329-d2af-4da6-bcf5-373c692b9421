{"metrics": {"actions": [{"type": "View Log", "description": "View server log", "valid_roles": ["owner", "operator"], "valid_states": ["provisioning_completed", "cluster_restarting", "remapping_completed", "nominal"], "destructive": false}, {"type": "Update Metadata", "description": "Update metadata for Instance", "payload": {"fields": [{"label": "description", "value": "a new description"}, {"label": "name", "value": "a new name"}]}, "required_payload_fields": ["description", "name"], "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "cluster_restarting", "remapping_completed", "nominal"], "destructive": false}, {"type": "Restart VMAgent", "description": "Restart the VMAgent server", "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": false}, {"type": "<PERSON>art <PERSON>", "description": "Restart VMAuth server", "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": false}, {"type": "Delete Instance", "description": "Delete Instance", "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": true}]}, "logging": {"actions": [{"type": "View Log", "description": "View VMAuth log", "valid_roles": ["owner", "operator"], "valid_states": ["provisioning_completed", "cluster_restarting", "remapping_completed", "nominal"], "destructive": false}, {"type": "Update Metadata", "description": "Update metadata for Instance", "payload": {"fields": [{"label": "description", "value": "a new description"}, {"label": "name", "value": "a new name"}]}, "required_payload_fields": ["description", "name"], "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "cluster_restarting", "remapping_completed", "nominal"], "destructive": false}, {"type": "Restart Cluster", "description": "Restart the ES cluster", "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": false}, {"type": "<PERSON><PERSON>", "description": "Restart the Kibana server", "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": false}, {"type": "Delete Instance", "description": "Delete Instance", "valid_roles": ["owner"], "valid_states": ["provisioning_completed", "running"], "destructive": true}]}}