import logging
import typing as t

excluded_endpoints = ["/favicon.ico", "/api/v1/auth/callback"]

# TODO - filters not supported in fileConfig, convert to dictConfig
#      and rewrite here for reuse of single filter


class EndpointFilter_favicon(logging.Filter):
    def __init__(self, path: str, *args: t.Any, **kwargs: t.Any):
        super().__init__(*args, **kwargs)
        self._path = path

    def filter(self, record: logging.LogRecord) -> bool:
        return record.getMessage().find(path="/favicon.ico") == -1


class EndPointFilter_test1(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        print(f"\n\nDEBUG -   record: {record} \n\n")
        return record.getMessage().find("GET /favicon.ico HTTP") == -1


class EndPointFilter_test2(logging.Filter):
    def __init(self, excluded_endpoints: list[str] =
               excluded_endpoints) -> None:
        self.excluded_endpoints = excluded_endpoints

    def filter(self, record: logging.LogRecord) -> bool:
        return record.args and len(record.args) >= 3 and \
            record.args[2] not in self.excluded_endpoints


class EndPointFilter_a(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        # if "GET / HTTP" in record.msg:
        if "GET / HTTP" in record.getMessage():
            return False

        return True
