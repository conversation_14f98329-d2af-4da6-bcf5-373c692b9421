import logging
import json

excluded_endpoints = ["GET / HTTP", "GET /favicon.ico", "GET /metrics"]


class JSONHTTPHandler(logging.Handler):
    """
    A class which sends records to a web server, using either GET or
    POST semantics.

    ** adezen200: Modified version of HTTPHandler which emits records in JSON
                 format using structure defined by formatter
    """
    def __init__(self, host, url, method="GET", secure=False, credentials=None,
                 context=None):
        """
        Initialize the instance with the host, the request URL, and the method
        ("GET" or "POST")
        """
        logging.Handler.__init__(self)
        method = method.upper()
        if method not in ["GET", "POST"]:
            raise ValueError("method must be GET or POST")
        if not secure and context is not None:
            raise ValueError("context parameter only makes sense "
                             "with secure=True")
        self.host = host
        self.url = url
        self.method = method
        self.secure = secure
        self.credentials = credentials
        self.context = context

    def mapLogRecord(self, record):
        """
        Default implementation of mapping the log record into a dict
        that is sent as the CGI data. Overwrite in your class.
        Contributed by <PERSON>.
        """
        return record.__dict__

    def getConnection(self, host, secure):
        """
        get a HTTP[S]Connection.

        Override when a custom connection is required, for example if
        there is a proxy.
        """
        import http.client
        if secure:
            connection = http.client.HTTPSConnection(host,
                                                     context=self.context)
        else:
            connection = http.client.HTTPConnection(host)
        return connection

    def emit(self, record):
        """
        Emit a record.

        Send the record to the web server as a percent-encoded dictionary
        """
        try:
            import urllib.parse  # noqa: F401
            host = self.host
            h = self.getConnection(host, self.secure)
            url = self.url
            data = self.format(record)

            # Drop any messages matching excluded_endpoints, and transform
            # records on callback calls to exclude authcode
            drop = False
            if "GET" in data and "auth/callback?code" in data:
                djson = json.loads(data)
                djson['message'] = \
                    "GET /api/v1/auth/callback?... HTTP/1.1\" 302"
                data = json.dumps(djson)
            if any(x in data for x in excluded_endpoints):
                drop = True

            if not drop:
                if self.method == "GET":
                    if (url.find('?') >= 0):
                        sep = '&'
                    else:
                        sep = '?'
                    url = url + "%c%s" % (sep, data)
                h.putrequest(self.method, url)
                # support multiple hosts on one IP address...
                # need to strip optional :port from host, if present
                i = host.find(":")
                if i >= 0:
                    host = host[:i]
                # See issue #30904: putrequest call above already adds
                # this header on Python 3.x.
                # h.putheader("Host", host)
                if self.method == "POST":
                    h.putheader("Content-type",
                                "application/json")
                # "application/x-www-form-urlencoded")

                    h.putheader("Content-length", str(len(str(data))))
                if self.credentials:
                    import base64
                    s = ('%s:%s' % self.credentials).encode('utf-8')
                    s = 'Basic ' + base64.b64encode(s).strip().decode('ascii')
                    h.putheader('Authorization', s)
                h.endheaders()
                if self.method == "POST":
                    h.send(data.encode('utf-8'))
                h.getresponse()    # can't do anything with the result
        except Exception:
            self.handleError(record)


# class JSONHTTPHandler(logging.handlers.HTTPHandler):
#     def __init__(self, host, url, **kwargs):
#         print(f"kwargs: {**kwargs}")
#         super().__init__(host, url, **kwargs)
#
#     def emit(self, record):
#         """
#             Emit a log record
#             This handler emits records in JSON format
#         """
#         try:
#             data = json.dumps(self.format(record))
#             self.send(data)
#         except Exception:
#             self.handleError(record)
#
#     def send(self, record):
#         super().send(record)
