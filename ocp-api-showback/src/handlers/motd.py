import logging
import logging.config
import time

from bson import ObjectId
from pymongo.errors import PyMongoError
from typing import List

from fastapi import APIRouter, HTTPException, Body, Query, Request
from fastapi.security import APIKeyHeader

from src.auth import oidc as auth
from src.util.logging import get_request_log
from src.models.input import MOTDMessage, MOTDResponse
from src.clients.actived import ADClient
from src.clients.mongo import MOTD


logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

api_key_header = APIKeyHeader(name='authorization')

adc = ADClient()

motd_router = APIRouter(prefix="/api/v1/motd",
                        tags=["motd"],
                        responses={404: {"description": "Not found"}})


@motd_router.post("/create")  # , response_model=MOTDResponse)
async def create_motd(request: Request, motd: MOTDMessage):  # = Body(...)):
    '''
        Create new MOTD message to display in OCP UI
        For start and end time, enter epoch in seconds, i.e. 1714421661
    '''
    logger.info("Creating a new MOTD")
    request_log = await get_request_log(request)
    auth.validate_auth(request, request_log)
    if not auth.is_user_admin(request):
        raise HTTPException(status_code=403,
                            detail="Forbidden")

    try:
        motd_col = MOTD()
        motd_data = motd.dict(exclude_none=True)
        motd_data['created'] = int(time.time())
        motd_data['start_time'] = int(motd_data['start_time'])
        motd_data['end_time'] = int(motd_data['end_time'])

        inserted = motd_col.insert_one(motd_data)
        if not inserted.acknowledged:
            logger.error("MongoDB did not acknowledge the insert operation.")
            raise HTTPException(status_code=500,
                                detail="Failed to create MOTD in the "
                                       "database")

        # motd.id = str(inserted.inserted_id)

        res = motd_col.find_one({"_id": inserted.inserted_id})

        motd_resp = MOTDResponse(id=str(res['_id']),
                                 title=res['title'],
                                 type=res['type'],
                                 text=res['text'],
                                 buttons=res['buttons'] if 'buttons' in res
                                 else [],
                                 closeable=res['closeable'],
                                 start_time=res['start_time'],
                                 end_time=res['end_time'])

        return motd_resp
    except Exception as e:
        logger.error("Failed to create MOTD", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@motd_router.get("/read", response_model=List[MOTDResponse])
async def read_motd(request: Request,
                    show_expired: bool =
                    Query(False,
                          description="If true, show only expired "
                          "messages; otherwise, show only "
                          "active messages.")):
    logger.info("Fetching MOTD messages, show_expired: %s", show_expired)
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    # Read available for any user, no admin test
    auth.validate_auth(request, request_log)

    motd_col = MOTD()

    try:
        current_time = int(time.time())
        query = {}
        if show_expired:
            query = {"end_time": {"$lte": current_time}}
        else:
            query = {"end_time": {"$gt": current_time}}

        all_motds = motd_col.find_many(query)
        result_motds = []
        for motd_data in all_motds:
            motd_data['id'] = str(motd_data.pop('_id'))
            result_motds.append(MOTDResponse(**motd_data))
        return result_motds
    except Exception as e:
        logger.error(f"Failed to fetch MOTDs: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@motd_router.put("/update/{motd_id}", response_model=MOTDMessage)
async def update_motd(request: Request, motd_id: str,
                      motd_update: MOTDMessage = Body(...)):
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    logger.info("Updating MOTD")
    auth.validate_auth(request, request_log)
    if not auth.is_user_admin(request):
        raise HTTPException(status_code=403,
                            detail="Forbidden")

    try:
        motd_col = MOTD()

        # Construct update_data dict considering only fields explicitly
        # provided
        update_data = {}
        for key, value in motd_update.dict(exclude_unset=True).items():
            if value is not None:
                update_data[key] = value

        updated_motd = motd_col.update_one({"_id": ObjectId(motd_id)},
                                           update_data)

        if updated_motd.modified_count != 1:
            raise HTTPException(status_code=404, detail="No record found or "
                                                        "no update performed")

        res = motd_col.find_one({"_id": ObjectId(motd_id)})

        if res:
            motd_resp = MOTDResponse(id=str(res['_id']),
                                     title=res['title'],
                                     type=res['type'],
                                     text=res['text'],
                                     buttons=res['buttons'] if 'buttons' in res
                                     else [],
                                     closeable=res['closeable'],
                                     start_time=res['start_time'],
                                     end_time=res['end_time'])
            return motd_resp
        else:
            raise HTTPException(status_code=404,
                                detail="MOTD not found after update")
    except Exception as e:
        logger.error(f"MongoDB error during update: {e=}")
        raise HTTPException(status_code=500,
                            detail="MongoDB operation failed")


@motd_router.delete("/delete/{motd_id}")
async def delete_motd(request: Request, motd_id: str):
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    logger.info("Expiring MOTD (setting end_time to current time)")
    auth.validate_auth(request, request_log)
    if not auth.is_user_admin(request):
        raise HTTPException(status_code=403,
                            detail="Forbidden")

    try:
        current_time = int(time.time())
        motd_col = MOTD()
        update_result = motd_col.update_one(
            {"_id": ObjectId(motd_id)},
            {"end_time": current_time}
        )
        if update_result.matched_count == 0:
            raise HTTPException(status_code=404,
                                detail="MOTD not found")
        return {"status": "message expired", "id": motd_id}
    except PyMongoError:
        logger.error("MongoDB error during update for expiring message")
        raise HTTPException(status_code=500,
                            detail="Error expiring MOTD message")
