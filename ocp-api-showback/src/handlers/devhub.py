import logging

from fastapi import API<PERSON>outer, Request, HTTPException, Query, Depends
from fastapi.security import API<PERSON>eyHeader
from typing import List

from src.util.logging import get_request_log
from src.clients.devhub import get_devhub_applications
from src.auth.oidc import validate_auth
from src.models.input import DevhubAppID


logger = logging.getLogger(__name__)

api_key_header = APIKeyHeader(name='authorization')

devhub_router = APIRouter(prefix="/api/v1/devhub",
                          tags=["devhub"],
                          responses={404: {"description": "Not found"}})


def parse_comma_separated_ids(app_ids: str = Query(None, description="Comma-separated application "
                                                   "IDs to search for")
                              ) -> List[DevhubAppID]:
    if not app_ids:
        return []

    parsed_ids = []
    for app_id_str in app_ids.split(','):
        try:
            app_id = int(app_id_str)
        except ValueError:
            raise HTTPException(status_code=400,
                                detail=f"Invalid app ID: {app_id_str} is not "
                                       "an integer")

        # Check if app_id meets the 4 to 8 digits criteria
        if not 1000 <= app_id <= 99999999:
            raise HTTPException(status_code=400,
                                detail="Each ID in app_ids must be 4 to 8 "
                                f"digits long: '{app_id}'")

        parsed_ids.append(app_id)

    return parsed_ids


@devhub_router.get("/get_by_name")
async def devhub_by_name(
    request: Request,
    app_name: str = Query("*",
                          description="The name of the app to search for"),
    page_offset: int = Query(0, description="Pagination offset"),
    page_limit: int = Query(100, description="Pagination limit")
):
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    try:
        validate_auth(request, request_log)
    except Exception as e:
        logger.error(f"Authentication validation failed: {e}")
        raise HTTPException(status_code=401,
                            detail=f"Unauthorized: {e}")

    if not app_name:
        raise HTTPException(status_code=400,
                            detail="Bad Request: 'app_name' query parameter is required") # noqa E501

    try:
        logger.info(f"Fetching applications from DevHub by name: {app_name}")
        applications_data = get_devhub_applications(
            app_name=app_name,
            offset=page_offset,
            limit=page_limit
        )
        if not applications_data:
            raise HTTPException(status_code=404,
                                detail=f"No applications found with name: {app_name}") # noqa E501
        return applications_data
    except HTTPException as e:
        logger.error(f"Client error while fetching applications by name: {e.detail}") # noqa E501
        raise e
    except TimeoutError as e:
        logger.error(f"Timeout error while fetching applications by name: {e}")
        raise HTTPException(status_code=504,
                            detail="Gateway Timeout: The request took too long to complete") # noqa E501
    except Exception as e:
        logger.error(f"Error fetching applications by name: {e}")
        raise HTTPException(status_code=500,
                            detail=f"Internal Server Error: {e}")


@devhub_router.get("/get_by_ids")
async def devhub_by_ids(
    request: Request,
    app_ids: List[DevhubAppID] = Depends(parse_comma_separated_ids),
    page_offset: int = Query(0, description="Pagination offset"),
    page_limit: int = Query(100, description="Pagination limit")
):
    request_log = await get_request_log(request)
    logger.info(f"{request_log}")
    try:
        validate_auth(request, request_log)
    except Exception as e:
        logger.error(f"Authentication validation failed: {e}")
        raise HTTPException(status_code=401,
                            detail=f"Unauthorized: {e}")

    if not app_ids:
        raise HTTPException(status_code=400,
                            detail="Bad Request: 'app_ids' query parameter is required") # noqa E501

    try:
        logger.info(f"Fetching applications from DevHub by IDs: {app_ids}")
        applications_data = get_devhub_applications(
            app_name="",
            offset=page_offset,
            limit=page_limit,
            app_ids=app_ids
        )
        if not applications_data:
            raise HTTPException(status_code=404,
                                detail=f"No applications found with IDs: {app_ids}") # noqa E501
        return applications_data
    except HTTPException as e:
        logger.error(f"Client error while fetching applications by IDs: {e.detail}") # noqa E501
        raise e
    except TimeoutError as e:
        logger.error(f"Timeout error while fetching applications by IDs: {e}")
        raise HTTPException(status_code=504,
                            detail="Gateway Timeout: The request took too long to complete") # noqa E501
    except Exception as e:
        logger.error(f"Error fetching applications by IDs: {e}")
        raise HTTPException(status_code=500,
                            detail=f"Internal Server Error: {e}")
