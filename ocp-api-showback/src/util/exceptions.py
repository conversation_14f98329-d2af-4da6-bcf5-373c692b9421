import logging
import logging.config

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)


def parse_exception_args(e):
    '''
        Converts an exception to a dictionary object that can be
        accessed by keys
    '''
    errstr = str(e.args[0])
    fmsg = errstr.replace("'", '"').replace('"{', '').replace('}"', '')\
        .replace('""', '"').replace('\\', '').replace('msg":"', '')\
        .replace('https://', '')
    fmsg = fmsg[fmsg.find('{') + 1:fmsg.find('}')]

    # print(f"\nDEBUG - formatted string: |{fmsg}| \n\n")

    obj = {}
    lastkey = ''
    for a in fmsg.split(','):
        # print(f"DEBUG -      a: |{a}|  --  lastkey: {lastkey}  ")
        if a.find(':') < 0:
            obj[lastkey] = obj[lastkey] + str(a.strip())
            continue
        else:
            lastkey = str(a.split(':')[0].strip()).replace('"', '')
        obj[str(a.split(':')[0].strip()).replace('"', '')] = \
            str(a.split(':')[1].strip()).replace('"', '')

    if 'status' in obj.keys():
        obj['status'] = int(obj['status'])

    return obj
