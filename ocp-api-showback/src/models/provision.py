import typing
from typing import Optional, Union
from enum import Enum
from pydantic import BaseModel, validator, conint
from src.validation.input_validator import validate_input


class DevHubLimit(str, Enum):
    SINGLE = "single"
    MULTIPLE = "multiple"


class Conditional(BaseModel):
    for_field: str              # Name of field to test against - IF value
    value: str                  # matches field value, Conditional is TRUE


class Instructions(BaseModel):
    type: str                   # paragraph, list, numbered
    values: typing.List[str]    # "1) Select ...", "2) Enter ..."


# class Label(BaseModel):
#     type: str = "label"
#     field_name: str
#     header: bool
#     conditional: Optional[Conditional]


class TextInput(BaseModel):
    type: str = "text"
    name: str
    label: str
    description: Optional[Instructions]
    # default_text: Union[None, str]
    required: bool
    conditional: Optional[Conditional]


class SelectorOption(BaseModel):
    label: str
    value: str


class DevHubList(BaseModel):
    type: str                   # 'devhub_ids'
    name: str
    limit: DevHubLimit                # 'single' or 'multiple'


class Selector(BaseModel):
    type: str                   # 'select' or 'checkbox'
    name: str
    label: str
    description: Optional[Instructions]
    options: Optional[typing.List[SelectorOption]]
    conditional: Optional[Conditional]


class ProvisionForm(BaseModel):
    '''
        Instance Provisioning Form may contain any number of inputs.
          Each input may optionally include a description, defined by a
         Instructions object, or a Conditional field, which will determine
         whether or not an input is displayed on the form based on the
         current value of another input field ("WHEN dependent_field ==
         'value' THEN display input")
    '''
    # title: str
    pillar: str
    instructions: Optional[Instructions]
    inputs: typing.List[Union[TextInput, Selector, DevHubList]]


class form_input(BaseModel):
    name: str
    value: str
    _validate_ = validator('name', 'value',
                           allow_reuse=True)(validate_input)


DevhubAppID = conint(ge=1000, le=999999)


class provision_request(BaseModel):
    '''
        This model represents the form filled out by user on Provision
        Instance page.  The 'fields' field represents all inputs defined
        by Pillar in their form for provisioning an Instance.
    '''
    tenant_id: str
    devhub_ids: typing.List[DevhubAppID]
    fields: typing.List[form_input]
    metadata: Optional[typing.List[form_input]]
    _validate_ = validator('tenant_id', 'fields',
                           allow_reuse=True)(validate_input)


class provision_request_pillar(BaseModel):
    '''
        This model represents the request sent to Pillar APIs to
        provision a new Instance
    '''
    # pillar: str
    instance_name: str
    tenant_id: str
    tenant_name: str
    git_repo: str
    devhub_ids: typing.List[DevhubAppID]
    ad_groups: Optional[typing.Dict[str, str]]
    fields: typing.List[form_input]
