# import datetime
import re
# import time
from typing import Optional, List, Union
from pydantic import BaseModel, validator, conint  # , Field

from fastapi import HTTPException
from src.validation.input_validator import validate_input, validate_tenant_apps, \
    validate_devhub_apps, validate_link

# Define a constrained integer type for the devhubapps IDs
DevhubAppID = conint(ge=1000, le=999999)
# TODO - migrate all validation to validator module


class ad_group(BaseModel):
    oid: str
    name: str
    description: str
    _validate_ = validator('oid', 'name', 'description',
                           allow_reuse=True)(validate_input)


class create_tenant_request(BaseModel):
    name: str
    description: Optional[str] = None
    tenant_apps: List[DevhubAppID]
    # owner: List[ad_group]

    _validate_ = validator('name', 'description',
                           allow_reuse=True)(validate_input)
    _validate_ = validator('tenant_apps',
                           allow_reuse=True)(validate_tenant_apps)


class update_tenant_request(BaseModel):
    tenant_id: Optional[str] = None
    tenant_name: Optional[str] = None
    update_description: Optional[str] = None
    update_tenant_apps: Optional[List[DevhubAppID]] = None
    # update_name: Optional[str] = None
    # update_owner: Optional[List[ad_group]] = None

    _validate_ = validator('tenant_id',
                           'tenant_name',
                           'update_description',
                           # 'update_owner',
                           allow_reuse=True)(validate_input)
    _validate_ = validator('update_tenant_apps',
                           allow_reuse=True)(validate_tenant_apps)


class add_instances_request(BaseModel):
    tenant_id: str = None
    instance_ids: List[str] = None
    _validate_ = validator('tenant_id', 'instance_ids',
                           allow_reuse=True)(validate_input)


class Input(BaseModel):
    type: str = "text"
    label: str = "DisplayName"
    name: str = "InputName"
    _validate_ = validator('type', 'label', 'name',
                           allow_reuse=True)(validate_input)


# TODO - if DevHubApps and MOTDResponse are not input expected by user, then
#  migrate to separate model class


class DevHubApps(BaseModel):
    app_name: Optional[str] = "*"
    app_ids: Optional[List[DevhubAppID]] = None
    page_offset: int = 0
    page_limit: int = 100
    _validate_ = validator('app_name', 'app_ids',
                           allow_reuse=True)(validate_devhub_apps)


class MOTDButton(BaseModel):
    label: str = "MOTD label. Max 50 characters."
    link: str = "Absolute or relative URL i.e. /support or https://example.com"
    icon: Optional[str] = "link"
    _validate_ = validator('label', 'icon',
                           allow_reuse=True)(validate_input)

    @validator('link')
    def validate_link(cls, v):
        return validate_link(v)


class MOTDResponse(BaseModel):
    id: str
    title: str
    type: str
    text: str
    # buttons: List[Dict[str, str]]
    buttons: List[MOTDButton]
    closeable: bool
    start_time: int
    end_time: int


# NOW_FACTORY = lambda: int(time.time())

# def get_current_epoch() -> int:
#     return int(time.time())
#     #return int(datetime.datetime.now().timestamp())

# def get_dt_plus_day() -> datetime.datetime:
#     return datetime.datetime.utcnow()+datetime.timedelta(days=1)


class MOTDMessage(BaseModel):
    title: str = "MOTD Message Title. Max 50 characters."
    type: str = "Type can be one of: info, warning, alert or inprogress"
    text: str = "MOTD Message goes here. Max 120 characters."
    buttons: Optional[List[MOTDButton]]
    closeable: bool = True
    start_time: int = 0
    end_time: int = 0
    _validate_ = validator('closeable',
                           allow_reuse=True)(validate_input)

    # TODO - find working method to pass default values for start/end time,
    #   we seem to have an issue with default_factory in Field class, this
    #   does not work with int values, and unable to modify a datetime value

    # start_time: datetime.datetime = \
    #   Field(default_factory=datetime.datetime.utcnow())
    # end_time: datetime.datetime = \
    #   Field(default_factory=get_dt_plus_day)
    # end_time: datetime.datetime = \
    #   Field(default_factory=datetime.datetime.utcnow()+datetime.timedelta(days=1))
    # start_time: int = Field(default_factory=NOW_FACTORY)
    # end_time: int = Field(default_factory=NOW_FACTORY)
    # start_time: int = Field(default_factory=get_current_epoch)
    # end_time: int = Field(default_factory=get_current_epoch)
    # start_time: int = \
    #   Field(default_factory=lambda: int(datetime.datetime.now().timestamp()))
    # end_time: int = \
    #   Field(default_factory=lambda:
    #   int(datetime.datetime.now().timestamp()) + 86400)

    @validator('end_time')
    def validate_endtime(cls, end_time, values):
        if 'start_time' in values and \
                int(end_time) <= int(values['start_time']):
            raise ValueError('end_time must be after start_time')
        if not re.search(r'^(0|\d{10})$', str(end_time)):
            raise HTTPException(status_code=400,
                                detail="Value should be valid epoch timestamp"
                                       " in seconds")
        if int(end_time) > 0:
            return end_time

    @validator('start_time')
    def validate_starttime(cls, start_time, values):
        if not re.search(r'^(0|\d{10})$', str(start_time)):
            raise HTTPException(status_code=400,
                                detail="Value should be valid epoch timestamp"
                                       " in seconds")
        if int(start_time) > 0:
            return start_time

    @validator('title', 'text', each_item=True)
    def check_text(cls, v):
        if not v.strip():
            raise ValueError("Text must not be empty.")
        if len(v) > 500:
            raise ValueError("Text must not exceed 500 characters.")
        # Allows letters, numbers, spaces, and common punctuation
        if not re.match(r"^[a-zA-Z0-9 .,!?'-]+$", v):
            raise ValueError("Text contains invalid characters.")
        return v

    @validator('type')
    def check_type(cls, v):
        valid_types = ['info', 'warning', 'alert', 'delete', 'inprogress']
        if v not in valid_types:
            raise ValueError("Invalid type for MOTD. Valid types are: info, "
                             "warning, alert, delete, inprogress")
        return v

    # TODO - migrate all validation to validator module


class KV(BaseModel):
    label: str
    value: Union[str, int, float]
    _validate_ = validator('label', 'value')


class Payload(BaseModel):
    fields: List[KV]


class ActionRequest(BaseModel):
    pillar: str
    instance_id: str
    tenant_id: str
    action_type: str                           # FK to Action
    payload: Optional[Payload]
    _validate_ = validator('pillar', 'instance_id', 'tenant_id', 'type',
                           'user')
