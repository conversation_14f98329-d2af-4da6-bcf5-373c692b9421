from typing import Optional, List, Union
from enum import Enum
from pydantic import BaseModel


class Role(str, Enum):
    OWNERS = "owners"
    OPERATORS = "ops"
    USERS = "users"
    COLLABORATORS = "collabs"


class KV(BaseModel):
    label: str
    value: Union[str, int, float]


class Payload(BaseModel):
    fields: List[KV]


class InputType(str, Enum):
    SHORTTEXT = "shorttext"
    LONGTEXT = "longtext"
    DROPDOWN = "dropdown"
    RADIO = "radio"
    MULTISELECT = "multiselect"


class Conditional(BaseModel):
    for_field: str
    value: str


class InputElement(BaseModel):
    label: str
    type: InputType
    values: Optional[List[Union[str, int, float]]]


class InputRow(BaseModel):
    input: InputElement
    required: bool
    conditional: Optional[Conditional]


class ActionInterface(BaseModel):
    title: str
    instructions: str
    input_rows: Optional[List[InputRow]]
    action_type: str                    # FK to Action definition


class ActionInterfaces(BaseModel):
    interfaces: List[ActionInterface]


class Action(BaseModel):
    type: str
    description: str
    payload: Optional[Payload]
    required_payload_fields: Optional[List[str]]
    valid_roles: List[Role]
    valid_states: List[str]
    destructive: bool


class Actions(BaseModel):
    actions: List[Action]


class PillarActionRequest(BaseModel):
    instance_id: str
    tenant_id: str
    action_type: str                       # FK to Action
    payload: Optional[Payload]
    user: str                       # ntid (OIDC) or client_id (OAuth)
