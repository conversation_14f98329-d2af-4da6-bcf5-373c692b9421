import typing
from typing import Optional, Union
from pydantic import BaseModel, validator
from enum import Enum
from src.models.entities import Instance
from src.validation.input_validator import validate_input


class KV(BaseModel):
    label: str
    value: str
    _validate_ = validator('label', 'value',
                           allow_reuse=True)(validate_input)


class StatusIndicatorThreshold(BaseModel):
    threshold_label: str        # 'nominal', 'degraded', 'hazcon'
    value_range: str            # '1.5 >= <6.0'
    _validate_ = validator('threshold_label', 'value_range',
                           allow_reuse=True)(validate_input)


class IndicatorType(str, Enum):
    COUNTER = "counter"
    GAUGE = "gauge"
    DOT = "dot"
    TEXT = "text"


class StatusIndicator(BaseModel):
    label: str
    type: IndicatorType
    value: float
    thresholds: Optional[typing.List[StatusIndicatorThreshold]]
#     size: str = '1x1'           # default '1x1'
    _validate_ = validator('label', 'value',
                           allow_reuse=True)(validate_input)


class StackResource(BaseModel):
    title: str                  # SR may represent a link to a resource
    description: Optional[str]  # or other metadata defining a resource,
    link: Optional[str]         # (link OR key/value)
    resource_kv: Optional[typing.List[KV]]
    status: Optional[StatusIndicator]

    # TODO - discuss with pillars, this data is managed by pillars
    @validator('status', pre=True, always=True)
    def set_status_to_none_if_empty(cls, v):
        if isinstance(v, dict) and not v:
            return None
        return v

    _validate_ = validator('title', 'description',
                           allow_reuse=True)(validate_input)


class Info(BaseModel):
    instance: Instance
    stack_title: Optional[str]
    stack: Optional[typing.List[StackResource]]
    # status_title: Optional[str]
    # short_status: Optional[typing.List[StatusIndicator]]
    _validate_ = validator('stack_title',
                           allow_reuse=True)(validate_input)


# class PanelLayout(BaseModel):
#     size: str                   # '1x1', '3x1', '2x2', '4x3'
#     affinity: str               # 'left', 'right', 'center'


class SeriesLabel(KV):
    pass


class DataPoint(BaseModel):
    timestamp: int              # Epoch in seconds
    value: float
    _validate_ = validator('timestamp', 'value',
                           allow_reuse=True)(validate_input)


class TSThreshold(KV):          # TSThreshold may be utilized to represent
    # key: str                  # a SLA or otherwise a threshold for a KPI
    value: float                # in a TSChart
    color: str
    _validate_ = validator('value', 'color',
                           allow_reuse=True)(validate_input)


class TimeSeries(BaseModel):
    metric: str
    unit: str                   # 'MB', 'ms', 'calls/sec', 'errors/min'
    labels: typing.List[SeriesLabel]
    data_points: typing.List[DataPoint]
    _validate_ = validator('metric', 'unit',
                           allow_reuse=True)(validate_input)


class TSChartType(str, Enum):
    LINECHART = "linechart"
    SPARKLINE = "sparkline"


class TSChart(BaseModel):
    title: str
    type: TSChartType
    series: typing.List[TimeSeries]
    thresholds: Optional[typing.List[TSThreshold]]
#     size: str                   # '1x1', '3x1', '2x2', '4x3'
    _validate_ = validator('title',
                           allow_reuse=True)(validate_input)


class Panel(BaseModel):
    title: str
    description: Optional[str]
#     layout: PanelLayout
    _validate_ = validator('title', 'description',
                           allow_reuse=True)(validate_input)


class MetricsPanel(Panel):
    # title: str; description; layout: PanelLayout
    visualizations: typing.List[Union[TSChart, StatusIndicator]]


class Metrics(BaseModel):
    title: str
    description: Optional[str]
    panels: typing.List[MetricsPanel]
    start_ts: int               # Epoch in seconds
    end_ts: int                 # Epoch in seconds
    range: Optional[str]        # '15m', '1h', '1d'
    _validate_ = validator('title', 'description', 'start_ts', 'end_ts',
                           'range',
                           allow_reuse=True)(validate_input)


class CostLI(BaseModel):
    resource_label: str
    cost: float
    _validate_ = validator('resource_label', 'cost',
                           allow_reuse=True)(validate_input)


class CostsPanel(Panel):
    # title: str; description: Optional[str]; layout: PanelLayout
    costs: typing.List[CostLI]
    type: str = "statement"
    currency: str               # 'dollars'
    total_cost: float          # Not input, calculated field
    _validate_ = validator('type', 'currency', 'total_cost',
                           allow_reuse=True)(validate_input)


class UtilLI(BaseModel):
    resource_label: str
    visualization: Union[TSChart, StatusIndicator]
    _validate_ = validator('resource_label',
                           allow_reuse=True)(validate_input)


class UtilPanel(Panel):
    # title: str; description: Optional[str]; layout: PanelLayout
    type: str = "utilization"
    visualizations: typing.List[UtilLI]
    _validate_ = validator('type',
                           allow_reuse=True)(validate_input)


class Showback(BaseModel):
    title: str
    description: str
    panels: typing.List[Union[CostsPanel, UtilPanel]]
    # costs: typing.List[CostsPanel]
    # utilizations: typing.List[UtilPanel]
    start_ts: int                   # Epoch in seconds
    end_ts: int                     # Epoch in seconds
    range: Optional[str]            # '15m', '1h', '1d'
    _validate_ = validator('title', 'description', 'start_ts', 'end_ts',
                           'range',
                           allow_reuse=True)(validate_input)


# class StatusPanel(Panel):
#     # title: str; description: Optional[str]; layout: PanelLayout
#     vizualizations: Union(TSChart, StatusIndicator)
#     start_ts: int
#     end_ts: int
#     range: Optional[str]


# class Status(BaseModel):
#     caption: str
#     panels: typing.List(StatusPanel)


# class Action(BaseModel):        # Define dynamic list of fields/inputs to
#     label: str                  # represent a modal / popup
#     caption: str
#     endpoint: str               # API endpoint + paramters to execute Action


# class ActionsPanel(Panel):
#     # title: str; description: Optional[str]; layout: PanelLayout
#     actions: typing.List(Action)


# class Actions(BaseModel):
#     description: str
#     panels: typing.List(ActionsPanel)


class InstanceDetails(BaseModel):
    info: Info
    metrics: Metrics
    showback: Showback
#     status: Status
#     actions: Actions
