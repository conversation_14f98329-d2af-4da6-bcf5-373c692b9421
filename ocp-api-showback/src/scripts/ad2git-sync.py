import logging
import logging.config
import time

import sys
sys.path.extend("./src")

from src.clients import mongo  # noqa: E402
from src.clients.actived import ADClient  # noqa: E402
from src.clients.git import GHClient  # noqa: E402

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

""" The AD2Git Sync process works off records stored in
    OCPDB (definitions of Tenants and relationships to
    AD groups defining membership), pulls membership in
    those AD groups and in the teams assigned to the Git
    repositories belonging to each Tenant, compares
    membership, and updates membership in those Git teams
    to 'sync' them with the authoratative record (the AD
    groups)
"""

logger.info("OCP AD to Git sync starting...")

# 1) Pull list of Tenants and Owner group records
logger.info("Querying Tenant/Role records...")
_tenant = mongo.Tenant()

# Retrieve all tenant records, and aggregate
# each w corresponding role record:

tenants_roles = \
    _tenant.collection.\
    aggregate([{"$addFields":
                {"tid":
                 {"$toString": "$_id"}}},
               {"$lookup":
                {"from": "role", "localField": "tid",
                 "foreignField": "tenant_id", "as": "roles"}}])

# 2) Query AD and pull list of members per group
logger.info("Querying AD for Owners groups membership...")
adc = ADClient()
adg_members = {}
ad_oid_to_tenant_slug = {}
for t in tenants_roles:
    time.sleep(.5)
    logger.info(f"Pulling owner membership for Tenant '{t['name']}'...")
    if len(t['roles']) < 1:
        logger.error("No AD Groups found for Tenant, "
                     + f"'{str(t['_id'])}': '{t['name']}'")
        continue
    resp = None
    try:
        resp = adc.get_ad_group_members(t['roles'][0]['owners'])
        adg_members[t['roles'][0]['owners']] = resp
    except Exception as e:
        logger.error("Failed to retrieve membership for AD group, "
                     + f"{t['roles'][0]['owners']} - '{t['name']} - Owners':"
                     + f" {e=}")
        continue

    # Only continue sync for this tenant if we find AD group
    ad_oid_to_tenant_slug[t['roles'][0]['owners']] = t['slug']

# 3) Query GHEC and pull list of members per team
logger.info("Querying Git for repo team membership...")
ghc = GHClient()
ghteam_members = {}
for oid in adg_members.keys():
    logger.info("Pulling membership in repo team for Tenant "
                + f"'{ad_oid_to_tenant_slug[oid]}'...")
    time.sleep(8)
    try:
        resp = ghc.get_repo_team_members(ad_oid_to_tenant_slug[oid])
        ghteam_members[oid] = resp
    except Exception as e:
        logger.error("Failed to retrieve membership for repo team, "
                     + f"{ad_oid_to_tenant_slug[oid]}_owners: {e=}")
        ghteam_members[oid] = []
        continue

# 4) Compare membership, AD groups are authoratative
logger.info("Comparing membership between AD groups and repo teams...")
gh_users_to_add = {}
gh_users_to_remove = {}
time.sleep(1)

for oid in adg_members.keys():
    logger.info("Comparing Owner group membership to repo team membership "
                + f"for Tenant '{ad_oid_to_tenant_slug[oid]}'...")
    gh_set = set()
    gh_set |= set([x.split("_")[0] for x in ghteam_members[oid]])

    ad_set = set()
    ad_set |= set(adg_members[oid])

    if gh_set != ad_set:
        gh_users_to_add[oid] = ad_set.difference(gh_set)
        gh_users_to_remove[oid] = gh_set.difference(ad_set)
        logger.info(f"Found diff for '{ad_oid_to_tenant_slug[oid]}'- "
                    + f"add to repo team: {gh_users_to_add[oid]}, "
                    + f"remove from repo team: {gh_users_to_remove[oid]}")

# 5) Step through add/remove dicts and issue calls to update membership
# in Git repo teams - run sequential in single thread (avoid rate limiting)
logger.info("Adding new memberships to repo teams...")
for oid in gh_users_to_add.keys():
    if len(gh_users_to_add[oid]) > 0:
        logger.info("Adding new members to repo team for Tenant "
                    + f"'{ad_oid_to_tenant_slug[oid]}': "
                    + f"{list(gh_users_to_add[oid])}'...")
        time.sleep(2)
        try:
            ghc.add_members_to_repo_team(ad_oid_to_tenant_slug[oid],
                                         list(gh_users_to_add[oid]))
        except Exception as e:
            logger.error("Failed to add new members, "
                         + f"{list(gh_users_to_add[oid])} to repo team "
                         + f"'{ad_oid_to_tenant_slug[oid]}': {e=}")
    time.sleep(6)

logger.info("Removing old memberships from repo teams...")
for oid in gh_users_to_remove.keys():
    if len(gh_users_to_remove[oid]) > 0:
        logger.info("Removing old members from repo team for Tenant "
                    + f"'{ad_oid_to_tenant_slug[oid]}': "
                    + f"{list(gh_users_to_remove[oid])}'...")
        time.sleep(2)
        try:
            ghc.remove_members_from_repo_team(ad_oid_to_tenant_slug[oid],
                                              list(gh_users_to_remove[oid]))
        except Exception as e:
            logger.error("Failed to remove old members, "
                         + f"{list(gh_users_to_remove[oid])}, from repo team "
                         + f"'{ad_oid_to_tenant_slug[oid]}': {e=}")
    time.sleep(6)

logger.info("AD to Git sync complete")
