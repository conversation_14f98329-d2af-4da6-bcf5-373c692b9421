#!/usr/bin/env python
import os
import subprocess


def load_env_file(env_file='.env'):
    if not os.path.exists(env_file):
        raise FileNotFoundError(f"Error: {env_file} not found. \
            Cannot proceed without this file as it contains \
            necessary credentials.")

    with open(env_file, 'r') as file:
        for line in file:
            if '=' in line:
                key, val = line.strip().split('=', 1)
                os.environ[key] = val
                print(f"export {key}={val}")


def check_required_env_vars(required_vars):
    missing_vars = [var for var in required_vars if os.getenv(var) is None]
    if missing_vars:
        raise EnvironmentError(f"Missing required environment \
            variables: {', '.join(missing_vars)}")


def export_secrets(secrets):
    secrets_list = secrets.split('|')
    for item in secrets_list:
        if '=' in item:
            key, val = item.split('=', 1)
            os.environ[key] = val
            print(f"export {key}={val}")


def main():
    ocp_env = os.getenv('PLATFORMENV')

    if ocp_env not in ['rdei', 'docker']:
        # Load environment variables from the .env file
        load_env_file()

    # Ensure that required Vault credentials are loaded from the environment
    check_required_env_vars(['VAULT_ROLE_ID', 'VAULT_SECRET_ID'])

    # Execute Vault commands only after loading .env variables
    if ocp_env not in ['rdei', 'docker']:
        secrets = subprocess.check_output(
            [
                'poetry',
                'run',
                'python',
                '-c',
                'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)' # noqa E501
            ],
            universal_newlines=True
        ).strip()
    else:
        secrets = subprocess.check_output(
            ['python', '-c', 'from src.clients.vault import VaultClient; v=VaultClient(); v.read_secrets(log=False,p=True)'], # noqa E501
            universal_newlines=True
        ).strip()

    export_secrets(secrets)


if __name__ == '__main__':
    try:
        main()
    except (FileNotFoundError, EnvironmentError) as e:
        print(str(e))
        exit(1)
