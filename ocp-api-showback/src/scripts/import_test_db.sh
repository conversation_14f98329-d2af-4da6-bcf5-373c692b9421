#!/bin/bash
set -e

echo "Starting Dev DB Import..."
echo "Exporting secrets..."
. ./export_env.sh

echo "Pull dump from OCP Test DB..."
mongodump --uri="mongodb://${MONGO_APP_USER}:${MONGO_APP_PASS}@test.ocp.comcast.net/cpdb?authSource=cpdb&tls=true&tlsInsecure=true" --out=/var/tmp/backup --db=cpdb

echo "Restore Test DB dump to local ocpmongo..."
mongorestore --uri="mongodb://${MONGO_APP_USER}:${MONGO_APP_PASS}@localhost:27018/cpdb?authSource=cpdb&tls=true&tlsInsecure=true" /var/tmp/backup/cpdb/

echo "Local MongoDB CPDB database restored from Test MongoDB"
