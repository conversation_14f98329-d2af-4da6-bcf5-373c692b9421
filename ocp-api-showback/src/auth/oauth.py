import configparser
import jwt
import logging
import logging.config
import os
import time

from jwt import PyJWKClient
from src.clients.retry_adapter import RetryAdapter

retry_adapter = RetryAdapter()

config = configparser.ConfigParser()
config.read('config/auth.ini')

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

oauth_jwks = os.environ.get('oauth_jwks_url')
oauth_iss_stage = os.environ.get('oauth_iss_stage')
oauth_iss_prod = os.environ.get('oauth_iss_prod')
oauth_iss_url_stage = os.environ.get('oauth_iss_url_stage')
oauth_iss_url_prod = os.environ.get('oauth_iss_url_prod')

# Showback specific oauth settings
client_id_metrix_showback = os.environ.get("oauth_client_id_metrix_showback")
client_secret_metrix_showback = os.environ.get("oauth_client_secret_metrix_showback")
client_id_visualization_showback = os.environ.get("oauth_client_id_visualization_showback")
client_secret_visualization_showback = os.environ.get("oauth_client_secret_visualization_showback")
client_scope_showback = os.environ.get("oauth_client_scope_showback")

# TODO - rework oauth setup, devhub requires staging client in
# lower environments, metrics/logging apis require prod client
# in all environments

client_id = os.environ.get('oauth_client_id_metrics')  # ocp prod client
client_secret = os.environ.get('oauth_client_secret_metrics')  # ocp-prod
client_scope_metrics = os.environ.get('oauth_client_scope_metrics')
client_scope_logging = os.environ.get('oauth_client_scope_logging')
client_scope_visualization = os.environ.get('oauth_client_scope_visualization')
client_scope_devhub = os.environ.get('oauth_client_scope_devhub')
client_secret_stage = os.environ.get('oauth_client_secret_devhub')
ocp_scope = os.environ.get('oauth_service_scope_user')
deploy_env = os.environ.get('DEPLOYENV')
env = os.environ.get('PLATFORMENV', 'local')
if env == 'local' or env == 'docker':
    ocp_proxy = None
else:
    ocp_proxy = os.environ.get('API_PROXY')

# Only set proxies if ocp_proxy is defined
proxies = {'https': ocp_proxy} if ocp_proxy else {}


def get_metrics_oauth_token():
    try:
        res = retry_adapter.post(oauth_iss_url_prod + "/v2/ws/token.oauth2",
                                 data={'client_id': client_id,
                                       'client_secret': client_secret,
                                       'grant_type': 'client_credentials',
                                       'scope': client_scope_metrics})
    except Exception as e:
        msg = "Failed to retrieve OAuth token for metricsapi"
        logger.exception(msg + f": {e=}")
        raise Exception({"message": msg})
    return res.json()


def get_logging_oauth_token():
    try:
        res = retry_adapter.post(oauth_iss_url_prod + "/v2/ws/token.oauth2",
                                 data={'client_id': client_id,
                                       'client_secret': client_secret,
                                       'grant_type': 'client_credentials',
                                       'scope': client_scope_logging})
    except Exception as e:
        msg = "Failed to retrieve OAuth token for Logging API"
        logger.exception(msg + f": {e=}")
        raise Exception({"message": msg})
    return res.json()


def get_viz_oauth_token():
    try:
        res = retry_adapter.post(oauth_iss_url_prod + "/v2/ws/token.oauth2",
                                 data={'client_id': client_id,
                                       'client_secret': client_secret,
                                       'grant_type': 'client_credentials',
                                       'scope': client_scope_visualization})
    except Exception as e:
        msg = "Failed to retrieve OAuth token for visualizationapi"
        logger.exception(msg + f": {e=}")
        raise Exception({"message": msg})
    return res.json()


def get_devhub_oauth_token():
    try:
        if deploy_env in ['prod', 'test']:
            url = oauth_iss_url_prod + "/v2/oauth/token"
        else:
            url = oauth_iss_url_stage + "/v2/oauth/token"
        headers = {
            'Content-Type': 'application/json',
            'X-Client-Id': client_id,
            'X-Client-Secret': client_secret_stage,
            'capabilities': client_scope_devhub
        }
        # logger.debug(f"DevHub OAuth headers: {headers}")
        response = retry_adapter.post(url, headers=headers)
    except Exception as e:
        msg = "Failed to retrieve OAuth token for DevHub API"
        logger.exception(msg + f": {e=}")
        raise Exception({"message": msg})
    return response.json()


def get_metrix_showback_oauth_token():
    try:
        res = retry_adapter.post(
            oauth_iss_url_prod + "/v2/ws/token.oauth2",
            data={
                "client_id": client_id_metrix_showback,
                "client_secret": client_secret_metrix_showback,
                "grant_type": "client_credentials",
                "scope": client_scope_showback,
            },
        )
    except Exception as e:
        msg = "Failed to retrieve OAuth token for metricsapi"
        logger.exception(msg + f": {e=}")
        raise Exception({"message": msg})
    return res.json()


def get_visualization_showback_oauth_token():
    try:
        res = retry_adapter.post(
            oauth_iss_url_prod + "/v2/ws/token.oauth2",
            data={
                "client_id": client_id_visualization_showback,
                "client_secret": client_secret_visualization_showback,
                "grant_type": "client_credentials",
                "scope": client_scope_showback,
            },
        )
    except Exception as e:
        msg = "Failed to retrieve OAuth token for metricsapi"
        logger.exception(msg + f": {e=}")
        raise Exception({"message": msg})
    return res.json()


def validate_bearer_token(auth_header):
    try:
        bearer_token = ''
        if 'Bearer' not in auth_header[:6]:
            return False
        else:
            bearer_token = auth_header[7:]

        iss = None
        if deploy_env != 'prod':
            iss = oauth_iss_stage
        else:
            iss = oauth_iss_prod

        logger.debug("ISS: {iss}")

        # print(f"\n\nDEBUG - validate bearer - iss: {iss}, "
        #       f"deployenv: {deploy_env} \n\n")

        if ocp_proxy:
            os.environ['http_proxy'] = ocp_proxy
            os.environ['https_proxy'] = ocp_proxy
        else:
            # Clear any previously set proxies if running locally
            os.environ.pop('http_proxy', None)
            os.environ.pop('https_proxy', None)

        jwk_client = PyJWKClient(oauth_jwks)
        signing_key = jwk_client.get_signing_key_from_jwt(bearer_token)

        if ocp_proxy:
            os.environ.pop('http_proxy', None)
            os.environ.pop('https_proxy', None)

        payload = jwt.decode(bearer_token, signing_key.key,
                             algorithms=["RS256"])
        now = int(time.time())

        # print(f"\n\nDEBUG - payload claims: {payload} \n\n")

        # TODO - rework validation to handle multiple scopes
        if ocp_scope not in payload['capabilities']:
            logger.exception("Invalid scope")
            return False
        # TODO - validate new SATS requirements
        if payload['iss'] != iss:
            logger.error("Invalid issuer")
        #     return False
        if payload['exp'] < now:
            logger.exception("Token expired")
            return False
    except Exception as e:
        logger.exception(f"Unable to validate token: {e=}")
        raise Exception({"message": "Failed to validate bearer token"})

    return True
