import configparser
import fastapi
import json
import jwt
import logging
import logging.config
import os
import secrets
import time

from jwt import PyJWKClient
from fastapi import APIRouter, Request, HTTPException, status  # , Response
from fastapi.responses import RedirectResponse
from typing import Any

from src.auth.oauth import validate_bearer_token
from src.util.logging import get_request_log
from src.clients.retry_adapter import RetryAdapter
from src.clients import mongo
from src.clients.actived import ADClient

retry_adapter = RetryAdapter()
adc = ADClient()

env = os.environ.get('PLATFORMENV', 'local')
if env == 'local' or env == 'docker':
    ocp_proxy = None
else:
    ocp_proxy = os.environ.get('API_PROXY')

# Only set proxies if ocp_proxy is defined
proxies = {'https': ocp_proxy} if ocp_proxy else {}

config = configparser.ConfigParser()
config.read('config/auth.ini')

config_ref = configparser.ConfigParser()
config_ref.read('config/refer.ini')

logging.config.fileConfig('config/logging.conf',
                          disable_existing_loggers=False)
logger = logging.getLogger(__name__)

auth_router = APIRouter(prefix="/api/v1/auth",
                        tags=[],
                        responses={404: {"description": "404 Not Found!"}},
                        )

auth_endpoint = config.get('endpoints', 'auth')
jwks_endpoint = config.get('endpoints', 'jwks')
token_endpoint = config.get('endpoints', 'token')
graphapi_endpoint = config.get('endpoints', 'graph')
client_id = os.environ.get('oidc_client_id')
client_secret = os.environ.get('oidc_client_secret')
client_aud = os.environ.get('oidc_client_audience')
client_tid = os.environ.get('oidc_client_tid')

# if not client_id or not client_secret or not client_aud or not client_tid:
#     logger.fatal("Failed to set some of these properties: client_id, "
#                  "client_secret, client_aud, client_tid")
#     raise Exception("Required secrets not available: client_id, "
#                     "client_secret, client_aud, client_tid")

auto_client_id = os.environ.get('oidc_adauto_client_id')
auto_client_secret = os.environ.get('oidc_adauto_client_secret')
auto_client_aud = os.environ.get('oidc_adauto_client_id')

ui_endpoint = None
api_endpoint = None

if env == 'local' or env == 'docker' or env == 'concourse':
    ui_endpoint = config_ref.get('urls', 'UI-ENDPOINT-LOCAL')
    api_endpoint = config_ref.get('urls', 'API-ENDPOINT-LOCAL')
else:
    ui_endpoint = os.environ.get('UI_ENDPOINT')
    api_endpoint = os.environ.get('API_ENDPOINT')

if not ui_endpoint or not api_endpoint:
    msg = "Failed to set app endpoints, UI/API endpoints must be set at"\
        + "runtime"
    logger.exception(msg)
    raise Exception({"message": f"Error: {msg}"})


def is_authenticated(request, token, req_log=None):
    '''
        Validate Bearer Token for Machine-to-Machine auth (OAuth), or
        validate ID Token for SSO Auth (OIDC).
    '''
    if 'id_token' in request.cookies.keys():
        logger.info("validating id_token")
        if validate_token(request.cookies['id_token'], client_aud):
            return True
    if 'user-agent' not in request.headers.keys():
        if 'authorization' in request.headers.keys():
            logger.info("validating auth header")
            if validate_bearer_token(request.headers['authorization']):
                return True
    if token:
        logger.info("validating token")
        if validate_bearer_token(None, token):
            return True

    logger.error("Authentication validation failed, "
                 + f" Request: {req_log}")

    return False


credentials_exception = HTTPException(
    status_code=401,
    detail="Unable to validate token. Authentication required for access."
    + " Create an OAuth client and request access to service "
    + "'cp-api' here; https://websecdevportal.cable.comcast.com",
    headers={"WWW-Authenticate": "Bearer"},
)


def validate_auth(request, request_log):
    # if not is_authenticated(request, token):
    if not is_authenticated(request, None):
        logger.error("401 Unauthorized, request_id: "
                     + f"{request_log['request_id']}")
        raise credentials_exception


def user_has_required_role(request: Request,
                           tenant_id: str,
                           required_roles: list):
    _role = mongo.Role()
    has_role, role = _role.has_role_user(request, tenant_id)
    return has_role and role in required_roles


@auth_router.get("/auto_login", include_in_schema=False,
                 response_class=RedirectResponse)
async def handle_oidc_auto_login(request: Request,
                                 response: RedirectResponse) -> RedirectResponse:
    '''
        Init SSO login for automation
    '''
    request_log = await get_request_log(request)
    logger.debug(f"{request_log}")

    # TODO - need to test if client is already authenticated?

    callback_url = str(request.url)[:-10] + "auto_callback"
    state = secrets.token_urlsafe(nbytes=64)
    redirect_to_auth_url = auth_endpoint + f"/?client_id={auto_client_id}&resp"\
        + f"onse_type=code&redirect_uri={callback_url}&scope=openid profile "\
        + f"email group.readwrite.all&domain_hint=comcast.com&state={state}"

    domain = get_domain(request.scope['server'][0])
    redirect_to_authorize = RedirectResponse(url=redirect_to_auth_url,
                                             status_code=302)
    redirect_to_authorize.set_cookie(key="state", value=state, max_age=90,
                                     path="/", domain=domain,
                                     samesite="lax", secure=True,
                                     httponly=True)
    return redirect_to_authorize


@auth_router.get("/auto_callback", include_in_schema=False)
async def handle_oidc_auto_callback_return_token(request: Request):
    '''
        Complete SSO login for automation, which returns access and refresh
         tokens
    '''
    request_log = await get_request_log(request)
    logger.debug(f"{request_log}")

    if 'code' not in request.query_params.keys() or 'state' \
            not in request.query_params.keys():
        logger.error("SSO Login failed; query params returned by IDP did not"
                     + f" include 'code' or 'state'. {request_log}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    if 'state' not in request.cookies.keys():
        logger.error("SSO Login failed; 'state' cookie not included in reques"
                     + f"t to callback. {request_log}")

        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    code = request.query_params['code']
    state = request.query_params['state']
    state_cookie = request.cookies['state']
    url = str(request.url)
    redirect_uri = url[:url.find('auth') + 5] + "auto_callback"

    if state != state_cookie:
        logger.error("SSO Login failed; state validation failed. "
                     + f"{request_log}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    params = {"client_id": auto_client_id,
              "client_secret": auto_client_secret,
              "scope": "openid profile email group.readwrite.all "
              + "offline_access",
              "redirect_uri": redirect_uri,
              "grant_type": "authorization_code",
              "code": code}
    try:
        token_resp = retry_adapter.post(token_endpoint,
                                        data=params,
                                        proxies=proxies)
    except Exception as e:
        logger.exception(f"ADAUTO token request failed: {e=}")
        raise HTTPException(status_code=500,
                            detail="ADAUTO token request failed")

    r_json = json.loads(token_resp.text)
    access_token = r_json['access_token']
    refresh_token = r_json['refresh_token']

    return {"access_token": access_token, "refresh_token": refresh_token}


@auth_router.get("/login", response_class=RedirectResponse,
                 include_in_schema=False)
async def handle_oidc_login(request: Request,
                            response: RedirectResponse) -> RedirectResponse:
    '''
        Init SSO login
    '''
    request_log = await get_request_log(request)
    logger.debug(f"{request_log}")

    if is_authenticated(request, None):
        return RedirectResponse(ui_endpoint)
        # if request.headers.get('referer'):
        #     return RedirectResponse(request.headers.get('referer'))
        # TODO - how to use referer?

    callback_url = str(request.url)[:-5] + "callback"
    state = secrets.token_urlsafe(nbytes=64)
    redirect_to_auth_url = auth_endpoint + f"/?client_id={client_id}&response_t"\
        + f"ype=code&redirect_uri={callback_url}&scope=openid profile email g"\
        + f"roupmember.read.all&domain_hint=comcast.com&state={state}"

    # domain = get_domain(api_endpoint)
    domain = get_domain(request.scope['server'][0])

    logger.debug(f"SETTING DOMAIN FOR COOKIES: '{domain}'")

    redirect_to_authorize = RedirectResponse(url=redirect_to_auth_url,
                                             status_code=302)

    redirect_to_authorize.set_cookie(key="state", value=state, max_age=90,
                                     path="/", domain=domain,
                                     samesite="lax", secure=True,
                                     httponly=True)

    # logger.debug(f"HEADERS: {request.headers}")
    # referer = None
    # if request.headers.get('referer'):
    #     referer = request.headers.get('referer')
    # else:
    #     referer = str(request_log['url'])[:-10]+"docs"

    # # Set referer in cookie for redirection
    # redirect_to_authorize.set_cookie(key="login_referer", value=referer,
    #                                  max_age=120, path="/", domain=domain,
    #                                  samesite="strict", secure=True,
    #                                  httponly=True)

    return redirect_to_authorize


@auth_router.get("/callback", include_in_schema=False)
async def handle_oidc_callback(request: Request):
    '''
        Processes callback from OIDC provider, using auth code returned to
        request ID and Access Tokens, then user data/profile from Graph API.
    '''
    request_log = await get_request_log(request)
    logger.debug(f"{request_log}")

    if 'code' not in request.query_params.keys() or 'state' \
            not in request.query_params.keys():
        logger.error("SSO Login failed; query params returned by IDP did not"
                     + f" include 'code' or 'state'. {request_log}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    if 'state' not in request.cookies.keys():
        logger.error("SSO Login failed; 'state' cookie not included in reques"
                     + f"t to callback. {request_log}")

        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    code = request.query_params['code']
    state = request.query_params['state']
    state_cookie = request.cookies['state']
    url = str(request.url)
    redirect_uri = url[:url.find('auth') + 5] + "callback"

    # orig_referer = request.cookies['login_referer'] if 'login_referer' in\
    #     request.cookies.keys() else None
    # if not orig_referer:
    #     orig_referer = api_endpoint[:-10]+"graphql" if 'user-agent' not\
    #         in request.headers.keys() else ui_referer
    # # TODO ^^^ rework

    if state != state_cookie:
        logger.error("SSO Login failed; state validation failed. "
                     + f"{request_log}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST)

    params = {"client_id": client_id,
              "client_secret": client_secret,
              "scope": "openid profile email groupmember.read.all",
              "redirect_uri": redirect_uri,
              "grant_type": "authorization_code",
              "code": code}
    try:
        token_resp = retry_adapter.post(token_endpoint,
                                        data=params,
                                        proxies=proxies)

    except Exception as e:
        logger.exception(f"Token request failed: {e=}")
        raise HTTPException(status_code=500,
                            detail="Token request failed")

    r_json = json.loads(token_resp.text)

    # if id token valid, use access token for request to MS Graph API to
    # retrieve user info / profile pic / groups, then redirect back to
    # original referrer, storing user data in cookies

    id_token = r_json['id_token']
    validated_id, id_payload = \
        validate_token(id_token, client_aud, return_payload=True)
    if not validated_id:
        logger.error(f"Failed to validate ID Token. {request_log}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST,
                            detail="Bad Request, invalid ID Token")

    access_token = r_json['access_token']

    # -----------------------------------------------------------
    # TODO - once Data Layer has been set up, we can start using this data
    #  but it needs to be wrapped in a JWT, until this is ready to work on
    #  commenting out this entire block of code, and 'user-profile' data
    #  will be limited to only claims in the ID token
    # profile_req_str = \
    #  "me?$select=givenName,surname,mail,id,jobTitle,extensio"\
    #  + "n_2df177c157954ecda2562da9ab70b764_ComcastGUID,extension_2df177c1"\
    #  + "57954ecda2562da9ab70b764_employeeType,extension_2df177c157954ecda"\
    #  + "2562da9ab70b764_ComcastEmployeeStatus,jobTitle,extension_2df177c1"\
    #  + "57954ecda2562da9ab70b764_ComcastJobCode,extension_2df177c157954ec"\
    #  + "da2562da9ab70b764_ComcastJobLevel,extension_2df177c157954ecda2562"\
    #  + "da9ab70b764_ComcastJobFunction,extension_2df177c157954ecda2562da9"\
    #  + "ab70b764_ComcastJobFamily,postalCode,officeLocation,extension_2df"\
    #  + "177c157954ecda2562da9ab70b764_ComcastWorkLocation,companyName,dep"\
    #  + "artment,extension_2df177c157954ecda2562da9ab70b764_ComcastOrg1,ex"\
    #  + "tension_2df177c157954ecda2562da9ab70b764_ComcastOrg2,extension_2d"\
    #  + "f177c157954ecda2562da9ab70b764_ComcastOrg3"
    # try:
    #    me_resp = retry_handler.get(graphapi_endpoint+profile_req_str,
    #                                headers={"Authorization":
    #                                         f"Bearer {access_token}"})
    # except Exception as e:
    #    logger.debug(f"Failed to retrieve org extensions for user: {e=}")

    # Graph API request returns custom Comcast extensions along with name,
    # email, id, jobTitle, postalCode, companyName and department

    # if me_resp.status_code != 200:
    #    # If first calls fails, we assume user is a contractor, not all custom
    #    # extensions are set in AD for contractors
    #  profile_req_str = "me?$select=givenName,surname,mail,id,displayName,e"\
    #      + "xtension_2df177c157954ecda2562da9ab70b764_ComcastGUID,postalCo"\
    #      + "de,state,streetAddress,extension_2df177c157954ecda2562da9ab70b"\
    #      + "764_ComcastEmployeeStatus,extension_2df177c157954ecda2562da9ab"\
    #      + "70b764_employeeType,extension_2df177c157954ecda2562da9ab70b764"\
    #      + "_ComcastWorkLocation"
    #    try:
    #        me_resp = retry_handler.get(graphapi_endpoint+profile_req_str,
    #                                    headers={"Authorization":
    #                                             f"Bearer {access_token}"})
    #    except Exception as e:
    #        logger.error(f"Failed to retrieve user props: {e=}")

    # if me_resp.status_code != 200:
    #    logger.error("Failed to retrieve user properties from Graph API"
    #                 + f".  User info; {id_payload['email']}."
    #                 + f" {request_log}")

    # user_props = json.loads(me_resp.text)

    # TODO - end code block w additional user data
    # -------------------------------------------------------------

    # if user_props is not None:
    #    user_name = f"{user_props['givenName']} {user_props['surname']}"
    #    new_props = {'name': user_name}
    #    for prop in user_props.keys():
    #        if 'data.context' in prop:
    #            continue
    #        if 'extension_2df177' in prop:
    #            if 'Comcast' in prop:
    #                new_props[prop[50:]] = user_props[prop]
    #            else:
    #                new_props[prop[43:]] = user_props[prop]
    #        else:
    #            new_props[prop] = user_props[prop].replace(',', ' - ')\
    #                if user_props[prop] is not None else None
    #    user_props = new_props

    user_props = {}
    id_claims = id_payload
    for prop in id_claims.keys():
        if prop in ['email', 'name', 'oid', 'onpremisessamaccountname']:
            if prop == 'name':
                name = id_claims[prop]
                user_props[prop] = name[name.find(',') + 2:] + ' ' + \
                    name[:name.find(',')]
            else:
                user_props[prop] = id_claims[prop]

    # redirect_to_referer = RedirectResponse(url=orig_referer, status_code=302)
    redirect_to_ui = RedirectResponse(url=ui_endpoint, status_code=302)

    domain = get_domain(str(request.url)) if "localhost" not in\
        str(request.url) else "localhost"
    time_to_expire = id_payload['exp'] - int(time.time())

    redirect_to_ui.set_cookie(key="id_token", value=id_token,
                              max_age=time_to_expire, path="/",
                              domain=domain, samesite="strict",
                              secure=True, httponly=True)

    redirect_to_ui.set_cookie(key="access_token", value=access_token,
                              max_age=time_to_expire, path="/",
                              domain=domain, samesite="strict",
                              secure=True, httponly=True)

    # user_props_value = '' if user_props is None else \
    #     str(user_props)[1:-1].replace(', ', '|').replace("'", '')

    # redirect_to_ui.set_cookie(key="user_props", value=user_props_value,
    #                          max_age=time_to_expire, path="/",
    #                          domain=domain, samesite="strict",
    #                          secure=True, httponly=True)

    # TODO - review this block and remove if not needed for data
    #  layer implementation

    # Due to character restrictions in cookie values, delimiter between props
    # is '|', delimiter between key/value is ': ', and other characters are
    # removed since resulting string cannot be directly converted to object

    # store names of groups user is member of, refresh data after 10 minutes
    # redirect_to_referer.set_cookie(key="users_groups",
    #                              value=users_groups_value,max_age=600,
    #                              path="/", domain=domain, samesite="strict",
    #                              secure=True, httponly=True)
    # TODO - need to apply transformation to store data in cookies due to
    # illegal characters in group id, name or desc

    # TODO - configure background refresh call in UI based on expiration of
    #        non-http cookies
    # TODO - configure token refresh endpoint

    return redirect_to_ui


@auth_router.get("/get_status_claims", tags=["auth"])
async def get_auth_status_claims(request: Request):
    '''
       Get authentication status and return user claims
    '''
    request_log = await get_request_log(request)
    logger.debug(f"{request_log}")
    return _get_auth_status_claims(request, request_log)


def _get_auth_status_claims(request: Request,
                            request_log: Any):
    if 'id_token' not in request.cookies.keys():
        logger.error(f"User is not authenticated:  {request_log}")
        return {"status": "Not authenticated"}

    valid, claims = validate_token(request.cookies['id_token'],
                                   client_aud,
                                   return_payload=True)

    if not valid:
        logger.error(f"User is not authenticated:  {request_log}")
        return {"status": "Not authenticated"}

    name = f"{claims['givenname']} {claims['surname']}"
    email = claims['email']
    expires = claims['exp'] - int(time.time())

    user_props = {"status": "Authenticated", "expireTime": expires,
                  "name": name, "email": email,
                  "ntid": claims['onpremisessamaccountname'],
                  "oid": claims['oid']}

    # This cookie is only for UI, API does not read after set, only ID token
    # user_claims = request.cookies['user_props']

    # if user_claims != '':
    #    for d in user_claims.split("|"):
    #        key, val = d.split(":")
    #        key = "guid" if key == "GUID" else key
    #        key = "email" if key == "mail" else key
    #        key = key[0].lower() + key[1:]
    #        val = val[1:].replace(" - ", ",")
    #        user_props[key] = val

    # TODO - when Data Layer is ready, store 'user_claims' (user properties
    # pulled from graph 'me' endpoint in JWT, then we can read them here,
    # can't store these plaintext

    return user_props


# TODO - keep route here, but migrate helper _get_user_ad_groups to
#  actived module
@auth_router.get("/get_ocp_groups", tags=["auth"])
async def get_user_ad_groups(request: Request,
                             userOnly: bool = False):
    # response: Response,
    # refresh: bool):
    '''
       Return all OCP AD groups, or only OCP AD groups user is a member of
    '''
    request_log = await get_request_log(request)
    logger.debug(f"{request_log}")
    if not is_authenticated(request, None):
        logger.error("401 Unauthorized, request_id: "
                     + f"{request_log['request_id']}")
        raise credentials_exception

    return _get_user_ad_groups(request, userOnly)


def _get_user_ad_groups(request: Request,
                        userOnly: bool,
                        user_oid: str = None):
    # logger.debug(f"{request_log}")
    # TODO - rework this, we want to use existing request_log
    # if available, need refactor due to number of existing calls
    # to this method

    if 'id_token' in request.cookies.keys() and not user_oid:
        # only checking id token when call comes from oauth method,
        # 'get_tenant_membership', worth refactor?
        valid, claims = validate_token(request.cookies['id_token'],
                                       client_aud,
                                       return_payload=True)

    users_groups = None

    if 'access_token' not in request.cookies.keys() and not user_oid:
        logger.exception("Access token not available to request users"
                         + " groups")
        raise HTTPException(status_code=500,
                            detail="Failed to retrieve users groups")
    else:
        access_token = None
        if not user_oid:
            access_token = request.cookies['access_token']
        else:
            # get service account access token for GraphAPI calls
            access_token = adc.get_service_principal_token()

        headers = {"Authorization":
                   f"Bearer {access_token}",
                   "ConsistencyLevel": "eventual"}
        if userOnly:
            oid = None
            if not user_oid:
                oid = claims['oid']
            else:
                oid = user_oid
            query = \
                f"users/{oid}/transitiveMemberOf/"\
                + "microsoft.graph.group"
        else:
            query = "groups"

        query += "?$count=true&$orderby=displayName"\
            + "&$filter=startswith(description,'OCP Tenant')"
        try:
            adg_resp = retry_adapter.get(graphapi_endpoint + query,
                                         headers=headers, proxies=proxies)
        except Exception as e:
            logger.exception("Failed to retrieve groups user is a member of: "
                             f"{e=}")  # - {request_log}")
            if '404' in str(e):
                raise HTTPException(detail="Failed to retrieve user groups"
                                    ", user or groups not found: 404",
                                    status_code=404)
            raise HTTPException(detail="Failed to retrieve user groups",
                                status_code=500)

        adg_data = json.loads(adg_resp.text)
        users_groups = []
        for g in adg_data['value']:
            users_groups.append({'oid': g['id'], 'name': g['displayName'],
                                 'description': g['description'] if
                                 g['description'] else ""})

        # Test for membership in admin group
        if not user_oid:
            query = \
                "me/memberOf?$filter=id eq "\
                + "'ef68420f-92d7-4837-9ef5-c04d67444523'"
        else:
            query = \
                f"users/{user_oid}/memberOf?$filter=id eq "\
                + "'ef68420f-92d7-4837-9ef5-c04d67444523'"
        found = False
        try:
            resp = retry_adapter.get(graphapi_endpoint + query,
                                     headers=headers, proxies=proxies)
        except Exception as e:
            if "404" in str(e):
                pass
            else:
                logger.error("Failed to test admin group membership for "
                             + f"user: {e=}")
        else:
            found = True

        if found:
            r = resp.json()['value'][0]
            users_groups.append({'oid': r['id'], 'name': r['displayName'],
                                 'description': r['description'] if
                                 r['description'] else ""})

        # users_groups_value = '' if users_groups is None else
        #    str(users_groups)[1:-1].replace(', ','|').replace("'",'')
        # response.set_cookie(key="users_groups", value=users_groups_value,
        #    max_age=600, path="/", domain=domain, samesite="strict",
        #    secure=True, httponly=True)

        # TODO - additonal transformation required to prevent illegal chars
        # from ending up in cookie value

    # if not users_groups:
    #    users_groups = request.cookies["users_groups"]

    # users_groups_obj = []
    # for g in users_groups.split("||||"):
    #    group = {}
    #    for d in g.split("|"):
    #        key, val = d.split(":")
    #        val = val[1:]
    #        group[key] = val
    #    users_groups_obj.append(group)

    return users_groups


def is_user_admin(request: Request):
    '''
       Test if user identified by session is a member of the O11Y
       admin group, if user is an admin return True, else False
    '''
    user_id = _get_auth_status_claims(request, "null")['ntid']
    # Current admin group in use is 'CHQ - MELT'
    admin_group_oid = "99fcef6a-6cbb-4bff-a804-d572830f5cf7"
    members = adc.get_ad_group_members(admin_group_oid)
    if user_id not in members:
        return False
    else:
        return True


@auth_router.get("/get_tenant_membership", tags=["auth"])
async def get_tenant_membership(request: Request,
                                tenant_slug: str,
                                user_oid: str):
    '''
        Return bool indicating if user is member of Tenant as well
        as role user has on Tenant

        This method only authenticates via OAuth
    '''
    request_log = get_request_log(request)
    logger.debug(f"{request_log}")

    authenticated = False
    if 'authorization' in request.headers.keys():
        logger.info("validating auth header")
        if validate_bearer_token(request.headers['authorization']):
            authenticated = True

    if not authenticated:
        raise HTTPException(
            status_code=401,
            detail="Unable to validate token. Authentication required for "
                   "access. Create an OAuth client and request access to "
                   "service 'ocp-api' here: https://sat-ng.comcast.com",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return _get_tenant_membership(request, tenant_slug, user_oid)


def _get_tenant_membership(request: Request, tenant_slug: str, user_oid: str):
    '''
        Internal function to retrieve tenant membership information for a user
    '''
    _tenant = mongo.Tenant()
    tenant = _tenant.get_tenant_by_slug(tenant_slug)

    _role = mongo.Role()
    try:
        has_role, role = _role.has_role_user(request, str(tenant['_id']),
                                             user_oid=user_oid)
    except Exception as e:
        if isinstance(e, fastapi.exceptions.HTTPException)\
                and e.status_code == 404:
            raise HTTPException(
                detail="Failed to retrieve roles, user or groups not found",
                status_code=404
            )
        raise HTTPException(
            detail="Failed to retrieve membership",
            status_code=500
        )

    return {"has_role": has_role, "role": role}


def validate_token(token, aud, return_payload=False):
    # Set proxy for urllib to use in PyJWKClient requests
    if ocp_proxy:
        os.environ['http_proxy'] = ocp_proxy
        os.environ['https_proxy'] = ocp_proxy
    else:
        # Clear any previously set proxies if running locally
        os.environ.pop('http_proxy', None)
        os.environ.pop('https_proxy', None)

    try:
        jwk_client = PyJWKClient(jwks_endpoint)
        signing_key = jwk_client.get_signing_key_from_jwt(token)

        # Remove proxy settings immediately after the call
        if ocp_proxy:
            os.environ.pop('http_proxy', None)
            os.environ.pop('https_proxy', None)

        payload = jwt.decode(token, signing_key.key, algorithms=["RS256"],
                             audience=aud)
        now = int(time.time())

        if payload['iat'] > now or payload['nbf'] > now:
            logger.exception("Invalid token")
            return False, {} if return_payload else False
        if payload['exp'] < now:
            logger.exception("Token expired")
            return False, {} if return_payload else False
        if payload['tid'] != client_tid:
            logger.exception("Token tid claim invalid")
            return False, {} if return_payload else False
    except Exception as e:
        logger.exception(f"Unable to validate token: {e=}")
        if return_payload:
            return False, {}
        return False

    if return_payload:
        return True, payload
    return True


def get_domain(url):
    domain = None
    if ':' in url[8:]:
        domain = url[8:url[8:].find(':') + 8]
    else:
        domain = url[8:url[8:].find('/') + 8]

    return domain
