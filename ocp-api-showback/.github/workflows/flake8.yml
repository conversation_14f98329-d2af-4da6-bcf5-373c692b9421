name: Flake8

on:
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: comcast-ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9"]
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8
    - name: Analysing the code with flake8
      run: |
        flake8 src/ --ignore=W901,W503 --max-line-length=100 --count  --show-source --statistics
