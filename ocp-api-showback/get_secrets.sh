#!/bin/bash

set -e

# Export secrets from .env
if [[ $PLATFORMENV == 'local' || $PLATFORMENV == '' ]]; then
  while read line; do
    if [[ $line =~ ^# ]]; then
      continue
    fi
    if [[ $line == *"="* ]]; then
      export $(echo $line | cut -d'=' -f1)="$(echo $line | cut -d'=' -f2)"
      val="$(echo $line | cut -d'=' -f2)"
      echo "export $(echo $line | cut -d'=' -f1)=$val"
    fi
  done < .env
fi

# TODO - remove this from .env file - use metricsapi lower env instead
unset VAULT_TOKEN

# Export secrets from $VAULT_PATH (in .env)
export VAULT_ADDR=https://or.vault.comcast.com
TOKEN=`vault write auth/approle/login role_id=$VAULT_ROLE_ID secret_id=$VAULT_SECRET_ID |grep '^token '|tr -s ' '|cut -d' ' -f2`
login=`vault login $TOKEN`
v_secrets=`vault read -format=json kv2/data/$VAULT_PATH | jq '.data.data'`
# Always revoke token after use
vault token revoke -self > /dev/null
for key in $(echo $v_secrets | jq -r 'keys[]'); do
  value=$(echo $v_secrets | jq ".$key" | sed 's/"//g')
  #export ${key}=${val}
  echo "export $key='$value'"
done
